package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BillRuleDetailDTO.java
 * @Description 扣费规则表
 * @createTime 2021年05月07日 20:17:00
 */

@Data
public class BillRuleDetailDTO {

    @ApiModelProperty(value = "套餐集合")
    private List<PackageVO> packages;

    @ApiModelProperty(value = "计费编码")
    private String billContent;

    @ApiModelProperty(value = "price")
    private BigDecimal price;

}
