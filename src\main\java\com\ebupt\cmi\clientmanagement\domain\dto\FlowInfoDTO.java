package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/10 10:49
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlowInfoDTO {
    /**
     * ICCID
     */
    private String iccid;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 使用时间
     */
    private String useDate;

    /**
     * 使用国家
     */
    private String mcc;

    /**
     * 流量
     */
    private BigDecimal flowCount;

    /**
     * 货币种类
     */
    private String currencyCode;

    /**
     * 价格
     */
    private BigDecimal price;
}
