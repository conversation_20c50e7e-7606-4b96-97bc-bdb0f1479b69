package com.ebupt.cmi.clientmanagement.domain.dto.Context;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.dto.ChannelFlowDetailDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.Channel;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketingRebate;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsMarketingRate;
import com.ebupt.cmi.clientmanagement.mapper.ChannelMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsChannelMarketingRebateMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsMarketingRateMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.SimpleFormatter;
import java.util.stream.Collectors;

@Data
public class RecordChannelFlowContext {
    private final Date unlimitAmountDate;
    // 折扣比例
    private final Map<String, Integer> rate = new HashMap<>();

    //渠道商参与的营销活动扣款总金额
    private final Map<String, Map<Long, BigDecimal>> rebateAmount = new HashMap<>();

    // 渠道商a2z扣款总金额
    private final Map<String, BigDecimal> amount = new HashMap<>();

    //渠道商参加的a2z营销活动
    private final Map<String, List<CmsChannelMarketingRebate>> marketingRebate = new HashMap<>();

    //gtp话单可能的corpId
    private Map<String, String> corpIdMap = new HashMap<>();

    private ArrayList<Long> marketingRebateIdList = new ArrayList<>();

    //当前在处理的话单
    private ChannelFlowDetailDTO currentRecord;

    //当前在处理话单的营销活动扣款金额
    private BigDecimal retabateAmount;

    private CmsChannelMarketingRebateMapper marketingRebateMapper;

    private CmsMarketingRateMapper marketingRateMapper;

    private ChannelMapper channelMapper;

    private boolean isGtp;

    //话单日期
    private String date;

    //营销款日期
    private Date amountDate;

    private boolean poskpack;

    public RecordChannelFlowContext() {
        try {
            unlimitAmountDate = new SimpleDateFormat("yyyy-MM-dd").parse("2099-12-30");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param corpId
     * @param RebateId 返利表id
     * @param amount
     */
    public void increaseRebateAmount(String corpId, Long RebateId, BigDecimal amount) {
        Map<Long, BigDecimal> map = rebateAmount.get(corpId);
        if (map == null) {
            map = new HashMap<>();
            rebateAmount.put(corpId, map);
            map.put(RebateId, amount);
        } else {
            map.merge(RebateId, amount, BigDecimal::add);
        }
    }

    public void increaseAmount(String corpId, BigDecimal amount) {
        this.amount.merge(corpId, amount, BigDecimal::add);
    }

    public Integer getRate(String corpId, String mcc) {
        if (!rate.containsKey(corpId + mcc)) {
            CmsMarketingRate cmsMarketingRate = marketingRateMapper.selectOne(Wrappers.lambdaQuery(CmsMarketingRate.class)
                    .eq(CmsMarketingRate::getCorpId, corpId)
                    .eq(CmsMarketingRate::getMcc, mcc));
            if (cmsMarketingRate != null) {
                rate.put(corpId + mcc, cmsMarketingRate.getRate());
            }
        }

        return rate.getOrDefault(corpId + mcc, 0);
    }

    public List<CmsChannelMarketingRebate> getMarketingRebate(String corpId) {
        if (marketingRebate.containsKey(corpId)) {
            return marketingRebate.get(corpId);
        }
        List<CmsChannelMarketingRebate> cmsChannelMarketingRebates;

        cmsChannelMarketingRebates = marketingRebateMapper.selectList(Wrappers.lambdaQuery(CmsChannelMarketingRebate.class)
                .eq(CmsChannelMarketingRebate::getCorpId, corpId)
                .eq(CmsChannelMarketingRebate::getType, CmsChannelMarketingRebate.TypeEnum.A2Z.getType())
                .ge(CmsChannelMarketingRebate::getExpiryTime, getAmountDate())
                .gt(CmsChannelMarketingRebate::getRemainAmount, BigDecimal.ZERO)
                .orderByAsc(CmsChannelMarketingRebate::getExpiryTime));

        int deviation = 0;
        List<CmsChannelMarketingRebate> tmp = new ArrayList<>();
        for (int i = cmsChannelMarketingRebates.size() - 1; i >= 0; i--) {
            if (cmsChannelMarketingRebates.get(i).getExpiryTime().after(unlimitAmountDate)) {
                tmp.add(cmsChannelMarketingRebates.get(i));
            } else {
                tmp.add(cmsChannelMarketingRebates.get(deviation));
                deviation++;
            }
        }

        marketingRebate.put(corpId, tmp);

        return tmp;
    }

    private Date getAmountDate() {
        if (this.amountDate == null) {
            StringBuilder stringBuilder = new StringBuilder(date);
            stringBuilder.insert(6, "-");
            stringBuilder.insert(4, "-");
            if (isGtp) {
                stringBuilder.append(" 12:00:00");
            } else {
                stringBuilder.append(" 10:00:00");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            try {
                Date date = sdf.parse(stringBuilder.toString());
                Calendar instance = Calendar.getInstance();
                instance.setTime(date);
                instance.add(Calendar.DAY_OF_MONTH, 1);
                this.amountDate = instance.getTime();
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return this.amountDate;
    }

    public String getCorpId(String ebsCode) {
        if (corpIdMap.containsKey(ebsCode)) return corpIdMap.get(ebsCode);

        Channel channel = channelMapper.selectOne(Wrappers.lambdaQuery(Channel.class)
                .select(Channel::getCorpId)
                .eq(Channel::getEbsCode, ebsCode));
        if (channel == null) {
            return null;
        }

        corpIdMap.put(ebsCode, channel.getCorpId());
        return channel.getCorpId();
    }
}
