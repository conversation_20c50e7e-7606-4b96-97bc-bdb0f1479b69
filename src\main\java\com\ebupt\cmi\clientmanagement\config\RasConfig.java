package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
/**
 * YmlConfig
 * 读取配置
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/5/20 13:07
 */
@Data
@Component
@ConfigurationProperties(prefix = "rsa")
public class RasConfig {
    private String privateKey;
}
