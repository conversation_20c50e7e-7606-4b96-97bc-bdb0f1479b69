package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * DailyTrafficDTO
 *
 * @Author: <PERSON><PERSON>qi<PERSON>kun
 * @Date: 2021/5/20 15:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DailyTraffic {
    /**
     * 日期：ps:2018-07-07；
     */
    @NotBlank
    private String date;
    /**
     * 套餐当天使用的流量；
     */
    @NotBlank
    private BigDecimal traffic;
}
