package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.CertificatesType;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.H5RealNameAuthVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exception.NotRollBackBizException;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.service.OutSideApiRealNameSysService;
import com.ebupt.cmi.clientmanagement.utils.ImageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping
@RefreshScope
@Api(tags = "实名制外部API")
public class OutSideApiRealNameSysController {

    public static final long maxFileSize = 10 * 1024 * 1024;
    public static final long longestEdgePix = 4096;
    public static final long shortestEdgePix = 15;

    @Autowired
    OutSideApiRealNameSysService outSideApiRealNameSysService;

    @Autowired
    RedissonLock redissonLock;
    @Autowired
    private PmsFeignClient pmsFeignClient;

    @Value("${rule-code-mcc.map}")
    private String mccMappingConfig;

    @ApiOperation(value = "H5实名认证接口", notes = "H5实名认证接口")
    @ApiResponses({
            @ApiResponse(code = 0000, message = "成功"),
            @ApiResponse(code = 1000, message = "服务器内部错误，请稍候再试")
    })
    @PostMapping(value = "/aep/SBO_auth_realName/v1", headers = "content-type=multipart/form-data")
    public Response h5RealNameAuthentication(@Validated H5RealNameAuthVO h5RealNameAuthVO) {
        //参数校验
        if (!h5RealNameAuthVO.getIsSimple() && h5RealNameAuthVO.getImgFile() == null){
            throw new BizException("证件图片不能为空","1000");
        }

        if (StrUtil.isNotBlank(h5RealNameAuthVO.getOrderID()) && StrUtil.isBlank(h5RealNameAuthVO.getIccid())){
            throw new BizException(ApiResponseEnum.REALNAME_AUTH_ONLY_ICCID);
        }


        checkParams(h5RealNameAuthVO);
        if (h5RealNameAuthVO.getIsSimple()) {
            checkSimpleParams(h5RealNameAuthVO);
        }
        String lockName = splicingLockName(h5RealNameAuthVO);
        final boolean lock = redissonLock.tryLock(lockName);
        if (!lock) {
            log.warn("获取分布式锁失败 orderUniqueId/iccid: {}", lockName);
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_REPEATED);
        }

        try {
            //证件图片校验

            Map<String,String> mccMapping = new HashMap<>();

            String[] mappings = mccMappingConfig.split(",");
            for (String mapping : mappings) {
                String[] parts = mapping.split(":");
                if (parts.length == 2) {
                    mccMapping.put(parts[0].trim(), parts[1].trim());
                }
            }

            if (mccMapping.containsKey(h5RealNameAuthVO.getRuleCode())){
                String mcc = mccMapping.get(h5RealNameAuthVO.getRuleCode());
                h5RealNameAuthVO.setRuleCode(mcc);
            }

            String ruleCode = Response.getAndCheckRemoteData(pmsFeignClient.getRuleCodeByICCIDAndMcc(h5RealNameAuthVO.getIccid(),h5RealNameAuthVO.getRuleCode()));

            if (StrUtil.isBlank(ruleCode)){
                throw new BizException(ApiResponseEnum.RULE_CODE_NOT_FOUND);
            }

            h5RealNameAuthVO.setRuleCode(ruleCode);

            if (h5RealNameAuthVO.getImgFile() != null){
                log.info("证件图片不为空保存图片");
                long length = h5RealNameAuthVO.getImgFile().getBytes().length;
                File file = ImageUtils.multiToFile2(h5RealNameAuthVO.getImgFile());
                checkFileForH5RealNameAuth(file, length);
                return outSideApiRealNameSysService.h5RealNameAuthentication(h5RealNameAuthVO, file);
            }else {
                log.info("证件图片为空不保存图片");
                return outSideApiRealNameSysService.h5RealNameAuthentication(h5RealNameAuthVO, null);

            }

        } catch (BizException | NotRollBackBizException ex) {
            throw ex;
        } catch (Exception e) {
            log.error("H5实名认证失败", e);
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_FAILED);
        } finally {
            //如果该线程还持有该锁，那么释放该锁。如果该线程不持有该锁，说明该线程的锁已到过期时间，自动释放锁
            if (redissonLock.isHeldByCurrentThread(lockName)) {
                redissonLock.unlock(lockName);
            }
        }
    }

    /**
     * 简易流程参数校验
     *
     * @param vo 入参
     */
    private void checkSimpleParams(H5RealNameAuthVO vo) {
        // 过期时间格式校验
        if (!dateStrIsValid(vo.getExpireDate(), "yyyyMMdd")) {
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_EXPIRE_DATE);
        }
        // 护照类型判断：香港护照判断，country_code为CHN且"证件ID": "K和H开头的；BNO护照判断逻辑；若是，则返回错误信息（护照类型不符合要求）
        //护照类型判断
//        if (CertificatesType.PASSPORT.getType().equals(vo.getCertificatesType())) {
//            log.debug("【简易认证】护照类型证件，现进行输入国家码判断");
//            //当护照国家码为“香港”，返回认证失败
//            //香港护照判断逻辑：country_code为CHN且"certificatesId": "K和H开头的
//            String country = vo.getPassportCountry();
//            String number = vo.getCertificatesId();
//            if ("CHN".equals(country.toUpperCase()) &&
//                    (number.toUpperCase().startsWith("K") || number.toUpperCase().startsWith("H"))) {
//                //护照类型不符合要求
//                throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PASSPORT_TYPE_ERROR);
//            }
//        }

        // 证件有效期判断：当前时间>过期时间，若是，则返回错误信息
        log.debug("【简易认证】根据输入过期时间，现进行证件有效期判断");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.parse(vo.getExpireDate()));
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        if (new Date().compareTo(calendar.getTime()) >= 0) {
            // "证件已过期";
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_THE_CERTIFICATE_HAS_EXPIRED);
        }

        // 未满16周岁判断：当前时间<出生年月+16年+1天，若是，则返回错误信息（该证件未满16周岁）
        log.debug("【简易认证】根据输入出生时间，现进行16周岁判断");
        Calendar cal = Calendar.getInstance();
        cal.setTime(DateUtil.parse(vo.getDateOfBirth()));
        cal.add(Calendar.YEAR, 16);
        cal.add(Calendar.DAY_OF_MONTH, 1);
        if (new Date().compareTo(cal.getTime()) < 0) {
            // "未满16周岁";
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_UNDER_16_YEARS_OLD);
        }
    }

    /**
     * 拼接redis锁key
     * @param h5RealNameAuthVO
     * @return
     */
    private String splicingLockName(H5RealNameAuthVO h5RealNameAuthVO){
        String authRealName = "Auth_RealName_";
        if(h5RealNameAuthVO.getIccid() != null){
            return authRealName.concat(h5RealNameAuthVO.getIccid());
        }
        return authRealName.concat(h5RealNameAuthVO.getOrderID());
    }

    /**
     * 参数校验
     * @param h5RealNameAuthVO
     */
    private void checkParams(H5RealNameAuthVO h5RealNameAuthVO) {
        if (StrUtil.isBlank(h5RealNameAuthVO.getIccid())) {
            h5RealNameAuthVO.setIccid(null);
        }
        if (StrUtil.isBlank(h5RealNameAuthVO.getOrderID())) {
            h5RealNameAuthVO.setOrderID(null);
        }
        if (StrUtil.isBlank(h5RealNameAuthVO.getPhoneNumber())) {
            h5RealNameAuthVO.setPhoneNumber(null);
        }
        if (StrUtil.isBlank(h5RealNameAuthVO.getEmail())) {
            h5RealNameAuthVO.setEmail(null);
        }
        if (StrUtil.isBlank(h5RealNameAuthVO.getPassportCountry())) {
            h5RealNameAuthVO.setPassportCountry(null);
        }
        if (StrUtil.isBlank(h5RealNameAuthVO.getDateOfBirth())) {
            h5RealNameAuthVO.setDateOfBirth(null);
        }
        if (StrUtil.isBlank(h5RealNameAuthVO.getInputNameCh())) {
            h5RealNameAuthVO.setInputNameCh(null);
        }
//        //名字校验
//        if (StrUtil.isBlank(h5RealNameAuthVO.getInputName())) {
//            throw new BizException("姓名（中英文自动转换）不能为空");
//        }
        //ICCID、OrderId校验
        if (h5RealNameAuthVO.getIccid() == null && h5RealNameAuthVO.getOrderID() == null) {
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_ICCID_ORDERID);
        }
        //手机号、邮箱校验
        if (h5RealNameAuthVO.getPhoneNumber() == null && h5RealNameAuthVO.getEmail() == null) {
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_PHONE_EMAIL);
        }
        //护照国家校验
        if (CertificatesType.PASSPORT.getType().equals(h5RealNameAuthVO.getCertificatesType()) && h5RealNameAuthVO.getPassportCountry() == null) {
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_COUNTRY);
        }
        //旧ID和旧类型成对出现
        if ((h5RealNameAuthVO.getOCertificatesType() != null && h5RealNameAuthVO.getOCertificatesId() == null)
                || (h5RealNameAuthVO.getOCertificatesType() == null && h5RealNameAuthVO.getOCertificatesId() != null)
        ) {
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_OLD_TYPE_ID);
        }
        if (StrUtil.isNotBlank(h5RealNameAuthVO.getDateOfBirth()) && !dateStrIsValid(h5RealNameAuthVO.getDateOfBirth(), "yyyyMMdd")) {
            throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_BIRTH);
        }
    }

    /**
     * 证件图片校验
     * 证件图片仅支持JPG/PNG
     * 证件图片尺寸不小于15×15像素，最长边不超过4096像素
     * 证件图片文件大小不超过10MB
     *
     * @param ff
     */
    public void checkFileForH5RealNameAuth(File ff, Long len) {
        try {
            //检查图片类型
            if (ImageUtils.getPicType(new FileInputStream(ff)) == null) {
                throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_FILE_TYPE);
            }
            //检查图片的像素大小
            if (!ImageUtils.checkImageScale(ff, longestEdgePix, shortestEdgePix)) {
                throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_FILE_PIXEL);
            }
            //检查图片大小
            if (!ImageUtils.checkFileSize(len, maxFileSize)) {
                throw new BizException(ApiResponseEnum.AUTH_REAL_NAME_PARAMS_FILE_SIZE);
            }
        } catch (IOException e) {
            log.error("IO异常 ====> : ", e);
        }
    }

    /**
     * 验证字符串是否为指定日期格式
     *
     * @param rawDateStr 待验证字符串
     * @param pattern    日期字符串格式, 例如 "yyyyMMdd"
     * @return 有效性结果, true 为正确, false 为错误
     */
    public boolean dateStrIsValid(String rawDateStr, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        Date date = null;
        try {
            // 转化为 Date类型测试判断
            date = dateFormat.parse(rawDateStr);
            return rawDateStr.equals(dateFormat.format(date));
        } catch (ParseException e) {
            log.error("Illegal date string! Exception occurred: {}", e.getMessage(), e);
            return false;
        }

    }
}
