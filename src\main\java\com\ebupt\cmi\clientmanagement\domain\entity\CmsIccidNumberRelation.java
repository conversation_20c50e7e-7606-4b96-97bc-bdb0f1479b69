package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsIccidNumberRelation对象", description="")
@Builder
public class CmsIccidNumberRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "Iccid （cmi资源")
    private String iccid;

    @ApiModelProperty(value = "国内手机号码")
    private String cmccNumber;

    @ApiModelProperty(value = "国内省份")
    private String cmccNumberProvince;

    @ApiModelProperty(value = "国内市区")
    private String cmccNumberCity;

    @ApiModelProperty(value = "订单唯一id")
    private String orderUniqueId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


}
