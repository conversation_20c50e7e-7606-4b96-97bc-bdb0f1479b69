package com.ebupt.cmi.clientmanagement.domain.entity;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * (CmsFlowpoolQuota)实体类
 *
 * <AUTHOR>
 * @since 2024-04-08 14:35:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsFlowpoolQuota{

    private Long id;
/**
     * 归属的流量池唯一id
     */
    private String flowUniqueId;
/**
     * 已分配配额
     */
    private Long quotaFlow;
/**
     * 过期时间
     */
    private String expireTime;

}

