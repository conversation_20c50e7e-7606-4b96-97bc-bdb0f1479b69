package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="CmsDelayMessageRecord对象", description="")
public class CmsDelayMessageRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId()
    private Long id;

    @ApiModelProperty(value = "队列名称")
    private String queueName;

    @ApiModelProperty(value = "json消息体")
    private String message;

    @ApiModelProperty(value = "消费时间")
    private Date consumeTime;

    @ApiModelProperty(value = "创建时间默认值：CURRENT_TIMESTAMP")
    private Date createTime;

    //0: 不需要， 1: 需要
    private String scheduledCompensation;

    public CmsDelayMessageRecord(Long id, String queueName, String message, Date consumeTime, Date createTime) {
        this.id = id;
        this.queueName = queueName;
        this.message = message;
        this.consumeTime = consumeTime;
        this.createTime = createTime;
    }
}
