# 渠道新增接口分析报告

## 一、接口功能描述与业务目标

**接口路径**：`POST /channel/newChannel`

**功能描述**：
- 用于新增渠道商（客户），实现渠道商基础信息、合作模式、套餐组、账户、通知等多维度的初始化配置。
- 支持多种合作模式（如A2Z、资源合作、自建套餐等），并根据业务规则进行参数校验和数据落库。
- 业务目标是实现渠道商全生命周期的起始环节，确保数据一致性、合规性和后续业务可扩展性。

## 二、详细调用流程

### 1. 接口入口
- `ChannelController#newChannel(NewChannelVO)`（控制器/渠道新增接口，参数：NewChannelVO-渠道新增参数对象）
  - 接收前端传入的`NewChannelVO`（渠道新增参数对象）参数，调用`channelService.newChannel(newChannelVO)`（渠道服务-新增渠道方法）。
  - 异常捕获，失败时返回错误信息。

### 2. 参数结构与校验
- `NewChannelVO`（渠道新增参数对象）：包含如下主要字段：
  - `corpName`（厂商名称）、`channelStatus`（渠道商状态）、`ebsCode`（EBS编码）、`deposit`（押金金额）、`currencyCode`（货币种类）、`mail`（联系人邮箱）、`packageInfos`（可购套餐组）、`channelCooperationMode`（渠道合作模式）、`activateNotificationUrl`（激活通知URL）、`contractBeginTime`（合约开始时间）、`contractEndTime`（合约结束时间）、`createAccountNumber`（创建账户数量）、`depositNotify`（可用金额提醒阈值）、`channelType`（渠道商类型）等。
  - 关键字段带有`@NotBlank`等校验注解，部分字段为必填。
- 业务校验：
  - `allowUpdateCheck(UpdateChannelVO, null, true)`（参数校验方法）：
    - 校验激活通知、esim通知URL必填性。
    - 校验渠道商名称、公司名称不能包含特殊字符。
    - 校验渠道商名称、EBS CODE唯一性。

### 3. 业务主流程
- `ChannelServiceImpl#newChannel(NewChannelVO)`（渠道服务实现-新增渠道方法）
  1. **参数转换**：`BeanUtils.copyProperties(newChannelVO, updateChannelVO)`（属性拷贝，VO转为更新对象）
  2. **校验**：调用`allowUpdateCheck`进行业务参数校验。
  3. **生成主键**：生成渠道商唯一ID（UUID）。
  4. **定向应用处理**：
     - 若有定向应用，且合作模式包含A2Z且允许自建套餐，则写入`cms_channel_directional_relation`（定向应用关系表）和`cms_channel_directional_relation_auth`（定向应用关系授权表）。
     - 否则抛出业务异常。
  5. **套餐组校验与绑定**：
     - 若合作模式不为纯资源合作，调用`filteratePackageGroup`（套餐组校验方法）校验套餐组与合作模式的匹配性。
     - 调用`insertChannelPackageRelation`（插入套餐组关系方法）批量插入套餐组与渠道商关系（`cms_channel_package_relation`（渠道-套餐组关系表）、`cms_channel_package_relation_auth`（渠道-套餐组关系授权表））。
  6. **渠道商主表写入**：
     - 插入`Channel`（渠道商实体类，表：`cms_channel`，字段如corp_id-渠道ID、corp_name-渠道名称、status-状态、type-类型、ebs_code-EBS编码、currency_code-货币种类、internal_order-内部订单、company_name-公司名称、address-地址、check_status-审核状态等），并同步写入`CmsChannelAuth`（渠道商授权表，`cms_channel_auth`）。
  7. **账户创建**：
     - 若需创建账户，调用`backFeignClient.createAccountForNewChannel`（远程账户服务-新渠道开户）远程创建。
  8. **渠道商详情写入**：
     - 构建`ChannelDistributorDetail`（渠道商详情实体类，表：`channel_distributors_detail`，字段如corp_id-渠道ID、is_sub-渠道商状态、app_key-应用key、app_secret-应用密钥、deposite_reset-押金重置、deposit-押金、deposite_remind_threshold-押金提醒阈值、currency_code-货币种类、discount-折扣、contract_start_time-合约开始时间、contract_end_time-合约结束时间、deposit_amount-合约销售金额、email-邮箱、account_num-账户数、channel_type-渠道类型、activate_notification-激活通知、unsubscribe_rule-退订规则、total_deposit-总额度、activate_notification_url-激活通知URL、allow_new_package-是否允许自建套餐、runoutof_balance_remind_threshold-余额用尽提醒、stop_use_remind_threshold-停止使用提醒、prohibitive_buy_remind_threshold-禁止购买提醒、channel_cooperation_mode-合作模式、package_use_percentage-套餐提醒阈值、overdue_notify-到期提醒、overdue_notify_url-到期提醒URL、package_use_notify_url-套餐使用通知URL、esim_notification-ESIM通知、esim_notification_url-ESIM通知URL、approval_package-审批套餐、a2z_contract_start_time-A2Z合约开始、a2z_contract_end_time-A2Z合约结束、sales_mail-销售邮箱、a2z_deposit_amount-A2Z押金、a2z_accounting_period_id-A2Z账期ID、distribution_accounting_period_id-分销账期ID、a2z_channel_type-A2Z渠道类型、is_sub_a2z-A2Z子渠道、resource_accounting_period_id-资源账期ID、resource_stop_use_remind_threshold-资源停止使用提醒、resource_prohibitive_buy_remind_threshold-资源禁止购买提醒、resource_runoutof_balance_remind_threshold-资源余额用尽提醒、resource_channel_type-资源渠道类型、marketing_amount-营销金额、credit_amount-授信金额、a2z_marketing_amount-A2Z营销金额等），插入`channel_distributors_detail`。
     - 若有自建套餐，写入`cms_channel_selfpackage_country_relation`（自建套餐-国家关系表）、`channel_upcctemplate_relation`（UPCC模板关系表）等。
  9. **A2Z运营商、套餐、规则等多表写入**：
     - 运营商信息写入`cms_channel_a2z_operator`（A2Z运营商表）。
     - IMSI、规则、短信模板等多表批量插入。
  10. **同步写入授权表**：
      - `CmsChannelDistributorsAuth`（渠道商详情授权表，`cms_channel_distributors_detail_auth`）。

### 4. 关键校验与数据一致性
- 所有写库操作均在`@Transactional`（Spring事务注解）事务下，异常自动回滚。
- 关键业务校验点：
  - 合作模式与套餐组、定向应用的强一致性校验。
  - 套餐组唯一性、套餐组内套餐唯一性校验。
  - 渠道商名称、EBS CODE唯一性。

### 5. 异常处理机制
- 业务异常统一抛出`BizException`（业务异常类），接口层捕获后返回错误信息。
- 数据库唯一约束冲突抛出`DuplicateKeyException`（唯一键冲突异常），转为业务异常。
- 其他异常统一转为"新增渠道商失败"。
- 所有异常均触发事务回滚，保证数据一致性。

## 三、核心类与方法清单（中英文对照）

- Controller层：
  - `ChannelController#newChannel(NewChannelVO)`（渠道控制器-新增渠道方法，参数：NewChannelVO-渠道新增参数对象）
- Service层：
  - `ChannelService#newChannel(NewChannelVO)`（渠道服务接口-新增渠道方法）
  - `ChannelServiceImpl#newChannel(NewChannelVO)`（渠道服务实现-新增渠道方法）
  - `allowUpdateCheck(UpdateChannelVO, ChannelDistributorDetail, boolean)`（参数校验方法）
  - `filteratePackageGroup(List<PackageInfo>, List<String>)`（套餐组校验方法）
  - `insertChannelPackageRelation(UpdateChannelVO, String, boolean, boolean)`（插入套餐组关系方法）
- VO/DTO/Entity：
  - `NewChannelVO`（渠道新增参数对象）、`UpdateChannelVO`（渠道更新参数对象）、`ChannelDistributorDetail`（渠道商详情实体）、`Channel`（渠道商实体）、`CmsChannelAuth`（渠道商授权实体）、`ChannelPackageRelation`（渠道-套餐组关系实体）、`CmsChannelPackageRelationAuth`（渠道-套餐组关系授权实体）、`CmsChannelSelfpackageCountryRelation`（自建套餐-国家关系实体）等
- Mapper/DAO：
  - `ChannelMapper`（渠道商表操作接口）、`ChannelDistributorDetailMapper`（渠道商详情表操作接口）、`ChannelPackageRelationMapper`（渠道-套餐组关系表操作接口）、`CmsChannelAuthMapper`（渠道商授权表操作接口）等
- 远程服务：
  - `backFeignClient.createAccountForNewChannel`（远程账户服务-新渠道开户）
  - `pmsFeignClient.getPackageGroupsByGroupIds`（远程套餐组服务-校验套餐组）、`pmsFeignClient.haveSamePackageInSameGroup`（远程套餐组服务-校验套餐组唯一性）

## 四、数据流转过程（表结构及字段中英文对照）

1. **前端请求**：提交`NewChannelVO`参数至`/channel/newChannel`。
2. **参数校验**：Spring Validation + 业务校验。
3. **主流程处理**：
   - 生成渠道ID，处理定向应用、套餐组、合作模式等。
   - 多表写入（主表、详情表、套餐组关系、授权表等）。
   - 远程服务调用（账户创建、套餐组校验等）。
4. **数据库表**：
   - `cms_channel`（渠道商主表）：
     - `corp_id`（渠道ID）、`corp_name`（渠道名称）、`status`（状态）、`type`（类型）、`ebs_code`（EBS编码）、`currency_code`（货币种类）、`internal_order`（内部订单）、`company_name`（公司名称）、`address`（地址）、`check_status`（审核状态）等
   - `cms_channel_auth`（渠道商授权表）：同上，带审核状态
   - `channel_distributors_detail`（渠道商详情表）：
     - `corp_id`（渠道ID）、`is_sub`（渠道商状态）、`app_key`（应用key）、`app_secret`（应用密钥）、`deposite_reset`（押金重置）、`deposit`（押金）、`deposite_remind_threshold`（押金提醒阈值）、`currency_code`（货币种类）、`discount`（折扣）、`contract_start_time`（合约开始时间）、`contract_end_time`（合约结束时间）、`deposit_amount`（合约销售金额）、`email`（邮箱）、`account_num`（账户数）、`channel_type`（渠道类型）、`activate_notification`（激活通知）、`unsubscribe_rule`（退订规则）、`total_deposit`（总额度）、`activate_notification_url`（激活通知URL）、`allow_new_package`（是否允许自建套餐）、`runoutof_balance_remind_threshold`（余额用尽提醒）、`stop_use_remind_threshold`（停止使用提醒）、`prohibitive_buy_remind_threshold`（禁止购买提醒）、`channel_cooperation_mode`（合作模式）、`package_use_percentage`（套餐提醒阈值）、`overdue_notify`（到期提醒）、`overdue_notify_url`（到期提醒URL）、`package_use_notify_url`（套餐使用通知URL）、`esim_notification`（ESIM通知）、`esim_notification_url`（ESIM通知URL）、`approval_package`（审批套餐）、`a2z_contract_start_time`（A2Z合约开始）、`a2z_contract_end_time`（A2Z合约结束）、`sales_mail`（销售邮箱）、`a2z_deposit_amount`（A2Z押金）、`a2z_accounting_period_id`（A2Z账期ID）、`distribution_accounting_period_id`（分销账期ID）、`a2z_channel_type`（A2Z渠道类型）、`is_sub_a2z`（A2Z子渠道）、`resource_accounting_period_id`（资源账期ID）、`resource_stop_use_remind_threshold`（资源停止使用提醒）、`resource_prohibitive_buy_remind_threshold`（资源禁止购买提醒）、`resource_runoutof_balance_remind_threshold`（资源余额用尽提醒）、`resource_channel_type`（资源渠道类型）、`marketing_amount`（营销金额）、`credit_amount`（授信金额）、`a2z_marketing_amount`（A2Z营销金额）等
   - `cms_channel_package_relation`（渠道-套餐组关系表）：
     - `corp_id`（渠道ID）、`group_id`（套餐组ID）、`group_name`（套餐组名称）、`cooperation_mode`（合作模式）、`is_channel_create`（是否自建套餐）等
   - `cms_channel_package_relation_auth`（渠道-套餐组关系授权表）：同上
   - `cms_channel_selfpackage_country_relation`（自建套餐-国家关系表）：
     - `corp_id`（渠道ID）、`group_id`（套餐组ID）
   - 其他如`cms_channel_a2z_operator`（A2Z运营商表）、`cms_channel_directional_relation`（定向应用关系表）、`cms_channel_directional_relation_auth`（定向应用关系授权表）等
5. **事务控制**：全流程事务包裹，异常自动回滚。

## 五、流程图

```mermaid
graph TD
    A[前端请求 /channel/newChannel] --> B[Controller 参数校验]
    B --> C[Service 业务校验 allowUpdateCheck]
    C --> D[生成渠道ID]
    D --> E{定向应用处理}
    E -- 合规 --> F[套餐组校验 filteratePackageGroup]
    F --> G[套餐组关系插入 insertChannelPackageRelation]
    G --> H[主表/详情表/授权表写入]
    H --> I[账户创建（远程）]
    H --> J[多表写入（A2Z/IMSI/规则/模板）]
    I & J --> K[事务提交]
    E -- 不合规 --> Z[异常返回]
    F -- 校验失败 --> Z
    G -- 唯一性冲突 --> Z
    H -- 其他异常 --> Z
```

## 六、总结与注意事项

- 该接口为渠道商全生命周期的起点，涉及多表、多业务规则，所有操作均需保证原子性。
- 业务规则复杂，需严格遵循参数校验、合作模式与套餐组一致性、唯一性约束等。
- 远程服务依赖需关注超时、失败等异常场景。
- 代码实现已充分利用Spring事务、异常机制，保证数据一致性和可维护性。

---

> 本文档基于实际代码实现自动分析生成，适用于团队学习、代码审查、业务梳理等场景。 