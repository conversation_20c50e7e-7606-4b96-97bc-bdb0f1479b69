package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.dto.common.DialectInfo;
import com.ebupt.cmi.clientmanagement.domain.dto.common.PriceInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/24 14:49
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserQueryPackageDTO {

    /**
     * 套餐包描述
     */
    private List<DialectInfo> bundleDesc;

    /**
     * 套餐包名称
     */
    private List<DialectInfo> name;

    /**
     * 套餐标识
     */
    private String dataBundleId;

    /**
     * 订购关系状态
     * 1：未激活
     * 2：已过期
     * 3：已激活
     * 99：已退款
     */
    private Integer status;

    /**
     * 订单ID
     */
    private String orderID;

    /**
     * 子订单标识
     */
    private String subscriptionKey;

    /**
     * 订购价格
     */
    private PriceInfo price;

    /**
     * 订购渠道
     */
    private String orderChannel;

    /**
     * 订购时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /**
     * 套餐过期时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 套餐激活时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date activeTime;

    /**
     * 套餐指定激活日期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonProperty(value = "setActiveTime")
    private Date activeAt;

    /**
     * 可购买加油包的周期数
     */
    @JsonProperty("remainderDays")
    String purchaseDays;

    /**
     * 是否支持加油包
     */
    String isSupportFuelpack;

    /**
     * 套餐类型（单日限量/周期内限量）
     */
    String packageType;

    /**
     * 套餐扣费模式
     */
    String deductionModel;

    /**
     * 渠道商合作模式
     */
    String cooperationMode;

    /**
     * 剩余可用流量
     */
    String remainFlow;

    /**
     * 剩余可用时间
     */
    String remainTime;
}
