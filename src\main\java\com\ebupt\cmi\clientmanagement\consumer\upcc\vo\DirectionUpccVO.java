package com.ebupt.cmi.clientmanagement.consumer.upcc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DirectionUpccVO {

    private String packageStatus;

    private String packageUniqueId;

    //已激活套餐的ccr_u消息。 只处理这个组里的应用签约
    //待激活套餐 || 已激活套餐的ccr_i消息，处理套餐附属所有应用签约，同时校验有无多余签约。
    private Set<String> appGroupId;

    private String hImsi;

    private String vimsi;

    private String msisdnToH;

    private String msisdnToV;

    private String messageType;

    private boolean flag;


}
