package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MalfunctionDealDTO {
    String imsi;

    String himsi;

    String mcc;

    String packageUniqueId;

    String createTime;

    String malfunctionId;

    String supplierId;

    int total;

    boolean lastOne = false;
}
