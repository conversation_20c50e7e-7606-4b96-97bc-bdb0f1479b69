package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * cms_channel_online_payment
 * <AUTHOR>
@Data
public class CmsChannelOnlinePayment implements Serializable {
    /**
     * 自增ID
     */
    private Long id;
    /**
     * 用户ebscode
     */
    private String ebsCode;
    /**
     * 购买用户的corpId
     */
    private String corpId;

    /**
     * 订单id，uuid
     */
    private String orderId;

    /**
     * 第三方支付订单标识
     */
    private String thirdOrderNo;

    /**
     * 第三方支付交易流水号
     */
    private String thirdTransactionNo;

    /**
     * 第三方支付订单号
     */
    private String thirdMchorderNo;

    /**
     * 订购类型：1：账单缴费，2：押金缴费
     */
    private String orderType;

    /**
     * 产品id,缴费账单绑定账单ID,押金绑定
     */
    private String productId;

    /**
     * 货币类型
     */
    private String currencyCode;

    /**
     * 金额，单位：分
     */
    private BigDecimal amount;

    /**
     * 记录创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 记录更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 语言
     */
    private String lang;

    /**
     * 支付状态：PAYING-待支付, SUCCESS-支付成功, EXPIRED-支付已过期，CLOSED-未支付关闭订单,FAILED-支付失败
     */
    private String paymentStatus;

    /**
     * 支付方式：微信-wechat, 支付宝-alipay, 银行卡-card
     */
    private String paymentMethod;

    /**
     * 支付IP
     */
    private String paymentIp;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paymentTime;

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    private Boolean isDeleted;

    /**
     * 订单状态，创建成功：1
     */
    private String orderStatus;

    /**
     * 支付截止时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date exprieTime;

    private String orderTitle;

    private String orderBody;

    /**
     * 支付商户号
     */
    private String mchCode;

    /**
     * 支付方式
     */
    private String wayCode;

    private static final long serialVersionUID = 1L;
}