package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ExamineStatus
 * @description  审核状态枚举值
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/5/8 11:08
 */
@Getter
@AllArgsConstructor
public enum ExamineStatusEnum {
    /**
     * 新建待审批
     */
    ADD_WAIT("1"),
    /**
     *  通过
     */
    PASS("2"),
    /**
     *  不通过
     */
    NOPASS("3"),
    /**
     *  删除待审核
     */
    DEL_WAIT("4");

    private String checkStatus;
}
