package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.dto.common.DialectInfo;
import com.ebupt.cmi.clientmanagement.domain.dto.common.PriceInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/24 14:49
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserQueryPackageTransDTO {

//    /**
//     * 套餐包中文名称
//     */
//    String packageName;
//
//    /**
//     * 套餐包繁体名称
//     */
//    String nameTw;
//
//    /**
//     * 套餐包英文名称
//     */
//    String nameEn;

    /**
     * 套餐标识
     */
    private String dataBundleId;

    /**
     * 套餐状态
     */
    private String packageStatus;

    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 订单ID
     */
    private String orderID;

    /**
     * 子订单标识
     */
    private String subscriptionKey;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 订购渠道
     */
    private String orderChannel;

    /**
     * 订购时间
     */
    Date createTime;

    /**
     * 过期时间
     */
    Date expireTime;

    /**
     * 套餐过期时间
     */
    Date endTime;

    /**
     * 套餐激活时间
     */
    Date activeTime;

    /**
     * 套餐指定激活日期
     */
    Date activeAt;

    /**
     * 激活模式
     */
    String activationMode;

    /**
     *
     */
    String flowLimitType;

    /**
     * 周期类型
     */
    String periodUnit;

    String cooperationMode;

    String packageUniqueId;

    int keepPeriod;
}
