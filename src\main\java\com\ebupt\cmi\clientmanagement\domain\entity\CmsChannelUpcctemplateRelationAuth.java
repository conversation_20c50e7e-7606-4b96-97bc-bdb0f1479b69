package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * cms_channel_upcctemplate_relation_auth
 * <AUTHOR>
@Data
public class CmsChannelUpcctemplateRelationAuth implements Serializable {
    private Long id;

    /**
     * 速度模板id
     */
    private String templateId;

    /**
     * 渠道商id
     */
    private String corpId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}