---
description: 
globs: 
alwaysApply: false
---
# COSMIC 分析规则与标准（最终版）

本标准旨在统一COSMIC功能点分析的执行方法与输出格式，确保分析结果的规范性、一致性和可读性。所有后续分析均应严格遵守此标准。

## 一、 基本原则

1.  **理论基础**: 所有分析严格遵循**COSMIC功能点度量法**，其核心是：
    * **度量对象**: 功能性用户需求 (Functional User Requirements)，关注业务“做什么”，而非技术实现。
    * **度量原理**: 通过识别和计数**数据移动 (Data Movement)** 的数量来度量软件规模。

2.  **数据移动类型**: 分析围绕四种基本的数据移动类型展开：
    * **输入 (Entry, E)**: 数据从功能用户跨越边界进入被度量的软件。
    * **输出 (eXit, X)**: 数据从被度量的软件跨越边界传递给功能用户。
    * **读 (Read, R)**: 数据从持久存储介质移动到被度量的软件。
    * **写 (Write, W)**: 数据从被度量的软件移动到持久存储介质。

3.  **详尽性原则**:
    * 为了确保分析过程的完整性，分析将**包含**为了执行业务规则校验（如：检查名称是否重复）而必须发生的**读取（R）数据移动**。
    * 分析过程**不应被高度精简**，应尽可能详细地反映每一个独立的数据移动步骤。

## 二、 输出格式规范

1.  **默认格式**:
    * 标准交付格式为 **CSV (逗号分隔值)**。
    * *注：本文档本身根据您的本次要求，采用Markdown格式进行呈现。*

2.  **表格结构**: 分析结果应包含以下列：
    * `触发事件`
    * `功能过程`
    * `子过程描述`
    * `数据移动类型`
    * `数据组`
    * `数据属性（部分示例）`

3.  **结构性规则**:
    * **省略“功能用户”**: 输出中**不包含**“功能用户”列。
    * **过程合并显示**: 对于同一个`功能过程`中的多个`子过程`（即多行），`触发事件`和`功能过程`这两列**只在第一行显示**，后续行保持为空。

## 三、 内容填写规范

1.  **子过程描述 (`子过程描述`)**:
    * **无序号**: 描述前不加数字序号。
    * **业务化动词**: 使用简洁、贴近业务的动词（如：配置、记录、获取），并**省略**“系统将...”等赘述。
    * **逻辑抽象化**: **不体现**具体的“表”名，而是使用逻辑“信息”（如：渠道档案信息）代替。**不描述**技术性的“读取逻辑”或“校验”字眼。
    * **详细化结果**: 对于最终的**输出（X）**步骤，需详细补充其返回内容，例如：“返回操作结果，成功时包含XX摘要，失败时包含错误原因”。

2.  **数据移动类型 (`数据移动类型`)**:
    * 只保留 **E, R, W, X** 四种单字母缩写。

3.  **数据组 (`数据组`)**:
    * 使用**中文统称**来描述逻辑数据分组。

4.  **数据属性 (`数据属性（部分示例）`)**:
    * **中文释义**: 属性字段名使用**中文释义**。
    * **保留缩写**: 公认的英文大写缩写（如：EBS, URL, IMSI）应直接保留。

    * **ID格式**: 所有标识符的表述均以 **“XXID”** 的形式结尾（如：`渠道ID`）。