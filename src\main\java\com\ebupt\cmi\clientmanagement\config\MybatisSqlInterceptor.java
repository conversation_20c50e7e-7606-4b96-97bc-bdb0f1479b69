package com.ebupt.cmi.clientmanagement.config;

import com.ebupt.cmi.clientmanagement.controller.scheduled.ScheduledController;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.apache.ibatis.reflection.wrapper.DefaultObjectWrapperFactory;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;


//@Intercepts(@Signature(type = Executor.class, method = "update",
//        args = {MappedStatement.class, Object.class}))
@Slf4j
public class MybatisSqlInterceptor implements Interceptor {


    private final static String pre = "/*entityid=";

    private final static String last = "*/";


    @Resource
    private MultipleQueuesConfig multipleQueuesConfig;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        String sql = getSqlbyInvocation(invocation);

        if (StringUtils.isEmpty(sql)) {
            return invocation.proceed();
        }

        Object entityid = getAttribute();

        if (entityid != null){
            sql =  pre + entityid + last + sql;
        } else {
            String tableName = getTableName(sql);
            sql = getFixedSql(sql, tableName);
        }

        resetSqlInvocation(invocation, sql);

        return invocation.proceed();
    }

    private Object getAttribute() {
        Object entityid = null;
        try {
            entityid = httpServletRequest.getAttribute("entityid");
        } catch (Exception e) {
            return null;
        }
        return entityid;
    }


    private String getFixedSql(String sql, String tableName) {

        Map<String, List<Integer>> maps = multipleQueuesConfig.getMaps();

        if (CollectionUtils.isEmpty(maps)) {
            return sql;
        }

        List<Integer> queues = maps.get(tableName);

        String str = pre;

        if (queues == null) {
            str += 5;
        } else if (queues.size() == 1) {
            str += queues.get(0);
        } else {
            int random = new Random().nextInt(queues.size());
            str += queues.get(random);
        }
        str += last;

        sql = str + sql;
        return sql;
    }

    private void resetSqlInvocation(Invocation invocation, String sql) {
        final Object[] args = invocation.getArgs();
        MappedStatement statement = (MappedStatement) args[0];
        Object parameterObject = args[1];
        BoundSql boundSql = statement.getBoundSql(parameterObject);
        MappedStatement newStatement = newMappedStatement(statement, new BoundSqlSqlSource(boundSql));
        MetaObject msObject = MetaObject.forObject(newStatement, new DefaultObjectFactory(), new DefaultObjectWrapperFactory(), new DefaultReflectorFactory());
        msObject.setValue("sqlSource.boundSql.sql", sql);
        args[0] = newStatement;
    }

    private String getSqlbyInvocation(Invocation invocation) {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameterObject = args[1];
        BoundSql boundSql = mappedStatement.getBoundSql(parameterObject);
        return boundSql.getSql();
    }

    private MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder =
                new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length != 0) {
            StringBuilder keyProperties = new StringBuilder();
            for (String keyProperty : ms.getKeyProperties()) {
                keyProperties.append(keyProperty).append(",");
            }
            keyProperties.delete(keyProperties.length() - 1, keyProperties.length());
            builder.keyProperty(keyProperties.toString());
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());

        return builder.build();
    }

    private static String getTableName(String sql) {
        StringBuilder stringBuilder = new StringBuilder();
        boolean flag = false;
        for (int i = 6; i < sql.length(); i++) {
            //从第一个下划线得下个字符开始计算表名
            char c = sql.charAt(i);
            if (flag) {
                stringBuilder.append(c);
            }
            if (!flag && c == '_') {
                flag = true;
            }
            //到空格说明表名结束
            if (flag && c == ' '){
                break;
            }
        }

        return stringBuilder.toString().trim();
    }


    private String getOperateType(Invocation invocation) {
        final Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        SqlCommandType commondType = ms.getSqlCommandType();
        if (commondType.compareTo(SqlCommandType.SELECT) == 0) {
            return "select";
        }
        if (commondType.compareTo(SqlCommandType.INSERT) == 0) {
            return "insert";
        }
        if (commondType.compareTo(SqlCommandType.UPDATE) == 0) {
            return "update";
        }
        if (commondType.compareTo(SqlCommandType.DELETE) == 0) {
            return "delete";
        }
        return null;
    }

    //    定义一个内部辅助类，作用是包装sq
    static class BoundSqlSqlSource implements SqlSource {
        private final BoundSql boundSql;

        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }


}
