package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 上网明细表
 * @date 2021/4/20 15:57
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_surf_detail")
@Data
@ToString
@Builder
public class ChannelSurfDetail extends BaseEntity {

    private Long id;
    private Long surfId;

    private String corpId;

    private String changeStatus;

    private Date startTime;
    private Date endTime;

}
