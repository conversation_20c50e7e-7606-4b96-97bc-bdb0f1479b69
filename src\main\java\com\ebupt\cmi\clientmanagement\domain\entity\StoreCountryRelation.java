package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("cms_store_country_relation")
public class StoreCountryRelation extends BaseEntity{
    private Long id;
    /**
     * 仓库id
     */
    private Long storeId;

    /**
     * 国家id
     */
    private String mcc;



}

