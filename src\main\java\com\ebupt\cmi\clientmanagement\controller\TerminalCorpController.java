package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.TerminalCorpDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.TerminalDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.TerminalRecordDetailDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsTerminalcorpPlmnlist;
import com.ebupt.cmi.clientmanagement.domain.entity.SettleRuleDetail;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.service.ITerminalCorpService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 终端厂商Controller
 * @date 2021/5/7 11:03
 */
@Api(tags = "终端厂商相关接口")
@RestController
@RequestMapping("/api/v1/terminal")
@AllArgsConstructor
public class TerminalCorpController {

    private final ITerminalCorpService terminalCorpService;

    @ApiOperation(value = "新建终端厂商")
    @PostMapping
    @OperationLog(operationName = "终端厂商管理——新增厂商",operationType = OperationTypeEnum.ADD)
    public Response<Void> addTerminal(@RequestBody @Validated TerminalVO channel) {
        terminalCorpService.addTerminal(channel);
        return Response.ok();
    }

    @ApiOperation(value = "查询终端厂商详情")
    @ApiImplicitParam(name = "id", value = "终端厂商id", paramType = "path", dataType = "string", required = true)
    @GetMapping("/{id}")
    public Response<TerminalDTO> getTerminal(@PathVariable String id) {
        TerminalDTO terminal = terminalCorpService.getTerminal(id);
        return Response.ok(terminal);
    }

    @ApiOperation(value = "修改终端厂商")
    @ApiImplicitParam(name = "id", value = "终端厂商id", paramType = "path", dataType = "string", required = true)
    @PutMapping("/{id}")
    @OperationLog(operationName = "终端厂商管理——修改厂商",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> updateTerminal(@PathVariable String id, @RequestBody @Validated TerminalVO terminalVO) {
        terminalCorpService.updateTerminal(id, terminalVO);
        return Response.ok();
    }

    @ApiOperation(value = "审核终端厂商")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "终端厂商id", paramType = "path", dataType = "string", required = true),
            @ApiImplicitParam(name = "passed", value = "true通过/false不通过", paramType = "query", dataType = "boolean", required = true),
            @ApiImplicitParam(name = "available", value = "true可用/false不可用", paramType = "query", dataType = "boolean")
    })
    @PutMapping("/audit/{id}")
    @OperationLog(operationName = "终端厂商管理——审核厂商",operationType = OperationTypeEnum.AUDIT)
    public Response<Void> auditTerminal(@PathVariable String id, @RequestParam boolean passed, @RequestParam(required = false) Boolean available) {
        terminalCorpService.auditTerminal(id, passed, available);
        return Response.ok();
    }

    @ApiOperation(value = "分页查询终端厂商")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageSize", value = "单页显示数量", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "pageNumber", value = "显示第几页数据", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "corpName", value = "厂商名称，模糊匹配", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "corpType", value = "厂商类型[7终端线上|8终端显现]，模糊匹配", paramType = "query", dataType = "string"),
    })
    @GetMapping("/pages")
    public Response<PageResult<TerminalCorpDTO>> getTerminals(@RequestParam Integer pageSize,
                                                              @RequestParam Integer pageNumber,
                                                              @RequestParam(required = false) String corpName,
                                                              @RequestParam(required = false) String corpType) {
        PageResult<TerminalCorpDTO> results = terminalCorpService.getTerminals(pageSize, pageNumber, corpName, corpType);
        return Response.ok(results);
    }

    @ApiOperation(value = "查询终端厂商结算表")
    @ApiImplicitParam(name = "corpId", value = "渠道id", paramType = "query", dataType = "string")
    @GetMapping("/settleRule/query")
    public Response<List<FlowSettleRuleDetailVO>> getSettleRules(@RequestParam String corpId, @RequestParam String settleType) {

        return Response.ok(terminalCorpService.getSettleRules(corpId, settleType));
    }

    @ApiOperation(value = "分页查询终端厂商结算表")
    @GetMapping("/settleRule/queryList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "corpId", value = "渠道id", paramType = "String"),
            @ApiImplicitParam(name = "settleType", value = "线下厂商付费模式", paramType = "String"),
            @ApiImplicitParam(name = "pageSize", value = "页大小", paramType = "Integer"),
            @ApiImplicitParam(name = "pageNumber", value = "页数", paramType = "Integer")
    })
    public Response getSettleRulesList(@RequestParam String corpId,
                                       @RequestParam String settleType,
                                       @RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                                       @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return terminalCorpService.getSettleRulesList(corpId, settleType, pageNumber, pageSize);
    }

    @ApiOperation(value = "套餐列表查询(查询结算规则的表套餐)")
    @ApiImplicitParam(name = "corpId", value = "渠道id", paramType = "query", dataType = "string")
    @GetMapping("/settleRule/{corpId}")
    public Response<List<SettleRuleDetail>> getSettleRuleList(@PathVariable @ApiParam("厂商ID") String corpId) {
        return Response.ok(terminalCorpService.getSettleRuleList(corpId));
    }

    @ApiOperation(value = "单个新增终端厂商结算表")
    @PostMapping("/settleRule/add")
    @OperationLog(operationName = "终端厂商管理——单个新增终端厂商结算规则",operationType = OperationTypeEnum.ADD)
    public Response addSettleRule(@RequestBody @Validated SettleRuleDetailVO settleRuleDetailVO) {
        terminalCorpService.addSettleRule(settleRuleDetailVO);
        return Response.ok();
    }

    @ApiOperation(value = "单个修改终端厂商结算表")
    @PutMapping("/settleRule/update")
    @OperationLog(operationName = "终端厂商管理——单个修改终端厂商结算规则",operationType = OperationTypeEnum.UPDATE)
    public Response updateSettleRule(@RequestBody @Validated SettleRuleDetailVO settleRuleDetailVO) {
        terminalCorpService.updateSettleRule(settleRuleDetailVO);
        return Response.ok();
    }

    @ApiOperation(value = "单个删除终端厂商结算表")
    @DeleteMapping("/settleRule/delete/{id}")
    @OperationLog(operationName = "终端厂商管理——单个删除终端厂商结算规则",operationType = OperationTypeEnum.DELETE)
    public Response deleteSettleRule(@PathVariable String id) {
        terminalCorpService.deleteSettleRule(id);
        return Response.ok();
    }

    @ApiOperation(value = "多个删除终端厂商结算表")
    @PostMapping("/settleRule/deleteBatch")
    @OperationLog(operationName = "终端厂商管理——多个删除终端厂商结算规则",operationType = OperationTypeEnum.DELETE)
    public Response deleteSettleRuleBatch(@RequestBody List<Long> ids) {
        terminalCorpService.deleteSettleRuleBatch(ids);
        return Response.ok();
    }

    @ApiOperation(value = "查询终端厂商详情[V卡使用详情]")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageSize", value = "单页显示数量", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "pageNumber", value = "显示第几页数据", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "id", value = "终端厂商id", paramType = "path", dataType = "string", required = true),
            @ApiImplicitParam(name = "yearMonth", value = "月份[格式yyyy-MM]", paramType = "query", dataType = "string")
    })
    @GetMapping("/details")
    public Response<PageResult<TerminalRecordDetailDTO>> getTerminalDetails(@RequestParam String id,
                                                                            @RequestParam(required = false, value = "yearMonth") String yearMoth,
                                                                            @RequestParam Integer pageSize,
                                                                            @RequestParam Integer pageNumber) {
        PageResult<TerminalRecordDetailDTO> results = terminalCorpService.getTerminalDetails(id, yearMoth, pageNumber, pageSize);
        return Response.ok(results);
    }

    @ApiOperation(value = "导出终端厂商详情[V卡使用详情]")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "终端厂商id", paramType = "path", dataType = "string", required = true),
            @ApiImplicitParam(name = "yearMonth", value = "月份[格式yyyy-MM]", paramType = "query", dataType = "string")
    })
    @GetMapping(value = "/details/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportTerminalDetails(@RequestParam String id,
                                      @RequestParam(required = false, value = "yearMonth") String yearMoth) {
        terminalCorpService.exportTerminalDetails(id, yearMoth);
    }


    @ApiOperation(value = "删除终端厂商，将审核状态置为删除待审核")
    @ApiImplicitParam(name = "idList", value = "终端厂商id列表", paramType = "body", dataType = "list", required = true)
    @DeleteMapping
    @OperationLog(operationName = "终端厂商管理——删除厂商",operationType = OperationTypeEnum.DELETE)
    public Response<Void> delTerminal(@RequestBody List<String> idList) {
        terminalCorpService.batchLogicDel(idList);
        return Response.ok();
    }

    @ApiOperation(value = "码号资源V-imsi下发接口")
    @PostMapping("/vimsiSend")
    public Response<DataType> vimsiSend(@RequestBody @Valid VimsiSend vimsiSend) {
        return Response.ok(terminalCorpService.vimsiSend(vimsiSend, null, true, null));
    }

    @ApiOperation(value = "查询终端厂商plmnlist")
    @GetMapping("/plmnlist/get")
    public Response<List<CmsTerminalcorpPlmnlist>> getPlmnlist(@RequestParam("corpId") String corpId) {
        return Response.ok(terminalCorpService.getPlmnlist(corpId));
    }

    @ApiOperation(value = "新建终端厂商plmnlist")
    @PostMapping("/plmnlist/add")
    @OperationLog(operationName = "终端厂商管理——新建终端厂商plmnlist",operationType = OperationTypeEnum.ADD)
    public Response<Void> addPlmnlist(@RequestBody @Validated List<PlmnlistVO> voList) {
        terminalCorpService.addPlmnlist(voList);
        return Response.ok();
    }

    @ApiOperation(value = "修改终端厂商plmnlist")
    @PostMapping("/plmnlist/update")
    @OperationLog(operationName = "终端厂商管理——修改终端厂商plmnlist",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> updatePlmnlist(@RequestBody @Validated List<PlmnlistVO> voList) {
        terminalCorpService.updatePlmnlist(voList);
        return Response.ok();
    }

    @ApiOperation("文件新增终端厂商plmnlist")
    @PostMapping("/plmnlist/createByFile")
    @OperationLog(operationName = "终端厂商管理-文件新增终端厂商plmnlist", operationType = OperationTypeEnum.ADD)
    public Response<Void> createPlmnlistByFile(@Validated PlmnlistCreateVO vo) {
        terminalCorpService.createPlmnlistByFile(vo);
        return Response.ok();
    }
}
