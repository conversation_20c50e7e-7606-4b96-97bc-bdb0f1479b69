package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ebupt.excel.annotation.ExcelExport;
import com.ebupt.excel.constant.ExcelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * stat_channelincome_detail_month
 *
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatChannelincomeDetailMonth {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计月YYYYMM
     */
    private String statTime;

    /**
     * 渠道商名称
     */
    @ExcelExport(description = "Channel name", index = "0")
    @TableField(exist=false)
    private String companyName;

    private String corpName;

    /**
     * Uuid,厂商ID或者个人用户id
     */
    private String corpId;

    /**
     * EBSCode
     */
    @ExcelExport(description = "Customer EBS CODE", index = "1")
    private String ebscode;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订购日期
     */
    @ExcelExport(description = "Order Date", index = "2")
    private Date orderDate;

    /**
     * 渠道订单
     * 订购订单，填空
     * 退订订单，填退订时间
     * 个人订单
     * 激活订单，填激活时间
     * 已激活退订订单，填退订时间
     * 格式参考样例
     */
    @ExcelExport(description = "Date", index = "3")
    private Date dateTime;

    /**
     * 卡号
     */
    @ExcelExport(description = "ICCID", index = "4")
    private String iccid;

    /**
     * 套餐名称，英文
     */
    @ExcelExport(description = "Package", index = "5")
    private String packageName;

    /**
     * 数量（退订为负数），统计子订单，数量为1或-1
     */
    @ExcelExport(description = "Quantity", index = "6", type = ExcelTypeEnum.Integer)
    private Integer quantity;

    /**
     * 币种编码：156 人民币 840 美元 344 港币
     */
    @ExcelExport(description = "Currency", index = "7")
    private String currency;

    /**
     * 订单金额，统计子订单金额
     */
    @ExcelExport(description = "Amount", index = "8", type = ExcelTypeEnum.Double)
    private BigDecimal amount;

    /**
     * 订单金额，按照汇率折算为渠道商币种，渠道商币种为美元填写此项
     */
    @ExcelExport(description = "Amount_USD", index = "9", type = ExcelTypeEnum.Double)
    private BigDecimal amountUsd;

    /**
     * 订单金额，按照汇率折算为渠道商币种，渠道商币种为人民币填写此项
     */
    @ExcelExport(description = "Amount_CNY", index = "10", type = ExcelTypeEnum.Double)
    private BigDecimal amountCny;

    /**
     * 订单金额，按照汇率折算为渠道商币种，渠道商币种为港币填写此项
     */
    @ExcelExport(description = "Amount_HKD", index = "11", type = ExcelTypeEnum.Double)
    private BigDecimal amountHkd;

    /**
     * 销售模式
     * 渠道订单：1：Directly sales
     * 个人订单：2:Indirectly sales
     */
    @ExcelExport(description = "Sales model", index = "12")
    private String salesModel;

    /**
     * 佣金率
     */
    @ExcelExport(description = "Commission Ratio", index = "13", type = ExcelTypeEnum.Integer)
    private Integer commissionRatio;

    /**
     * 佣金
     */
    @ExcelExport(description = "Commission Amount", index = "14", type = ExcelTypeEnum.Double)
    private BigDecimal commissionAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    private String orderType;

    private String cooperationMode;

    private long accountingId;
}