package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/8 16:45
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDTO {

    @NotNull(message = "订单名称不能为空")
    @ApiModelProperty("订单名称")
    String orderName;

    @NotNull(message = "渠道id不能为空")
    @ApiModelProperty("渠道id")
    String corpId;

    @NotNull(message = "付费模式不能为空")
    @ApiModelProperty("付费模式")
    String payType;

    @NotNull(message = "套餐id不能为空")
    @ApiModelProperty("套餐id")
    String packageId;
}
