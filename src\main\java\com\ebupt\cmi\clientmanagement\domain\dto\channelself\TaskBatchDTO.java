package com.ebupt.cmi.clientmanagement.domain.dto.channelself;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskBatchDTO.java
 * @createTime 2021年06月16日 18:58:00
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskBatchDTO {
    /**
     * 任务id
     */
    String taskId;
    /**
     * 任务名称
     */
    String taskName;

    /**
     * 卡片数量
     */
    Integer taskNum;

    /**
     * 创建时间
     */
    String createTime;

}
