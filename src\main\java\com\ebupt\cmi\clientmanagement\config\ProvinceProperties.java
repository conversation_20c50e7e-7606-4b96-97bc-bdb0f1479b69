package com.ebupt.cmi.clientmanagement.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties("province")
public class ProvinceProperties {

    private Sftp sftp;

    @Data
    public static class Sftp {
        private String host;
        private int port;
        private String username;
        private String password;
        private String remotePath;
        private int sessionTimeout;
    }

}
