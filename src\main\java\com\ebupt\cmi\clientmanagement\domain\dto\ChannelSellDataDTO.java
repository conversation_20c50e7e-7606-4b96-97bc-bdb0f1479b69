package com.ebupt.cmi.clientmanagement.domain.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelSellDataDTO {
    private String corpName;

    private String account;

    private String cooperationMode;

    private String currencyCode;

    private String contractSellAmount;

    private String completedAmount;

    private String currentPeriodBill;

    private String arrears;
}
