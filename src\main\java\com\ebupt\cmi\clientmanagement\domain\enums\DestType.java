package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * destType
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/5/13 16:05
 */
@Getter
@AllArgsConstructor
public enum DestType {
    /**
     * //TODO 未知枚举值
     */
    VLR("vlr"),

    SGSN("SGSN"),

    MME("MME"),

    S4SGSN("S4SGSN"),

    AAA("AAA"),

    ALL("ALL");

    private String type;
}
