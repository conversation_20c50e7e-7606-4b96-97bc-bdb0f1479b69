package com.ebupt.cmi.clientmanagement.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName GlobalConstant.java
 * @Description 全局常量枚举 用来拼接完整的URL
 * @createTime 2020年11月04日 15:25:00
 */

public enum GlobalConstant {
    /*redis的url统一前缀*/
    REDIS_CONNECTION_PREFIX("redis://", "Redis地址配置前缀");

    private final String constant_value;
    private final String constant_desc;

    GlobalConstant(String constantValue, String constantDesc) {
        this.constant_value = constantValue;
        this.constant_desc = constantDesc;
    }

    public String getConstantValue() {
        return constant_value;
    }

    public String getConstantDesc() {
        return constant_desc;
    }
}
