package com.ebupt.cmi.clientmanagement.domain.entity.redis;

import com.ebupt.cmi.clientmanagement.constant.RedisConnectionType;
import com.ebupt.cmi.clientmanagement.strategy.RedissonConfigService;
import com.ebupt.cmi.clientmanagement.strategy.impl.ClusterConfigImpl;
import com.ebupt.cmi.clientmanagement.strategy.impl.MasterslaveConfigImpl;
import com.ebupt.cmi.clientmanagement.strategy.impl.SentineConfigImpl;
import com.ebupt.cmi.clientmanagement.strategy.impl.StandaloneConfigImpl;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.redisson.config.Config;

import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RedissonLock.java
 * @Description 针对源码Redisson进行一层装饰者模式
 * @createTime 2020年12月22日 16:50:00
 */

@Slf4j
public class RedissonLock {
    private Redisson redisson;
    private RedisUtil redisUtil;

    public RedissonLock(RedissonProperties redissonProperties) {
        try {
            //通过不同部署方式获得不同cofig实体
            Config config = RedissonConfigFactory.getInstance().createConfig(redissonProperties);
            redisson = (Redisson) Redisson.create(config);
        } catch (Exception e) {
            throw new IllegalArgumentException("please input correct configurations," +
                    "connectionType must in standalone/sentinel/cluster/masterslave");
        }
    }

    /**
     * 加锁操作 （设置锁的有效时间）
     *
     * @param lockName  锁名称
     * @param leaseTime 锁有效时间
     */
    public void lock(String lockName, long leaseTime) {
        RLock rLock = redisson.getLock(lockName);
        rLock.lock(leaseTime, TimeUnit.SECONDS);
    }

    /**
     * 加锁操作 （多个锁，设置锁的有效时间）
     *
     * @param lockNames 锁名称
     * @param leaseTime 锁有效时间
     */
    public void multiLock(Collection<String> lockNames, long leaseTime) {
        final RLock[] locks = lockNames.stream().map(redisson::getLock).toArray(RLock[]::new);
        RedissonMultiLock multiLock = new RedissonMultiLock(locks);
        multiLock.lock(leaseTime, TimeUnit.SECONDS);
    }

    /**
     * 加锁操作 (锁有效时间采用默认时间30秒）
     *
     * @param lockName 锁名称
     */
    public void lock(String lockName) {
        RLock rLock = redisson.getLock(lockName);
        rLock.lock();
    }

    /**
     * 加锁操作 （多个锁，锁有效时间采用默认时间30秒）
     *
     * @param lockNames 锁名称
     */
    public void lockMulti(Collection<String> lockNames) {
        final RLock[] locks = lockNames.stream().map(redisson::getLock).toArray(RLock[]::new);
        RedissonMultiLock multiLock = new RedissonMultiLock(locks);
        multiLock.lock();
    }

    /**
     * 加锁操作(tryLock锁，没有等待时间，锁有效时间采用默认时间30秒）
     *
     * @param lockName 锁名称
     */
    public boolean tryLock(String lockName) {
        RLock rLock = redisson.getLock(lockName);
        return rLock.tryLock();
    }

    /**
     * 加锁操作 （多个锁，锁有效时间采用默认时间30秒）
     *
     * @param lockNames 锁名称
     */
    public boolean tryLockMulti(Collection<String> lockNames) {
        final RLock[] locks = lockNames.stream().map(redisson::getLock).toArray(RLock[]::new);
        RedissonMultiLock multiLock = new RedissonMultiLock(locks);
        return multiLock.tryLock();
    }

    /**
     * 加锁操作(tryLock锁，没有等待时间）
     *
     * @param lockName  锁名称
     * @param leaseTime 锁有效时间
     */
    public boolean tryLock(String lockName, long leaseTime) {
        RLock rLock = redisson.getLock(lockName);
        boolean getLock = false;
        try {
            getLock = rLock.tryLock(0, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("e: ", e);
            e.printStackTrace();
            return false;
        }
        return getLock;
    }

    /**
     * 加锁操作(tryLock锁，有等待时间）
     *
     * @param lockName  锁名称
     * @param leaseTime 锁有效时间
     * @param waitTime  等待时间
     */
    public boolean tryLock(String lockName, long leaseTime, long waitTime) {

        RLock rLock = redisson.getLock(lockName);
        boolean getLock = false;
        try {
            getLock = rLock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
            return false;
        }
        return getLock;
    }

    /**
     * 解锁
     *
     * @param lockName 锁名称
     */
    public void unlock(String lockName) {
        redisson.getLock(lockName).unlock();
    }

    /**
     * 解锁
     *
     * @param lockNames 锁名称
     */
    public void unlockMulti(Collection<String> lockNames) {
        try {
            final RLock[] locks = lockNames.stream().map(redisson::getLock).toArray(RLock[]::new);
            new RedissonMultiLock(locks).unlock();
        } catch (Exception e){
            log.warn("redis分布式锁解锁失败，请注意");
        }
    }

    /**
     * 判断该锁是否已经被线程持有
     *
     * @param lockName 锁名称
     */
    public boolean isLock(String lockName) {
        RLock rLock = redisson.getLock(lockName);
        return rLock.isLocked();
    }


    /**
     * 判断该线程是否持有当前锁
     *
     * @param lockName 锁名称
     */
    public boolean isHeldByCurrentThread(String lockName) {
        RLock rLock = redisson.getLock(lockName);
        return rLock.isHeldByCurrentThread();
    }

    /**
     * Redisson连接方式配置工厂
     * 双重检查锁
     */
    static class RedissonConfigFactory {

        private RedissonConfigFactory() {
        }

        private static volatile RedissonConfigFactory factory = null;

        public static RedissonConfigFactory getInstance() {
            if (factory == null) {
                synchronized (RedissonConfigFactory.class) {
                    if (factory == null) {
                        factory = new RedissonConfigFactory();
                    }
                }
            }
            return factory;
        }


        /**
         * 根据连接类型获取对应连接方式的配置,基于策略模式
         *
         * @param redissonProperties redis连接信息
         * @return Config
         */
        Config createConfig(RedissonProperties redissonProperties) {
            Preconditions.checkNotNull(redissonProperties);
            Preconditions.checkNotNull(redissonProperties.getAddress(), "redisson.lock.server.address cannot be NULL!");
            Preconditions.checkNotNull(redissonProperties.getType(), "redisson.lock.server.password cannot be NULL");
            String connectionType = redissonProperties.getType();
            //声明配置上下文
            RedissonConfigService redissonConfigService = null;
            if (connectionType.equals(RedisConnectionType.STANDALONE.getConnection_type())) {
                redissonConfigService = new StandaloneConfigImpl();
            } else if (connectionType.equals(RedisConnectionType.SENTINEL.getConnection_type())) {
                redissonConfigService = new SentineConfigImpl();
            } else if (connectionType.equals(RedisConnectionType.CLUSTER.getConnection_type())) {
                redissonConfigService = new ClusterConfigImpl();
            } else if (connectionType.equals(RedisConnectionType.MASTERSLAVE.getConnection_type())) {
                redissonConfigService = new MasterslaveConfigImpl();
            } else {
                throw new IllegalArgumentException("创建Redisson连接Config失败！当前连接方式:" + connectionType);
            }
            return redissonConfigService.createRedissonConfig(redissonProperties);
        }
    }
}
