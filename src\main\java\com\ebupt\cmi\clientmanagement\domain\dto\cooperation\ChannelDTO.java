package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelDTO.java
 * @Description 客户信息表DTO
 * @createTime 2021年05月07日 19:55:00
 */
@Data
public class ChannelDTO {

    @ApiModelProperty(value = "运营商id")
    @NotEmpty(message = "运营商id不能为空")
    private String corpId;

    @ApiModelProperty(value = "运营商名称")
    @NotEmpty(message = "运营商名称不能为空")
    private String corpName;

    @ApiModelProperty(value = "运营商状态")
    @NotEmpty(message = "运营商状态不能为空")
    @Pattern(regexp = "\\d",message = "status错误")
    private String status;

    @ApiModelProperty(value = "就是ebsCode")
    @Length(max = 50,message = "你的ebsCode太长了！")
    private String ebsCode;

    @ApiModelProperty(value = "扣费类型1：资费编码 2：流量方向")
    @NotEmpty(message = "扣费类型")
    @Pattern(regexp = "\\d",message = "billType错误")
    private String billType;

    @ApiModelProperty(value = "结算类型：1：资费编码,2：流量方向")
    @NotEmpty(message = "结算类型不能为空")
    @Pattern(regexp = "\\d",message = "settleType错误")
    private String settleType;

    @ApiModelProperty(value = "币种")
    @NotEmpty(message = "币种信息不能为空")
    private String currencyCode;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("内部订单")
    private String internalOrder;

    @ApiModelProperty("地址")
    private String address;
}
