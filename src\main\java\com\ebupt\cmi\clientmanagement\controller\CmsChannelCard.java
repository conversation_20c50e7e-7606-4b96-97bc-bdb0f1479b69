package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.CcrMsg;
import com.ebupt.cmi.clientmanagement.domain.dto.HImsiVO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.ChannelCardFlowAddVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.service.ChannelCardService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 客户与卡接口相关接口
 *
 * @Date 2022/1/4 16:10
 */
@Slf4j
@RestController
@RequestMapping("/channelCard")
@Api(tags = "客户与卡相关接口")
public class CmsChannelCard {

    @Autowired
    private ChannelCardService channelCardService;

    @ApiOperation("ICCID导入")
    @PostMapping("/flowPoolAddCard")
    @OperationLog(operationName = "流量池管理——ICCID导入", operationType = OperationTypeEnum.ADD)
    public Response newICCIDBindFlowPool(@RequestBody @Valid ChannelCardFlowAddVO channelCardFlowAddVO) {
        try {
            log.debug("ICCID单个导入，入参{}", channelCardFlowAddVO);
            channelCardService.addFlowPool(channelCardFlowAddVO);
            return Response.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("ICCID批量导入")
    @PostMapping("/flowPoolAddCardBatch")
    @OperationLog(operationName = "流量池管理——ICCID批量导入", operationType = OperationTypeEnum.ADD)
    public Response newICCIDBindFlowPoolBatch(MultipartFile file, @RequestParam("poolID") String poolID) {
        try {
            Optional.ofNullable(file).orElseThrow(() -> new BizException("Upload file cannot be empty"));
            String[] split = file.getOriginalFilename().split("\\.");
            String suffix = split[split.length - 1];
            if (!"csv".equalsIgnoreCase(suffix)) {
                Response.error("Please upload a text file in csv format");
            }
            if (file.isEmpty()) {
                Response.error("Please do not upload empty files");
            }
            //新增任务表
            //启动异步线程入库iccid和流量池，并更新任务表。
            log.debug("流量池{}批量入库", poolID);
            channelCardService.addFlowPoolBatch(file, poolID);

            return Response.ok();
        } catch (Exception e) {
            log.debug("{}", e);
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("ICCID导入-外部API使用")
    @PostMapping("/flowPoolAddCardApi")
    public Response newICCIDBindFlowPoolAPI(@RequestBody @Valid ChannelCardFlowAddVO channelCardFlowAddVO) {
        try {
            log.debug("外部API调用：{}", channelCardFlowAddVO);
            channelCardService.addFlowPool(channelCardFlowAddVO);
            return Response.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("backupData")
    @PostMapping("/backupData")
    public Response backupData() {
        channelCardService.backupData();
        return Response.ok();
    }

    @ApiOperation("clearExpiredData")
    @PostMapping("/clearExpiredData")
    public Response clearExpiredData(@RequestParam @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") Date cardInfoDate, @RequestParam @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") Date realNameInfoDate) {
        channelCardService.clearExpiredData(cardInfoDate, realNameInfoDate);
        return Response.ok();
    }

    @PostMapping("/clearExpiredCard")
    public Response clearExpiredCard() {
        channelCardService.clearExpiredCard();
        return Response.ok();
    }

    @GetMapping("/getChannelCard")
    public Response<ChannelCard> getChannelCard(@RequestParam String iccid, @RequestParam String corpId) {
        return Response.ok(channelCardService.getChannelCard(iccid, corpId));
    }

    @GetMapping("/getSession")
    public Response<CcrMsg> getSession(@RequestParam String imsi) {
        return Response.ok(channelCardService.getSession(imsi));
    }

    @PostMapping("/getBillingImsi")
    public Response<List<ChannelCard>> getBillingImsi(@RequestBody Set<String> imsi) {
        return Response.ok(channelCardService.getBillingImsi(imsi));
    }

    @PostMapping("/getCardIsLimited")
    public Response<List<HImsiVO>> getCardIsLimited(@RequestBody List<HImsiVO> cards) {
        return Response.ok(channelCardService.getCardIsLimited(cards));
    }
}
