package com.ebupt.cmi.clientmanagement.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.io.File;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel(value = "OcrApiParam",description = "ocr证件识别入参")
@AllArgsConstructor
@NoArgsConstructor
public class OcrApiParam {

    @ApiModelProperty("证件图片文件:" +
            "要求base64编码后大小不超过10M。\n" +
            "支持JPG/PNG/BMP/TIFF格式")
//    @NotNull(message = "文件不能为空")
    private File file;

    @ApiModelProperty("证件类型")
    @NotBlank(message = "证件类型不能为空")
    String certificateType;

    @ApiModelProperty("项目区域, 不需要传入")
    private String region;

    @ApiModelProperty("base64图片数据, 不需要传入")
    private String image;

}
