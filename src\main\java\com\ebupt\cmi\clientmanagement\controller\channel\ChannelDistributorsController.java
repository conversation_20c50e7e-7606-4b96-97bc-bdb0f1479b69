package com.ebupt.cmi.clientmanagement.controller.channel;

import com.ebupt.cmi.clientmanagement.domain.dto.KanBanDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channel.*;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.validation.ChannelDeleteGroup;
import com.ebupt.cmi.clientmanagement.domain.validation.CommonGroup;
import com.ebupt.cmi.clientmanagement.domain.validation.RechargeRecordGroup;
import com.ebupt.cmi.clientmanagement.domain.vo.ChannelBillVO;
import com.ebupt.cmi.clientmanagement.domain.vo.ExportVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.*;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.service.channel.ChannelDistributorsService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * ChannelDistributorsController
 *
 * @Author: zhaoqiankun
 * @Date: 2021/6/14 22:29
 */
@Api(tags = "渠道商详情相关接口")
@RestController
@RequestMapping("/channel/distributors")
@AllArgsConstructor
public class ChannelDistributorsController {

    private final ChannelDistributorsService channelDistributorsService;

    @PostMapping("/detail")
    @ApiOperation(value = "分页查询渠道商信息")
    public Response<PageResult<AuthDTO>> getDetail(@RequestBody @Validated({CommonGroup.class}) ChannelOptVO channelOptVO) {
        return Response.ok(channelDistributorsService.getDetails(channelOptVO));
    }

    @DeleteMapping("/delete")
    @OperationLog(operationName = "客户管理-渠道商管理", operationType = OperationTypeEnum.DELETE)
    @ApiOperation(value = "删除渠道商信息")
    public Response delete(@RequestBody @Validated({ChannelDeleteGroup.class}) ChannelDeleteVO channelDeleteVO) {
        List<String> corpNames = channelDistributorsService.delete(channelDeleteVO.getCorpIds());
        if (!corpNames.isEmpty()) throw new BizException(StringUtils.join(corpNames, ",") + " 有流量池不能删除");
        return Response.ok();
    }

    @GetMapping("/infoforstat/{corpId}")
    @ApiOperation(value = "渠道商基本信息")
    public Response<Channel> getInfo(@PathVariable String corpId) {
        return channelDistributorsService.getInfo(corpId);
    }

    @GetMapping("/info")
    @ApiOperation(value = "查询渠道商基本信息")
    public Response<ChannelInfoAuthDTO> getDetailInfo(@RequestParam @ApiParam(value = "corpId", name = "corpId", required = true) String corpId) {
        return channelDistributorsService.getDetailInfo(corpId);
    }

    @GetMapping("/getImsiNameByCorpId")
    @ApiOperation(value = "查询渠道商资源合作得流量计费id")
    public Response<Map<String, String>> getImsiNameByCorpId(@RequestParam @ApiParam(value = "corpId", name = "corpId", required = true) String corpId) {
        return Response.ok(channelDistributorsService.getImsiNameByCorpId(corpId));
    }


    @PostMapping("/record")
    @ApiOperation(value = "充值记录查询接口")
    public Response<PageResult<ChannelChargeRecord>> getRechargeRecords(@RequestBody @Validated({RechargeRecordGroup.class, CommonGroup.class}) RechargeRecordVO rechargeRecordVO) {
        return Response.ok(channelDistributorsService.getRechargeRecords(rechargeRecordVO));
    }

    @GetMapping("/record/export/{corpId}")
    @ApiOperation(value = "充值记录导出接口")
    public void exportRecords(@PathVariable @ApiParam(value = "corpId", name = "corpId", required = true)
                              String corpId, HttpServletResponse response) throws IOException {
        channelDistributorsService.exportRechargeRecords(corpId, response);
    }

    @PostMapping("/purchase/record")
    @ApiOperation(value = "套餐购买记录查询接口（分页）")
    public Response<PageResult<RecordDTO>> getPurchaseRecord(@RequestBody @Validated({RechargeRecordGroup.class, CommonGroup.class}) PurchaseRecordVO purchaseRecordVO) {
        return Response.ok(channelDistributorsService.getPurchaseRecord(purchaseRecordVO));
    }

    @GetMapping("/purchase/record/export")
    @ApiOperation(value = "套餐购买记录导出接口")
    public void getPurchaseRecord(@RequestParam @ApiParam(value = "corpId", name = "corpId", required = true) String corpId,
                                  @RequestParam @ApiParam(value = "月份", name = "month", required = false) String month,
                                  HttpServletResponse response) throws IOException {
        channelDistributorsService.exportPurchaseRecord(corpId, month, response);
    }

    @PostMapping("/recharge")
    @ApiOperation(value = "渠道商充值接口")
    public Response<BigDecimal> recharge(@RequestBody @Validated RechargeVO rechargeVO) {
        int i = rechargeVO.getAmount().compareTo(BigDecimal.ZERO);
        if (i < 1) {
            throw new BizException("非法的充值金额");
        }
        return channelDistributorsService.recharge(rechargeVO.getCorpId(), rechargeVO.getAmount(), rechargeVO.getCodeType());
    }

    @PostMapping("/remunerate/export")
    @ApiOperation(value = "渠道商酬金详情导出接口")
    public void remunerationDetails(@RequestParam @ApiParam(value = "corpId", name = "corpId", required = true) String corpId,
                                    @RequestParam @ApiParam(value = "月份", name = "month", required = false) String month,
                                    HttpServletResponse response) throws IOException {
        channelDistributorsService.exportRemunerationDetails(corpId, month, response);
    }

    @GetMapping("/getByCorpId")
    @ApiOperation(value = "根据corpId查询渠道商详情")
    public Response<ChannelDistributorDetail> getByCorpId(@RequestParam String corpId) {
        return Response.ok(channelDistributorsService.getDistributorDetailByCorpId(corpId));
    }

    @GetMapping("/getByAppKey")
    @ApiOperation(value = "根据appKey查询渠道商详情")
    public Response<ChannelDistributorDetail> getByAppKey(@RequestParam String appKey) {
        return Response.ok(channelDistributorsService.getDistributorDetailByAppKey(appKey));
    }

    @PostMapping("/channelBill")
    @ApiOperation(value = "渠道商账单流水分页接口")
    public Response<PageResult<ChannelBillFlowRecord>> channelBillDetails(@RequestBody @Validated ChannelBillVO channelBillVO) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return Response.ok(channelDistributorsService.channelBill(channelBillVO.getCorpId(), simpleDateFormat.parse(channelBillVO.getStartTime()), simpleDateFormat.parse(channelBillVO.getEndTime()), channelBillVO.getPageNum(), channelBillVO.getPageSize()));
    }

    @PostMapping("/channelBill/export")
    @ApiOperation(value = "渠道商账单流水导出接口")
    public Response<ExportVO> channelBillDetailsExport(@RequestBody @Validated ChannelBillVO channelBillVO) {
        return Response.ok(channelDistributorsService.channelBillDetailsExport(channelBillVO.getCorpId(), channelBillVO.getUserId(), channelBillVO.getStartTime(), channelBillVO.getEndTime()));
    }

    @GetMapping("/getChannelNewPackageData")
    @ApiOperation(value = "查询渠道商自建套餐数据")
    public Response<ChannelDistributorDetail> getChannelNewPackageData(@RequestParam String corpId) {
        return Response.ok(channelDistributorsService.getChannelNewPackageData(corpId));
    }

    @GetMapping("/getChannelNewPackageGroupId")
    @ApiOperation(value = "查询渠道商自建套餐数据groupId")
    public Response<List<Long>> getChannelNewPackageGroupId(@RequestParam String corpId) {
        return Response.ok(channelDistributorsService.getGroupId(corpId));
    }

    @GetMapping("/getChannelUpccTemplate")
    @ApiOperation(value = "查询渠道商upcc模板id数据")
    public Response<List<String>> getChannelUpccTemplate(@RequestParam String corpId) {
        return Response.ok(channelDistributorsService.getChannelUpccTemplate(corpId));
    }

    @PostMapping("/getCardPoolGroup")
    @ApiOperation(value = "查询渠道商upcc模板id数据")
    public Response<Integer> getCardPoolGroup(@RequestBody List<Long> groupIds) {
        return Response.ok(channelDistributorsService.getCardPoolGroup(groupIds));
    }

    @GetMapping("/judgeChannelCreatePackage")
    @ApiOperation(value = "判断渠道商是否允许自建套餐")
    public Response<Boolean> judgeChannelCreatePackage(@RequestParam String corpId) {
        return Response.ok(channelDistributorsService.judgeChannelCreatePackage(corpId));
    }

    @GetMapping("/getKanbanInfo")
    @ApiOperation(value = "看板信息获取")
    public Response<KanBanDTO> getKanbanInfo(@RequestParam String corpId, @RequestParam String mode) {
        return Response.ok(channelDistributorsService.getKanbanInfo(corpId, mode));
    }

    @GetMapping("/card/suspend")
    @ApiOperation(value = "A2Z卡套餐暂停")
    @OperationLog(operationName = "A2Z套餐暂停", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> cardSuspendedForA2z(@RequestParam String corpId) {
        channelDistributorsService.cardSuspendedForA2z(corpId);
        return Response.ok();
    }

    @GetMapping("/card/recover")
    @ApiOperation(value = "A2Z卡套餐启用")
    @OperationLog(operationName = "A2Z套餐启动", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> cardRecoverForA2z(@RequestParam String corpId) {
        channelDistributorsService.cardRecoverForA2z(corpId);
        return Response.ok();
    }


    @GetMapping("/getPage")
    public Response<List<Channel>> queryPage(@RequestParam(required = false) String corpName,
                                             @RequestParam int pageSize,
                                             @RequestParam int pageNumber) {
        return Response.ok(channelDistributorsService.queryPage(pageNumber, pageSize, corpName));
    }

    @GetMapping("/getCorpDirectional")
    public Response<List<Long>> getCorpDirectional(@RequestParam String corpId) {
        return Response.ok(channelDistributorsService.getCorpDirectional(corpId));
    }

    @PostMapping("/getChargingId")
    Response<List<Long>> getChargingIdByCorpId(@RequestParam String corpId,@RequestParam String cooperationMode) {
        return Response.ok(channelDistributorsService.getChargingIdByCorpId(corpId,cooperationMode));
    }

    @PutMapping("/updateDeposit")
    Response updateDeposit(@RequestBody Map<String, BigDecimal> channelBillMap) {
        channelDistributorsService.updateDeposit(channelBillMap);
        return Response.ok();
    }

    @GetMapping("/getChannelSmsTemplate")
    Response<List<CmsChannelSmsTemplateRelation>> getChannelSmsTemplate(@RequestParam("corpId") String corpId,
                                                                  @RequestParam("cooperationMode") String cooperationMode) {
        return Response.ok(channelDistributorsService.getChannelSmsTemplate(corpId, cooperationMode));
    }

    @GetMapping("/updatePaymentRecord")
    Response<Void> updatePaymentRecord(@RequestParam(name = "date",required = false) String date) {
        channelDistributorsService.uploadPaymentRecord(date);
        return Response.ok();
    }

    @GetMapping("/uploadDepositRecord")
    @ApiOperation(value = "统计前一天的充值完成记录并上传rap")
    public Response<Void> uploadDepositRecord(@RequestParam(name = "date",required = false) String date) {
        channelDistributorsService.uploadDepositRecord(date);
        return Response.ok();
    }
    /**
     * 获取全量渠道商列表，type=1的
     */
    @GetMapping("/getAllChannelList")
    public Response<List<Channel>> getAllChannelList() {
        return Response.ok(channelDistributorsService.getAllChannelList());
    }
}
