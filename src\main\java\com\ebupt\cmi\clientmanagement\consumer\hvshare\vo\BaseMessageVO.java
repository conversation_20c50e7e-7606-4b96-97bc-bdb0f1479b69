package com.ebupt.cmi.clientmanagement.consumer.hvshare.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseMessageVO.java
 * @Description 消息VO基类
 * @createTime 2022年02月28日 17:00:00
 */

@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseMessageVO {
    protected String imsi;

    protected String msisdn;

    protected String iccid;

    protected String himsi;
    /**
     * 卡类型: 1:H  2:V
     */
    protected String cardType;

    /**
     * 套餐唯一ID
     */
    protected String packageUniqueId;
}
