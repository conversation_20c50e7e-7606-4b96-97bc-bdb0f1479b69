package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SettleRuleDetailUpdateDTO.java
 * @Description
 * @createTime 2021年05月11日 20:12:00
 */

@Data
public class SettleRuleDetailUpdateDTO {

    private BigDecimal price;

    private List<String> mcc;
}
