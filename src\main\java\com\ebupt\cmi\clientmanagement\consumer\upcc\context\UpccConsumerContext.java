package com.ebupt.cmi.clientmanagement.consumer.upcc.context;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageDirectionRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpccConsumerContext {

    private String hImsi;

    private String vimsi;

    private String msisdnToH;

    private String msisdnToV;

    private String packageUniqueId;

    private Long appId;

    private PackageDirectionRelation packageDirectionRelation;

    private RetryContext retryContext;

    private boolean hasSetFlow;





}
