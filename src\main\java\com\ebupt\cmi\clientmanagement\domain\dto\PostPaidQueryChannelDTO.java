package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/31 14:53
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PostPaidQueryChannelDTO {
    /**
     * id
     */
    private String corpId;

    /**
     * 后付费渠道名称
     */
    private String corpName;

    /**
     * 付费模式
     */
    private String settleType;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 审批状态
     */
    private String checkStatus;
    /**
     * ebsCode
     */
    private String ebsCode;

    private String companyName;

    private String internalOrder;

    private String address;
}
