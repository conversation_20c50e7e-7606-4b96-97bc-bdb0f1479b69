package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * cms_record_detail_config
 * <AUTHOR>
@Data
public class CmsRecordDetailConfig implements Serializable {
    @TableId
    private Long id;

    private String corpname;

    private String partnerEn;

    private String corpId;

    private String zipPass;

    private Integer fileLineLimit;

    private static final long serialVersionUID = 1L;
}