package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.*;

import java.io.Serializable;

/**
 * (CmsFlowpoolRemain)实体类
 *
 * <AUTHOR>
 * @since 2024-04-08 15:11:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsFlowpoolRemain{
/**
     * 主键
     */
    private Long id;
/**
     * 剩余流量类型1：单卡单周期剩余流量2：单卡全周期剩余流量3：流量池剩余流量
     */
    private String flowType;
/**
     * 归属的流量池唯一id
     */
    private String flowUniqueId;
/**
     * 流量池ID
     */
    private String flowPoolId;
/**
     * imsi
     */
    private String imsi;
/**
     * 剩余流量
     */
    private Long remainFlow;
/**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 剩余流量类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum FlowTypeEnum {

        /**
         * 1：单卡单周期剩余流量
         */
        FLOW_POOL_CYCLE_REMAIN("1"),

        /**
         * 2：单卡全周期剩余流量
         */
        FLOW_POOL_CARD_REMAIN("2"),

        /**
         * 3：流量池剩余流量
         */
        FLOW_POOL_REMAIN("3");

        @Getter
        private String value;

    }
}

