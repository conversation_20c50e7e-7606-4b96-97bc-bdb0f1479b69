package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.reaching;

import com.alibaba.nacos.client.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching.ReachingTreatmentContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.UnSubscribeServiceVO;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.mapper.CmsCardUpccRecordMapper;
import com.ebupt.cmi.clientmanagement.mapper.PackageDirectionRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Component("dReachingReleaseStrategy")
@Slf4j
public class DReachingReleaseStrategy extends AbstractOutsideNetStrategy {

    @Resource
    private PackageDirectionRelationMapper packageDirectionRelationMapper;

    @Override
    protected <T extends BaseContext> boolean tryOutsideNet(T context) {
        try {
            ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;

            for (Long appId : reachingTreatmentContext.getApp()) {

                String upccSignId = null;
                if (reachingTreatmentContext.isNeedLimitSpeed()) {

                    CmsPackageCardUpccRelation cmsPackageCardUpccRelation = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                            .eq(CmsPackageCardUpccRelation::getPackageUniqueId, context.getMessageVO().getPackageUniqueId())
                            .eq(CmsPackageCardUpccRelation::getAppId, appId)
                            .orderByDesc(CmsPackageCardUpccRelation::getConsumption));
                    upccSignId = cmsPackageCardUpccRelation.getUpccSignId();

                }

                log.debug("处理V卡定向应用签约");
                for (String vimsi : reachingTreatmentContext.getVImsi()) {

                    VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                            .getVcardAccountInfo(vimsi));
                    if ("4".equals(vcardInfo.getStatus())) {
                        log.debug("此V卡处于暂停状态，不用处理");
                        continue;
                    }

                    CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                            .getCardPoolByImsi(vimsi));
                    if (org.springframework.util.StringUtils.isEmpty(cardPool.getIsSignUpcc()) || !"1".equals(cardPool.getIsSignUpcc())) {
                        log.debug("此V卡卡池不是UPCC动态签约，不用处理");
                        continue;
                    }

                    hvShareRepository.extracted(reachingTreatmentContext.isNeedLimitSpeed(), appId, upccSignId, vimsi, vcardInfo.getMsisdn());
                    reachingTreatmentContext.setRetryTimes(0);
                }

                log.debug("处理H卡定向应用签约");
                String msisdn = reachingTreatmentContext.getHcardInfo().getMsisdn();
                String imsi = reachingTreatmentContext.getHcardInfo().getImsi();
                hvShareRepository.extracted(reachingTreatmentContext.isNeedLimitSpeed(), appId, upccSignId, imsi, msisdn);
            }

        } catch (Exception e) {

            log.error("调用外部网元时发生致命错误，位置：定向达量释放流程");

            log.error("", e);

            return false;
        }
        return true;
    }



    @Override
    public <T extends BaseContext> void handle(T context) throws InterruptedException {
        ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;

        setHcardIntoContext(reachingTreatmentContext);
        String appGroupId = reachingTreatmentContext.getAppGroupId();
        String packageUniqueId = reachingTreatmentContext.getMessageVO().getPackageUniqueId();

        PackageDirectionRelation packageDirectionRelation = hvShareRepository.getPackageDirectionRelation(appGroupId, packageUniqueId);

        PackageDirectionRelation otherPackageDirectionRelation = packageDirectionRelationMapper.selectOne(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId)
                .eq(PackageDirectionRelation::getDirectType, PackageDirectionRelation.DirectType.FREE_FLOW.getValue())
                .ne(PackageDirectionRelation::getAppGroupId, appGroupId)
                .ne(PackageDirectionRelation::getHasUsed, PackageDirectionRelation.Status.HAS_USED.getValue()));

        ChannelPackageCard channelPackageCard = hvShareRepository.getChannelPackageCardByPackageUniqueId(packageUniqueId);

        if (otherPackageDirectionRelation == null && PackageSurfStatusLog.SurfStatusEnum.LIMIT.getStatus().equals(channelPackageCard.getSurfStatus())) {
            log.debug("套餐和免流定向应用均已使用完毕，释放套餐");
            hvShareRepository.release(channelPackageCard);

        } else {

            if (!vertifyPackageUniqueID(packageUniqueId, reachingTreatmentContext.getHcardInfo().getUpccSignPackageUniqueId())) {
                return;
            }
            reachingTreatmentContext.setNeedCallback(true);

            if (packageDirectionRelation.getIsUsePackage().equals(PackageDirectionRelation.IsUsePackage.YES.getValue()) &&
                    PackageSurfStatusLog.SurfStatusEnum.LIMIT.getStatus().equals(channelPackageCard.getSurfStatus())) {
                log.debug("套餐通用流量已使用完毕且定向应用为继续使用通用流量，删除定向应用签约");
            } else {
                log.debug("套餐通用流量未使用完毕 或者 定向应用不继续使用通用流量 限速此定向应用");
                reachingTreatmentContext.setNeedLimitSpeed(true);
            }


        }
    }

    @Override
    public <T extends BaseContext> void beforeCallBack(T context) {
        Set<String> vimsi = hvShareRepository.getSurfCardByPackageUniqueId(context.getMessageVO().getPackageUniqueId());
        ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;
        String packageUniqueId = reachingTreatmentContext.getMessageVO().getPackageUniqueId();
        Set<Long> app = hvShareRepository.getSurfAppByPackageUniqueId(packageUniqueId, reachingTreatmentContext.getAppGroupId());
        reachingTreatmentContext.setApp(app);
        reachingTreatmentContext.setVImsi(vimsi);
    }
}
