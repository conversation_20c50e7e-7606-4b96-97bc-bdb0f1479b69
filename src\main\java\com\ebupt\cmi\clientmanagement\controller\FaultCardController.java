package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.FaultCardService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/faultCard")
public class FaultCardController {

    @Resource
    private FaultCardService faultCardService;

    @PostMapping("/asyncUpdateVcareStatusByFaultId")
    public Response<Void> asyncUpdateVcareStatusByFaultId(@RequestParam("id") Long id){
        faultCardService.asyncUpdateVcareStatusByFaultId(id);
        return Response.ok();
    }
}
