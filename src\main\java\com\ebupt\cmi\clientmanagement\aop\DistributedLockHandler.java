package com.ebupt.cmi.clientmanagement.aop;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.ebupt.cmi.clientmanagement.annotion.DistributedLock;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.common.operationlog.filter.JSONRequestWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DistributedLockHandler.java
 * @Description aop，实现对自定义注解的处理
 * @createTime 2020年12月22日 16:47:00
 */

@Aspect
@Component
@Slf4j
public class DistributedLockHandler {

    @Autowired
    RedissonLock redissonLock;

    @Autowired
    ObjectMapper objectMapper;

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        log.debug("[开始]执行RedisLock环绕通知,获取Redis分布式锁开始");
        //获取锁名称
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();

        String lockName = distributedLock.keyPrefix();
        if (distributedLock.keySuffixInPath()) {
            final String[] split = request.getRequestURI().split("/");
            final int length = split.length;
            if (length < distributedLock.index()) {
                throw new RuntimeException("参数位置不正确");
            }

            lockName = lockName.concat("_").concat(split[split.length - distributedLock.index()]);
        } else {
            String suffixKey;
            final String[] strings = request.getParameterMap().get(distributedLock.keySuffix());
            if (strings == null || StrUtil.isBlank(strings[0])) {
                final JSONRequestWrapper requestWrapper = new JSONRequestWrapper(request);
                final String body = requestWrapper.getBody();
                Map map = objectMapper.readValue(body, Map.class);
                suffixKey = String.valueOf(map.get(distributedLock.keySuffix()));
                if (StrUtil.isBlank(suffixKey)) {
                    throw new RuntimeException("无法获取key");
                }
            } else {
                suffixKey = strings[0];
            }

            lockName = lockName.concat("_").concat(suffixKey);
        }

        //获取超时时间，默认10秒
        int leaseTime = distributedLock.leaseTime();
        redissonLock.lock(lockName, leaseTime);
        try {
            log.debug("获取Redis分布式锁[成功]，加锁完成，开始执行业务逻辑...");
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            throw throwable;
        } finally {
            //如果该线程还持有该锁，那么释放该锁。如果该线程不持有该锁，说明该线程的锁已到过期时间，自动释放锁
            if (redissonLock.isHeldByCurrentThread(lockName)) {
                redissonLock.unlock(lockName);
            }
        }
    }
}

