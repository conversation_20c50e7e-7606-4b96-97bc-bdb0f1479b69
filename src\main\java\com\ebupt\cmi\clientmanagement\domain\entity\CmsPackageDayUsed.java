package com.ebupt.cmi.clientmanagement.domain.entity;
import lombok.*;


/**
 * (CmsPackageDayUsed)实体类
 *
 * <AUTHOR>
 * @since 2024-04-07 16:18:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsPackageDayUsed {

    private Long id;
/**
     * 已使用流量类型1：套餐周期限量/流量池当日已使用流量2：套餐单日限量当日已使用流量 3：加油包单日限量剩余流量
     */
    private String flowType;
/**
     * 套餐唯一id
     */
    private String packageUniqueId;
/**
     * 单日限量indx值
     */
    private Long dayIndex;
/**
     * 周期限量/流量池统计日期
     */
    private String statTime;
/**
     * 已使用流量
     */
    private Long usedFlow;
/**
     * 过期时间
     */
    private String expireTime;

    public CmsPackageDayUsed(Long id, String flowType, String packageUniqueId, Long usedFlow, String expireTime) {
        this.id = id;
        this.flowType = flowType;
        this.packageUniqueId = packageUniqueId;
        this.usedFlow = usedFlow;
        this.expireTime = expireTime;
    }

    public CmsPackageDayUsed(Long id, String packageUniqueId, Long usedFlow, String expireTime) {
        this.id = id;
        this.packageUniqueId = packageUniqueId;
        this.usedFlow = usedFlow;
        this.expireTime = expireTime;
    }


    /**
     * 已使用流量类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum FlowTypeEnum {

        /**
         * 1：套餐周期限量/流量池当日已使用流量
         */
        CYCLE_OR_FLOWPOOL_REMAIN("1"),

        /**
         * 2：套餐单日限量当日已使用流量
         */
        PACKAGE_REMAIN("2"),

        /**
         * 3：加油包单日限量剩余流量
         */
        ADD_REMAIN("3");

        @Getter
        private String value;

    }
}

