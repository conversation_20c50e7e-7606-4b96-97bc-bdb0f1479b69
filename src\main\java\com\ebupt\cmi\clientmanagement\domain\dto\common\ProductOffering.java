package com.ebupt.cmi.clientmanagement.domain.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 18:52
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductOffering {
    private String orderItemID;

    private String productOfferingID;

    private String iccid;

    private String packageID;

    private String packageName;

    private String packageDesc;

    private String startTime;

    private String endTime;
}
