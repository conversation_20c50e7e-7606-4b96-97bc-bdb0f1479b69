package com.ebupt.cmi.clientmanagement.consumer.context;

import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FlowPoolConsumerContext.java
 * @Description
 * @createTime 2022年01月12日 17:23:00
 */

@Builder
@Data
public class FlowPoolConsumerContext {

    FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessage;

    /**
     * 重试
     */
    Integer retryTimes;

    /**
     * 当前客户与卡关系表实体
     */
    ChannelCard currentChannelCard;

    /**
     * 储存为了回写
     */
    Long sucLogId;

    HcardInfo hcardInfo;

    HimsiStatusAndLocationDTO himsiStatusAndLocationDTO;

    /**
     * 优先级，仅针对限速和停用流程
     * 2022/03/16 所有流程都需要，还好之前抽出来了，不然要写吐
     * 1. 限速
     * 2. 停用
     */
    int priorityFromTable;

    int priorityFromMessage;

    String upccSignId;
}
