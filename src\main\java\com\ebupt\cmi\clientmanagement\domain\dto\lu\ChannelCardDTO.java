package com.ebupt.cmi.clientmanagement.domain.dto.lu;

import lombok.Data;

/**
 * <AUTHOR>
 * @description cms_channel_card + cms_channel
 * @date 2021/5/26 10:35
 */
@Data
public class ChannelCardDTO {

    // 主卡相关变量
    /**
     * cms_channel_card主键
     */
    private Long channelCardId;

    /**
     * 主卡类型
     */
    private String cardType;

    /**
     * 卡池id
     */
    private String poolId;

    /**
     * 卡形态
     */
    private String cardForm;

    private String iccid;
    private String msisdn;

    // 客户相关变量
    /**
     * 客户id
     */
    private String corpId;

    /**
     * 客户类型
     */
    private String channelType;

    /**
     * 结算类型
     */
    private String settleType;
    /**
     * 结算规则
     */
    private String settleRule;

    /**
     * 扣费类型
     */
    private String billType;

    /**
     * 扣费规则
     */
    private String billRule;

    /**
     * 流量池ID
     */
    private String flowPoolId;

    /**
     * 当前类型：
     * 1：正常
     * 2：单卡周期达量限速
     * 3：单卡周期达量停用
     * 4：单卡总量达量限速
     * 5：单卡总量达量停用
     * 6：流量池达量限速
     * 7：流量池达量停用
     */
    private String currentRateType;


    private String flowPoolStatus;

    private String cooperationMode;

    private Long groupId;

    private String parentCorpId;

}
