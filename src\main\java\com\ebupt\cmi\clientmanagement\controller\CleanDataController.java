package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.CleanDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/clean")
@Api(tags = "清理过期数据")
public class CleanDataController {
    @Autowired
    private CleanDataService cleanupDataService;

    @ApiOperation("清理流量池剩余流量过期数据")
    @PostMapping("/cleanFlowPool")
    public Response cleanFlowPool( ) {
        cleanupDataService.cleanFlowPool();
        return Response.ok();
    }

    @ApiOperation("清理套餐剩余流量过期数据")
    @PostMapping("/cleanPackageRemain")
    public Response cleanPackageRemain( ) {
        cleanupDataService.cleanPackageCycleRemain();
        cleanupDataService.cleanPackageDayRemain();
        return Response.ok();
    }

    @ApiOperation("清理套餐已使用流量过期数据")
    @PostMapping("/cleanPackageUsed")
    public Response cleanPackageUsed( ) {
        cleanupDataService.cleanPackageUsed();
        return Response.ok();
    }

    @ApiOperation("清理流量池流量扣减成功下发邮件提醒和已分配配额")
    @PostMapping("/cleanFoolPoolQuotaAndRemind")
    public Response cleanFoolPoolQuotaAndRemind( ) {
        cleanupDataService.cleanFoolPoolQuota();
        cleanupDataService.cleanFoolPoolRemind();
        return Response.ok();
    }

}
