package com.ebupt.cmi.clientmanagement.domain.dto.Context;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelDistributorDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelBlankcardOrder;
import com.ebupt.cmi.clientmanagement.domain.vo.BlankCardDeliverVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.ChannelCardMapper;
import com.ebupt.cmi.clientmanagement.utils.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/5 16:45
 */

@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlankCardDeliverContext {
    /**
     * 文件写锁
     */
    public final Object WRITE_LOCK = new Object();

    /**
     * 单例文件锁
     */
    private final Object GENERATE_FILE_LOCK = new Object();

    private PmsFeignClient pmsFeignClient;

    private ChannelCardMapper channelCardMapper;

    private BlankCardDeliverVO vo;

    private String failFilePathPrefix;

    private CmsChannelBlankcardOrder order;

    private ChannelDistributorDetail channelDistribute;

    private volatile File errorlogFile;

    private volatile File esimCardListFile;

    private volatile File imsiCardListFile;

    /**
     * 上传发货iccid文件读出来的
     */
    private List<String> iccids;

    /**
     * 拆出来的2000个iccid
     */
    private List<List<String>> groupIccids;

    private CountDownLatch countDownLatch;

    public void clearIccids() {
        this.iccids = null;
    }

    public void clearMultipartFile() {
        this.vo.clearMultipartFile();
    }

    public File getErrorLogFileInstance() {
        if (this.errorlogFile == null) {
            synchronized (this.GENERATE_FILE_LOCK) {
                if (this.errorlogFile == null) {
                    String fileName = "failFile_".concat(DateTimeUtil.getNowTime()).concat(".csv");
                    final String failfilePath = failFilePathPrefix.concat(fileName);
                    this.errorlogFile = new File(failfilePath);
                    if (!this.errorlogFile.getParentFile().exists() && !this.errorlogFile.getParentFile().mkdirs()) {
                        log.error("创建错误文件父文件夹失败");
                    }
                }
            }
        }

        return this.errorlogFile;
    }

    public File getEsimCardListFileInstance() throws Exception {
        if (this.esimCardListFile == null) {
            synchronized (this.GENERATE_FILE_LOCK) {
                if (this.esimCardListFile == null) {
                    String fileName = "ESIM白卡订单号码".concat(DateTimeUtil.getNowTime()).concat(".csv");
                    final String failfilePath = failFilePathPrefix.concat(fileName);
                    this.esimCardListFile = new File(failfilePath);
                    if (!this.esimCardListFile.getParentFile().exists() && !this.esimCardListFile.getParentFile().mkdirs()) {
                        log.error("创建esim卡文件父文件夹失败");
                    }
                    try (FileOutputStream fileOutputStream = new FileOutputStream(this.esimCardListFile)) {
                        fileOutputStream.write("IMSI,ICCID,MSISDN,PIN,PUK,ESIM_URL\n".getBytes());
                    }
                }
              }
        }

        return this.esimCardListFile;
    }

    public File getImsiCardListFileInstance() throws Exception {
        if (this.imsiCardListFile == null) {
            synchronized (this.GENERATE_FILE_LOCK) {
                if (this.imsiCardListFile == null) {
                    String fileName = "IMSI白卡订单号码".concat(DateTimeUtil.getNowTime()).concat(".csv");
                    final String failfilePath = failFilePathPrefix.concat(fileName);
                    this.imsiCardListFile = new File(failfilePath);
                    if (!this.imsiCardListFile.getParentFile().exists() && !this.imsiCardListFile.getParentFile().mkdirs()) {
                        log.error("创建imsi卡文件父文件夹失败");
                    }
                    try (FileOutputStream fileOutputStream = new FileOutputStream(this.imsiCardListFile)) {
                        fileOutputStream.write("IMSI,ICCID,MSISDN,PIN,PUK,ESIM_URL\n".getBytes());
                    }
                }
            }
        }

        return this.imsiCardListFile;
    }
}
