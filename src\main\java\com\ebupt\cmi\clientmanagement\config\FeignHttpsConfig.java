package com.ebupt.cmi.clientmanagement.config;

import cn.hutool.core.net.DefaultTrustManager;
import feign.Client;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.ssl.SSLContexts;
import org.springframework.cloud.netflix.ribbon.SpringClientFactory;
import org.springframework.cloud.openfeign.ribbon.CachingSpringLoadBalancerFactory;
import org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient;
import org.springframework.context.annotation.Bean;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * Feign支持HTTPs配置
 * <AUTHOR>
 * @date 2021-7-20 13:03:53
 */
public class FeignHttpsConfig {

    @Bean
    public Client feignClient(CachingSpringLoadBalancerFactory cachingFactory,
                              SpringClientFactory clientFactory)
            throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        SSLContext ctx = SSLContexts.custom().loadTrustMaterial(null, TrustSelfSignedStrategy.INSTANCE).build();
        ctx.init(null, new TrustManager[]{ new DefaultTrustManager() }, null);
        final Client.Default client = new Client.Default(ctx.getSocketFactory(), NoopHostnameVerifier.INSTANCE);
        return new LoadBalancerFeignClient(client, cachingFactory, clientFactory);
    }

}
