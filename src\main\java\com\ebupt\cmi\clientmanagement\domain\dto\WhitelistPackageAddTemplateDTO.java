package com.ebupt.cmi.clientmanagement.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 白名单套餐DTO
 * @date 2023-03-13
 */
@Data
@ApiModel(value = "白名单套餐文件导入模板-新增")
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT)
public class WhitelistPackageAddTemplateDTO implements Serializable {

    @ExcelProperty(value = "ICCID", index =0)
    private String iccId;

    @ExcelProperty(value = "套餐ID", index = 1)
    private String packageId;

} 