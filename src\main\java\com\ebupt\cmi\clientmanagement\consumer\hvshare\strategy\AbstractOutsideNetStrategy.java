package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.delay.DelayContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packageSuspendContext.SuspendAndRecoverContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packagecontext.PackageContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsPackageCardUpccRelation;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageCardUpccRelationMapper;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AbstractReachingTreatmentStrategy.java
 * @Description 外部网元交互抽象类
 * @createTime 2022年02月28日 16:11:00
 */

@Slf4j
public abstract class AbstractOutsideNetStrategy extends AbstractStrategy {

    @Value("${flowPool.rabbit.resetTime}")
    protected Integer resetTime;

    @Value("${flowPool.rabbit.retryInterval}")
    protected Integer retryInterval;

    @Autowired
    protected HvShareRepository hvShareRepository;

    @Resource
    public PmsFeignClient pmsFeignClient;

    @Autowired
    protected ControlFeignClient controlFeignClient;

    @Autowired
    protected CCRCommonService ccrCommonService;

    @Resource
    public CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    @Resource
    public CoreNetCaller coreNetCaller;

    @Override
    public boolean vertifyPackageUniqueID(String packageUniqueIDFromMessage, String packageUniqueIDFromTable) {
        return packageUniqueIDFromMessage.equals(packageUniqueIDFromTable);
    }

    public void setHcardIntoContext(BaseContext context) {
        HcardInfo hcardInfo = hvShareRepository.getHcard(context.getMessageVO().getHimsi());
        context.setHcardInfo(hcardInfo);
    }

    /**
     * 就是单纯的跟外部网元交互而已
     *
     * @param context 上下文
     */
    protected abstract <T extends BaseContext> boolean tryOutsideNet(T context);

    @Override
    public void callBack(BaseContext context) throws InterruptedException {

        boolean isOutSideNetSuc;

        isOutSideNetSuc = tryOutsideNet(context);

        while (!isOutSideNetSuc) {

            if (retryInterval > 0) {
                Thread.sleep(retryInterval);
            }

            int retryTime = context.getRetryTimes();

            if (retryTime >= resetTime) {

                if (context instanceof DelayContext) {
                    if (context.getMessageVO().getCardType().equals(CardTypeEnum.V_CARD.getType())) {
                        hvShareRepository.updateVcardUpccSignStatus(context.getMessageVO().getImsi());
                        delayContextVCardRar(context);
                    }
                }

                throw new BizException("与外部网元交互的次数超过限制，请注意核心网状态");

            }

            context.setRetryTimes(++retryTime);

            isOutSideNetSuc = tryOutsideNet(context);

        }

        if (context instanceof DelayContext) {
            delayContextVCardRar(context);
        }

    }

    /**
     * 延时恢复队列V卡RAR流程
     *
     * @param context 上下文信息
     */
    public void delayContextVCardRar(BaseContext context) {
        DelayContext delayContext = (DelayContext) context;
        ChannelPackageCard channelPackageCard = delayContext.getChannelPackageCard();
        if (channelPackageCard != null && "2".equals(channelPackageCard.getPackageUseStatus())){
            log.info("套餐使用状态为暂停，v卡RAR流程结束。");
            return;
        }
        SingleDayDelayVO messageVO = (SingleDayDelayVO) delayContext.getMessageVO();
        if (!"2".equals(messageVO.getCardType())) {
            return;
        }
        log.info("==================[V卡RAR流程-开始]====================");
        String packageUniqueIDFromTable = delayContext.getHcardInfo().getUpccSignPackageUniqueId();
        String packageUniqueIDFromMessage = delayContext.getMessageVO().getPackageUniqueId();
        //如果二者不相等，不用进行外部的交互
        if (!vertifyPackageUniqueID(packageUniqueIDFromMessage, packageUniqueIDFromTable)) {
            log.info("==========================该卡使用的不是传入的套餐，不做处理=============================");
            return;
        }
        if (ccrCommonService.judgePgwSessionExists(messageVO.getImsi())) {
            log.info("==================触发V卡RAR====================");
            controlFeignClient.sendRAR(messageVO.getImsi());
        }
        log.info("==================[V卡RAR流程-结束]====================");
    }
}
