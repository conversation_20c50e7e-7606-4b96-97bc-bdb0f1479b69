package com.ebupt.cmi.clientmanagement.consumer.hvshare;

import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.ContextUtil;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packagecontext.PackageContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching.ReachingTreatmentContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.factory.HVShareStrategyFactory;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.BaseMessageVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.PackageDelayMessageVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.ReachingTreatmentVO;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageDelayQueueConsumer.java
 * @Description 套餐超时回滚队列消费者
 * @createTime 2022年03月07日 15:00:00
 */

@Component
@RabbitListener(queues = "packageExpire.delay.queue")
@Slf4j
public class PackageDelayQueueConsumer {

    @Autowired
    CommonRepository commonRepository;

    @Autowired
    HVShareStrategyFactory factory;

    @RabbitHandler
    public void process(String messageString, Channel channel, Message message) throws IOException, InterruptedException {

        log.info("============================rabbitMQ收到消息{}==================================", messageString);

        try {
            if (commonRepository.getMessageById(message.getMessageProperties().getMessageId())){
                log.debug("该消息已被处理");
                return;
            }

            PackageDelayMessageVO messageVO =
                    JSON.parseObject(messageString, PackageDelayMessageVO.class);

            PackageContext context = (PackageContext) ContextUtil
                    .getContext(PackageContext.class);

            //给上下文设置值
            context.setMessageVO(messageVO);

            context.setQueueEnum(QueueEnum.PackageExpireDelayQueue);


            AbstractStrategy strategy;

            strategy = factory
                    .getStrategy("packageDelayStrategy");

            assert strategy != null;

            strategy.handle(context);
        } catch (Exception e) {
            log.warn("套餐超时回滚失败, {}", e);
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            commonRepository.deleteMessage(message.getMessageProperties().getMessageId());
        }
    }


}
