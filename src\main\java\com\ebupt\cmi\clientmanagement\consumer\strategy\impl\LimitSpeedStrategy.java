package com.ebupt.cmi.clientmanagement.consumer.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.utils.StringUtils;
import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.strategy.AbstractFlowPoolConsumerStrategy;
import com.ebupt.cmi.clientmanagement.domain.entity.flowpool.CmsFlowpoolLimitLog;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import com.ebupt.cmi.clientmanagement.feign.control.domain.hss.CancelLocationVO;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import com.ebupt.cmi.clientmanagement.mapper.flowpool.CmsFlowpoolLimitLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LimitSpeedStrategy.java
 * @Description 限速策略
 * @createTime 2022年01月12日 16:43:00
 */

@Component("limitSpeed")
@Slf4j
public class LimitSpeedStrategy extends AbstractFlowPoolConsumerStrategy {
    @Autowired
    CmsFlowpoolLimitLogMapper cmsFlowpoolLimitLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = GoodException.class)
    public void handle(FlowPoolConsumerContext flowPoolConsumerContext) {

        log.info("==============限速流程开始，卡{}================",
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());

        super.handle(flowPoolConsumerContext);

        //(3)入库【cms_flowpool_limit_log】限速日志记录表
        insertIntoLimitLog(flowPoolConsumerContext);

        //callBack(flowPoolConsumerContext);

        log.info("==============限速流程结束，卡{}================",
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());


    }

    /**
     * (3)入库【cms_flowpool_limit_log】限速日志记录表
     *
     * @param flowPoolConsumerContext
     */
    void insertIntoLimitLog(FlowPoolConsumerContext flowPoolConsumerContext) {

        FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessageVO =
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage();

        String currentType = flowPoolRabbitMQMessageVO.getCurrentRateType();

        //类型
        //1、卡
        //2、流量池
        String corpId = "1";

        if ("6".equals(currentType)) {
            corpId = "2";
        }

        CmsFlowpoolLimitLog cmsFlowpoolLimitLog =
                CmsFlowpoolLimitLog
                        .builder()
                        .corpId(corpId)
                        .createTime(LocalDateTime.now())
                        .flowPoolUniq(flowPoolRabbitMQMessageVO.getFlowPoolUniqueId())
                        .limitId(flowPoolRabbitMQMessageVO.getImsi())
                        .build();

        cmsFlowpoolLimitLogMapper.insert(cmsFlowpoolLimitLog);

    }

    /**
     * Upcc/HSS做交互
     *
     * @param flowPoolConsumerContext
     */
    @Override
    public boolean tryOutsideNet(FlowPoolConsumerContext flowPoolConsumerContext) {
        try {

            log.info("========================这是第{}次进行外部网元交互重试============================",
                    flowPoolConsumerContext.getRetryTimes());

            FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                    .getFlowPoolRabbitMQMessage();

            HcardInfo hcardInfo = flowPoolConsumerContext.getHcardInfo();

            boolean useFlowPool = useFlowPool(flowPoolConsumerContext);

            //若不相等，什么操作都不做
            if (useFlowPool) {

                log.info("==========================该卡使用的是流量池=============================");

                /**
                 * 卡类型: 1:H  2:V，如果是V卡，查询V卡表，查询对应卡池 is_sign_upcc字段判断是否动态签约
                 */
                if ("2".equals(messageVO.getCardType())) {
                    VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                            .getVcardAccountInfo(messageVO.getImsi()));
                    log.info("查询v卡信息：{}", JSONObject.toJSONString(vcardInfo));

                    CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                            .getCardPoolByImsi(messageVO.getImsi()));
                    log.info("查询卡池信息：{}", JSONObject.toJSONString(cardPool));

                    //is_sign_upcc 是否去UPCC动态签约;1 true|0 false,注意该字段可能为空
                    log.info("cardPool.getIsSignUpcc:{}", cardPool.getIsSignUpcc());
                    if (StringUtils.isNotBlank(cardPool.getIsSignUpcc()) &&
                            "1".equals(cardPool.getIsSignUpcc())) {

                        LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                                .usrIdentifier(vcardInfo.getMsisdn())
                                .srvName(messageVO.getUpccLimitsSignId())
                                .build();
                        log.info("UPCC动态签约：{}", JSONObject.toJSONString(loadSubscribe));
                        Response.getAndCheckRemoteData(controlFeignClient
                                .subscribeService(loadSubscribe));

                    }

                    //H卡直接upcc签约
                } else {

                    LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                            .usrIdentifier(hcardInfo.getMsisdn())
                            .srvName(messageVO.getUpccLimitsSignId())
                            .build();

                    Response.getAndCheckRemoteData(controlFeignClient
                            .subscribeService(loadSubscribe));

                    //todo upcc已重新签约不需要发cancel

                }

            } else {
                log.info("==========================该卡使用的不是流量池，不做处理=============================");
            }


            log.info("=======================非常幸运，通过了，限速外部网元交互流程===========================");

            return true;
        } catch (Exception ex) {

            log.error("调用外部网元时发生致命错误，位置：限速流程");

            log.error("", ex);

            return false;

        }

    }

}
