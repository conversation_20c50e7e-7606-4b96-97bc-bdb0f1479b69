package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * @ description
 * @ author yhd
 * @ since 2025/6/11 17:26
 */
@Data
@ToString
@TableName(value = "cms_invoice_audit_record")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsInvoiceAuditRecord implements Serializable {

    private Long id;

    private String operationType;

    private String operateUser;

    private String dispositionComments;

    private String procUniqueId;

    private Date operateTime;
}

