package com.ebupt.cmi.clientmanagement.domain.entity;

import cn.hutool.log.Log;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_iccid_usedflow")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IccidUsedFlow extends BaseEntity {

    private String imsi;

    /**
     * 流量池单周期总量，MB
     */
    private BigDecimal usedFlow;


}