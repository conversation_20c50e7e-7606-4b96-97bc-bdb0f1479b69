package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/01/17 10:00
 */

@Data
@Builder
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class FlowpoolCardListDTO {

    /**
     * 流量池id
     */
    private String flowPoolId;

    /**
     * 流量池名称
     */
    private String flowPoolName;

    /**
     * iccid卡列表信息
     */
    private List<IccidCardDTO> iccidCardList;
}
