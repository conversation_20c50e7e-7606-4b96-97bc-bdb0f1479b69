package com.ebupt.cmi.clientmanagement.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "channel-contract")
public class ChannelContractConfig {
    private int beforeExpire;

    private int afterExpire;

    private int a2zBeforeExpire;

    private int a2zAfterExpire;

    private List<String> carbonCopyMail;

    private Mail mail;

    public static String consignmentMailKey = "channelcontractmailconsignment_%s";

    public static String a2zKey = "channelcontractmaila2z_%s";

    @Data
    public static class Mail {
        private Consignment consignment;
        private A2z a2z;
    }

    @Data
    public static class Consignment {
        private String title;
        private String content;
    }

    @Data
    public static class A2z {
        private String title;
        private String content;
    }
}
