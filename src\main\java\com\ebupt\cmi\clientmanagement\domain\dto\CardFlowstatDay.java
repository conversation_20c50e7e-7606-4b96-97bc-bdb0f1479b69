package com.ebupt.cmi.clientmanagement.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CardFlowstatDay {
    private Long id;
    private String statTime;
    private String imsi;
    private String msisdn;
    private String iccid;
    private String himsi;
    private String mcc;
    private String countryCn;
    private String countryTw;
    private String countryEn;
    private String plmnList;
    private String operatorName;
    private String corpId;
    private String packageId;
    private String packageName;
    private String nameTw;
    private String nameEn;
    private String packageUniqueId;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "下行流量，单位：G")
    private Long flowDownlink;
    @ApiModelProperty(value = "上行流量，单位：G")
    private Long flowUplink;
    @ApiModelProperty(value = "流量总量，单位：G")
    private Long flowByte;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activeTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    private String rg;

    @TableField(exist = false)
    private String corpName;

    @TableField(exist = false)
    @ApiModelProperty(value = "套餐使用流量总量，单位")
    private String flowByteTotal;

    @TableField(exist = false)
    @ApiModelProperty(value = "套餐实际有使用的天数")
    private Integer useDays;

    @TableField(exist = false)
    @ApiModelProperty(value = "套餐可使用天数，套餐结束时间-套餐开始时间")
    private String validDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "卡类型")
    private String cardType;

    @TableField(exist = false)
    @ApiModelProperty(value = "MNC")
    private String mnc;

    private String isUseCommon;


}
