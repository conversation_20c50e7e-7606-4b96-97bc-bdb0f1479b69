package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ebupt.cmi.clientmanagement.domain.vo.UsedFlow;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.data.annotation.Transient;


import java.util.Date;


@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "cms_channel_record_unknown")
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel
public class ChannelRecordUnknown extends BaseEntity {
    //   @ApiModelProperty(value = "使用日期")
//   @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    private String useDate;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    private String packageUniqueId;

    private String packageId;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @Transient
    private transient String packageNameEn;

    @ApiModelProperty(value = "IMSI")
    private String imsi;

    private String msisdn;

    private String iccid;

    @ApiModelProperty(value = "上网方式1H 2V")
    private String internetType;

    private String himsi;

    @ApiModelProperty(value = "总流量byte(上行流量+下行流量)")
    private Long flowCount;

    @ApiModelProperty(value = "繁体中文名称")
    private String nameTw;

    @ApiModelProperty(value = "英文名称")
    private String nameEn;

    @ApiModelProperty(value = "下行流量byte")
    private Long flowDownlink;

    @ApiModelProperty(value = "上行流量byte")
    private Long flowUplink;

    @ApiModelProperty(value = "国家-简体中文")
    private String countryCn;

    @ApiModelProperty(value = "国家-繁体中文")
    private String countryTw;

    @ApiModelProperty(value = "国家-英文")
    private String countryEn;

    @ApiModelProperty(value = "MCC+MNC")
    private String plmnlist;


    @ApiModelProperty(value = "国家")
    private String mcc;

    @ApiModelProperty(value = "国家英文")
    @Transient
    private transient String mccEn;

    private String tapcode;

    private String apn;

    @ApiModelProperty(value = "套餐状态")
    private String packageStatus;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("运营商名称")
    private String operatorName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    private String corpId;


    /**
     * 流量类型
     * 1：高速流量
     * 2：限速流量
     */
    @ApiModelProperty("流量类型")
    private String flowType;


    @ApiModelProperty(value = "应用rg值")
    private String rg;

    @ApiModelProperty(value = "网络类型")
    private String netType;


    /**
     * 定向流量是否已使用通用流量：
     * 1：是
     * null：否
     */
    @ApiModelProperty(value = "定向流量是否已使用通用流量")
    private String isUseCommon;

}
