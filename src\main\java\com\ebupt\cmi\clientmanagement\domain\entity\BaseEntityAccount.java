package com.ebupt.cmi.clientmanagement.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * BaseEntity
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>n
 * @Date: 2021/5/8 10:45
 */
@Data
@ToString
public class BaseEntityAccount {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
