package com.ebupt.cmi.clientmanagement.consumer.hvshare.vo;


import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReachingTreatmentVO.java
 * @Description 达量处理消息VO
 * @createTime 2022年02月28日 14:40:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ReachingTreatmentVO extends BaseMessageVO{

    /**
     * 通用流量限速签约模板ID（达量限速时不可以为空）,定向应用得签约id队列自己查
     */
    protected String upccLimitsSignId;

    /**
     * 当前类型：
     * 限速\释放（1：达量限速 2：达量释放）
     */
    protected String logic;

    protected List<String> appGroupId;
}
