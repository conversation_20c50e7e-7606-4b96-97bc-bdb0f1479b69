package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * MCC与OTA MCC关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsMccOtaRelation对象", description="MCC与OTA MCC关联表")
public class CmsMccOtaRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "上报MCC")
    private String mcc;

    @ApiModelProperty(value = "OTA下发MCC")
    private String otaMcc;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
