package com.ebupt.cmi.clientmanagement.controller.flowpool;

import com.ebupt.cmi.clientmanagement.job.chain.BaseFlowResetChain;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FlowPoolResetController.java
 * @Description 流量池重置controller
 * @createTime 2022年01月11日 10:31:00
 */

@Slf4j
@RestController
@RequestMapping("/flowPoolReset")
@Api(tags = "流量池重置Controller")
public class FlowPoolResetController {

    @Autowired
    BaseFlowResetChain cardAndFlowTotalChain;

    @Resource
    private Executor luExecutor;

    /**
     * 流量池重置
     *
     * @param type 1.旧周期 2.新周期
     */
    @GetMapping("/reset")
    @ApiOperation(value = "流量池重置", notes = "流量池重置")
    @NormalLog
    public void resetFlowPool(@RequestParam String type) {
        log.info("==============================开启流量池周期总流程================================");
        luExecutor.execute(() -> cardAndFlowTotalChain.next(type));

    }
}
