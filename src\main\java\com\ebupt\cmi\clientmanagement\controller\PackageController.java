package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.ActiveSpecificPackageVO;
import com.ebupt.cmi.clientmanagement.service.PackageService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-5-17 11:00:52
 * @description 套餐相关接口
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("package")
@Api(tags = "套餐相关接口")
public class PackageController {

    private final PackageService packageService;

    /**
     * 查询客户可购买套餐组id列表
     *
     * @return
     */
    @ApiOperation("查询客户可购买套餐组id")
    @GetMapping("/queryGroupIds/{corpId}")
    public Response<List<String>> queryGroupIds(@PathVariable String corpId) {
        return Response.ok(packageService.queryGroupIdsByCorpId(corpId));
    }

    /**
     * 查询客户可购买套餐
     *
     * @return
     */
    @ApiOperation("查询客户可购买套餐")
    @GetMapping("/queryPackages/{corpId}")
    public Response<Map<String, List<String>>> queryPackageIds(@PathVariable String corpId) {
        return Response.ok(packageService.queryPackagesByCorpId(corpId));
    }

    /**
     * 激活指定已购买的套餐，包含使用之前已经激活的套餐
     *
     * @return
     */
    @ApiOperation("激活指定已购买套餐")
    @PostMapping("activeSpecific")
    public Response activeSpecific(@RequestBody ActiveSpecificPackageVO activeSpecificPackageVO) {
        packageService.activePackage(activeSpecificPackageVO);
        return Response.ok();
    }

    @ApiOperation("获取自建套餐组")
    @GetMapping("/selfPackageGroup/{corpId}")
    public Response<String> selfPackageGroup(@PathVariable("corpId") String corpId) {
        return Response.ok(packageService.selfPackageGroup(corpId));
    }

    @ApiOperation("刷新套餐模板")
    @PutMapping("/refreshPackageSign")
    public Response<String> countPackages(@RequestParam String packageId, @RequestParam String highSpeedTemplate,
                                          @RequestParam String limiteSpeedTemplate) {
        try {
            packageService.refreshPackageSign(packageId, highSpeedTemplate, limiteSpeedTemplate);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("修改套餐指定时间激活接口")
    @PostMapping("/updatePackageActiveTime")
    @OperationLog(operationName = "修改套餐指定激活日期", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> updatePackageActiveTime(@RequestParam String packageUniqueId, @RequestParam Date newActiveTime) {
        packageService.updatePackageActiveTime(packageUniqueId, newActiveTime);
        return Response.ok();
    }

    @ApiOperation("【外部API】修改套餐指定时间激活接口")
    @PostMapping("/updatePackageActiveTimeForApi")
    public Response<Void> updatePackageActiveTime(@RequestParam String iccid, @RequestParam String packageId,
                                                  @RequestParam String orderId, @RequestParam String newActiveTime) {
        packageService.updatePackageActiveTime(iccid,packageId,orderId, newActiveTime);
        return Response.okForApi("0");
    }
}
