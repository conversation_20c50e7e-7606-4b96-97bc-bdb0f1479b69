# 渠道修改接口业务逻辑分析报告

## 1. 接口基本信息

- **接口名称**：渠道修改
- **HTTP方法**：PUT
- **请求路径**：`/channel/updateChannel`
- **功能描述**：用于修改已存在的渠道商（合作商）信息，包括基本信息、合作模式、套餐、押金、通知配置等。
- **业务目标**：确保渠道商信息的合规变更，支持多合作模式、套餐、规则等复杂业务场景，保障数据一致性和业务约束。

---

## 2. 输入输出规范

### 2.1 请求参数

- **请求体类型**：JSON
- **参数对象**：`UpdateChannelVO`（继承自`NewChannelVO`）

#### 主要字段及释义

| 字段名 | 类型 | 必填 | 释义 |
|--------|------|------|------|
| corpId | String | 是 | 渠道ID |
| corpName | String | 是 | 厂商名称 |
| channelStatus | String | 是 | 渠道商状态 |
| ebsCode | String | 是 | EBS编码 |
| deposit | BigDecimal | 是 | 押金金额 |
| currencyCode | String | 是 | 货币种类 |
| mail | String | 是 | 联系人邮箱 |
| createAccountNumber | Integer | 否 | 创建账户数量 |
| depositNotify | BigDecimal | 是 | 可用金额提醒阈值 |
| packageInfos | List | 否 | 可购套餐组 |
| depositeReset | String | 否 | 是否重置押金 |
| resetPrice | BigDecimal | 否 | 重置金额 |
| discount | Integer | 否 | 折扣 |
| contractBeginTime | Date | 是 | 合约开始时间 |
| contractEndTime | Date | 是 | 合约结束时间 |
| contractSellAmount | BigDecimal | 是 | 合约期间承诺销售金额 |
| companyName | String | 否 | 公司名称 |
| internalOrder | String | 否 | 内部订单 |
| address | String | 否 | 地址 |
| activateNotification | String | 是 | 激活通知开关 |
| channelType | String | 是 | 渠道商类型 |
| a2zChannelType | String | 是 | A2Z渠道类型 |
| unsubscribeRule | String | 是 | 退订规则 |
| activateNotificationUrl | String | 是 | 激活通知URL |
| totalDeposit | BigDecimal | 否 | 总额度 |
| allowNewPackage | String | 否 | 是否允许自建套餐 |
| limitPacakageNum | Integer | 否 | 自建套餐上限 |
| upccTemplateIds | List | 否 | upcc模板id |
| runoutofBalanceRemindThreshold | BigDecimal | 否 | 余额用尽提醒阈值 |
| stopUseRemindThreshold | BigDecimal | 否 | 停止使用提醒阈值 |
| prohibitiveBuyRemindThreshold | BigDecimal | 否 | 禁止购买提醒阈值 |
| channelCooperationMode | List | 否 | 渠道合作模式（如A2Z、资源合作等）|
| appids | List | 否 | 应用组id集合 |
| ... | ... | ... | 详见VO定义 |

> 详细字段及释义请参考`NewChannelVO.java`和`UpdateChannelVO.java`。

### 2.2 响应数据

- **成功**：`Response.ok()`，无数据体
- **失败**：`Response.error(String message)`，返回错误信息

---

## 3. 业务流程图

```mermaid
flowchart TD
    A[前端发起PUT请求<br/>/channel/updateChannel] --> B[ChannelController.updateChannel]
    B --> C[ChannelService.updateChannel]
    C --> D[ChannelServiceImpl.updateChannel]
    D --> E[参数校验/业务校验]
    E --> F[数据库操作<br/>（渠道主表、明细表、套餐、规则等）]
    F --> G[更新缓存/通知]
    G --> H[返回结果]
```

---

## 4. 核心组件清单

- **Controller**：`ChannelController`
  - 方法：`public Response updateChannel(@RequestBody UpdateChannelVO updateChannelVO)`
- **Service**：`ChannelService`
  - 方法：`void updateChannel(UpdateChannelVO updateChannelVO)`
- **ServiceImpl**：`ChannelServiceImpl`
  - 方法：`public void updateChannel(UpdateChannelVO updateChannelVO)`
- **VO**：`UpdateChannelVO`、`NewChannelVO`
- **Entity**：`ChannelDistributorDetail`（渠道商明细）、`Channel`（主表）
- **Mapper**：`ChannelDistributorDetailMapper`、`ChannelMapper`
- **数据库表**：
  - `cms_channel`（主表）
  - `cms_channel_distributors_detail`（明细表）
  - 相关套餐、规则、关系表等

---

## 5. 数据库设计

### 5.1 主要表结构

#### cms_channel（渠道商主表）

| 字段名 | 类型 | 释义 |
|--------|------|------|
| corp_id | varchar | 渠道ID（主键）|
| corp_name | varchar | 厂商名称 |
| ebs_code | varchar | EBS编码 |
| ... | ... | ... |

#### cms_channel_distributors_detail（渠道商明细表）

| 字段名 | 类型 | 释义 |
|--------|------|------|
| id | bigint | 主键 |
| corp_id | varchar | 渠道ID（外键）|
| deposit | decimal | 押金金额 |
| currency_code | varchar | 货币种类 |
| discount | int | 折扣 |
| contract_start_time | datetime | 合约开始时间 |
| contract_end_time | datetime | 合约结束时间 |
| ... | ... | ... |

> 详细字段请参考`ChannelDistributorDetail.java`。

---

## 6. 关键业务逻辑与约束

### 6.1 主要业务流程

1. **参数校验**：校验必填字段、格式、业务约束（如合作模式、套餐、押金等）。
2. **业务校验**：
   - 检查渠道商是否存在。
   - 检查渠道商当前状态是否允许修改。
   - 校验合作模式、套餐、规则等变更是否合法（如A2Z、资源合作等）。
   - 校验套餐、规则等是否已被绑定，若已绑定则不允许删除。
3. **数据更新**：
   - 更新`cms_channel`主表信息。
   - 更新`cms_channel_distributors_detail`明细表信息。
   - 维护相关套餐、规则、关系表（如A2Z、资源合作、短信模板等）。
   - 处理押金、额度等财务字段。
4. **缓存与通知**：
   - 更新相关缓存（如内部订单标识）。
   - 触发相关通知（如变更通知、阈值提醒等）。

### 6.2 事务与一致性

- 方法加`@Transactional`，保证多表操作原子性。
- 关键点加分布式事务`@GlobalTransactional`（Seata）。

### 6.3 异常处理机制

- 业务异常统一抛出`BizException`，Controller 层捕获后返回`Response.error`。
- 典型错误码/信息：
  - "渠道商不存在"
  - "不允许删除已绑定A2Z imsi费规则"
  - "不允许删除已绑定资源合作imsi费规则"
  - "合作模式不包含A2Z，不允许选择定向应用"
  - 其他参数、状态、约束类错误

---

## 7. 业务规则说明

- **参数校验**：使用`@NotBlank`等注解+业务代码双重校验。
- **合作模式**：支持多种合作模式（A2Z、资源合作、代销等），不同模式下参数、规则、套餐校验逻辑不同。
- **套餐/规则变更**：如已被订单/卡绑定，不允许删除。
- **押金/额度**：变更时需校验财务约束，部分字段需加锁或乐观锁处理。
- **通知配置**：如激活通知、到期提醒、套餐使用提醒等，需校验URL、开关等参数。

---

## 8. 代码示例

### Controller 层

```java
@PutMapping("/updateChannel")
public Response updateChannel(@RequestBody UpdateChannelVO updateChannelVO) {
    try {
        channelService.updateChannel(updateChannelVO);
        return Response.ok();
    } catch (Exception e) {
        return Response.error(e.getMessage());
    }
}
```

### Service 层

```java
void updateChannel(UpdateChannelVO updateChannelVO);
```

### ServiceImpl 关键片段

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void updateChannel(UpdateChannelVO updateChannelVO) {
    // 1. 参数与业务校验
    // 2. 处理合作模式、套餐、规则等变更
    // 3. 更新主表、明细表、关系表
    // 4. 维护缓存、通知
    // 5. 异常处理
}
```

---

## 9. 相关表关系图

```mermaid
erDiagram
    cms_channel ||--o{ cms_channel_distributors_detail : corp_id
    cms_channel_distributors_detail ||--o{ 相关规则/套餐/关系表 : corp_id
```

---

## 10. 参考类与方法

- `ChannelController#updateChannel`
- `ChannelService#updateChannel`
- `ChannelServiceImpl#updateChannel`
- `UpdateChannelVO`、`NewChannelVO`
- `ChannelDistributorDetail`
- `ChannelDistributorDetailMapper`
- `ChannelMapper`
- 数据库表：`cms_channel`、`cms_channel_distributors_detail`等

---

**文档编写人：AI自动分析  
编写时间：2024-06-11**

---

如需更详细字段释义、SQL结构或业务流程图，请联系开发团队。 