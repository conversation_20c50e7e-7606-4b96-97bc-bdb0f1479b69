package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 卡与套餐关系表，套餐类型枚举
 * @date 2021/4/20 16:00
 */
@Getter
@AllArgsConstructor
public enum CardPackageTypeEnum {

    /**
     * 1：普通套餐
     */
    PACKAGE("1"),
    /**
     * 2：终端线下套餐
     */
    TERMINAL_OFFLINE_PACKAGE("2"),
    /**
     * 3：流量池
     */
    FLOW_POOL("3"),
    /**
     * 4：加油包
     */
    REFUEL("4"),

    /**
     * 5：终端线上套餐
     */
    TERMINAL_ONLINE_PACKAGE("5");

    private String packageType;

}
