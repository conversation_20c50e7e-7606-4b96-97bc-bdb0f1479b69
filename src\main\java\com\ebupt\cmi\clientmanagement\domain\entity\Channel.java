package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Desc 客户信息实体
 * <AUTHOR> lingsong
 * @Date 2021/4/20 11:54
 */
@Data
@Builder
@TableName("cms_channel")
@AllArgsConstructor
@NoArgsConstructor
public class Channel {
    @TableId
    private String corpId;

    private String corpName;

    /**
     * 厂商状态
     * 1：正常
     * 2：暂停
     * 3：删除(暂不用)
     */
    private String status;

    private String type;

    private String billType;

    private String ebsCode;

    private String billRule;

    private String settleType;

    private String settleRule;

    private String checkStatus;

    /**
     * 币种编码：156 人民币 840 美元 344 港币
     * 后付费、终端线上有效
     */
    private String currencyCode;

    private String companyName;

    private String internalOrder;

    private String address;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 折扣
     */
    @TableField(exist = false)
    private Integer discount;

    /**
     * 渠道商详情id ----> 给扣款用
     */
    @TableField(exist = false)
    private Long id;

    /**
     * 渠道商押金----> 现在打日志用
     */
    @TableField(exist = false)
    private String cooperationMode;

    private String parentCorpId;

    @TableField(exist = false)
    private List<String> modes;

    @TableField(exist = false)
    private String channelType;

    @TableField(exist = false)
    private String a2zChannelType;

    @TableField(exist = false)
    private String resourceChannelType;

    @TableField(exist = false)
    private String salesMail;

    @TableField(exist = false)
    private String email;

    @TableField(exist = false)
    private Map<String, String> relationGroupNames;

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum StatusEnum {

        /**
         * 正常
         */
        NORMAL("1"),

        /**
         * 暂停
         */
        PAUSED("2"),

        /**
         * 删除
         */
        DELETED("3");

        @Getter
        private String value;

    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum ChannelTypeEnum {
        /**
         * 渠道商
         */
        CHANNEL("1"),

        /**
         * 合作运营商
         */
        COORPERATOR("3"),

        /**
         * 后付费
         */
        POST_PAIED("4"),

        /**
         * 测试
         */
        TEST("5"),

        /**
         * 个人用户
         */
        PERSONAL_USER("6"),

        /**
         * 终端线上
         */
        TEMMINAL_ONLINE("7"),

        /**
         * 终端线下
         */
        TEMMINAL_OFFLINE("8"),

        /**
         * 流量池
         */
        FLOW_POOL("10"),

        /**
         * 线下售卡
         */
        OFFLINE_CARD_SALES("11"),

        /**
         * 子渠道商
         */
        SUBCHANNEL("12");

        @Getter
        private String value;
    }

}
