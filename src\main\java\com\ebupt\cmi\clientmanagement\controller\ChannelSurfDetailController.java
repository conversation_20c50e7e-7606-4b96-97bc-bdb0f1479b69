package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.SurfDetailVO;
import com.ebupt.cmi.clientmanagement.service.ChannelSurfDetailService;
import com.ebupt.cmi.clientmanagement.service.ChannelSurfInfoService;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelSurfDetailController.java
 * @Description 用于对上网明细表进行增删改查的controller
 * @createTime 2021年04月23日 11:09:00
 */

@RestController
@RequestMapping("/surfdetail")
@Api(tags = "上网明细表增删改查")
public class ChannelSurfDetailController {
    @Autowired
    ChannelSurfDetailService channelSurfDetailService;

    @Autowired
    ChannelSurfInfoService channelSurfInfoService;

    @PostMapping("/insertSurfDetail")
    @NormalLog
    public Response<Long> insertSurfDetail(@RequestBody @Valid SurfDetailVO surfDetailVO) {
        Long id = channelSurfDetailService.insert(surfDetailVO);
        return Response.ok(id);
    }

    @PutMapping("/changeStatus/{id}")
    @NormalLog
    public Response upDateById(@PathVariable("id") Long id, @RequestParam String changeStatus) {
        channelSurfDetailService.updateById(id, changeStatus);
        return Response.ok();
    }

    @PutMapping("/{packageUniqueId}")
    @NormalLog
    public Response updatePackageCardByUUID(
            @PathVariable("packageUniqueId") String uuid,
            @RequestParam("changeStatus") String changeStatus) {
        channelSurfDetailService.updatePackageCardByUUID(uuid, changeStatus);
        return Response.ok();
    }

    @GetMapping("/getChannelSurfForImsi")
    @NormalLog
    public Response<Boolean> getChannelSurfForImsi(@RequestParam("imsi") String imsi) {
        Boolean result = channelSurfInfoService.getChannelSurfForImsi(imsi);
        return Response.ok(result);
    }
}
