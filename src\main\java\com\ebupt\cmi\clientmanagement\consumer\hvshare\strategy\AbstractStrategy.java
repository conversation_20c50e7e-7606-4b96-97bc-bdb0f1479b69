package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy;

import com.ebupt.cmi.clientmanagement.consumer.callback.StrategyProcessor;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AbstractReachingTreatmentStrategy.java
 * @Description 策略模式抽象基类
 * @createTime 2022年02月28日 15:27:00
 */

public abstract class AbstractStrategy {

    /**
     * 验证当前卡的在用套餐和已用套餐是否相等
     *
     * @param packageUniqueIDFromMessage 消息队列中传入的套餐唯一ID
     * @param packageUniqueIDFromTable   表中对应的套餐唯一ID
     */
    public abstract boolean vertifyPackageUniqueID(String packageUniqueIDFromMessage,
                                                   String packageUniqueIDFromTable);

    /**
     * 顶级handle抽象方法
     *
     * @param context 上下文
     * @param <T> 泛型
     * @throws InterruptedException 这有啥好标注的
     */
    public abstract <T extends BaseContext> void handle(T context) throws InterruptedException;


    /**
     * 一个默认的方法，可以不让所有子类实现。不规范
     * @param context 上下文
     * @param <T> 泛型
     */

    public  <T extends BaseContext> void beforeCallBack(T context) {

    };
    /**
     * 回调
     *
     * @param context 上下文
     * @throws InterruptedException 需要暂停线程
     */
    public abstract void callBack(BaseContext context) throws InterruptedException;

}
