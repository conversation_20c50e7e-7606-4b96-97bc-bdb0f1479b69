package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 套餐状态枚举
 * @date 2021/4/16 17:22
 */
@AllArgsConstructor
@Getter
public enum PackageStatusEnum {

    /**
     * 待激活
     */
    UNACTIVATED("1"),

    /**
     * 已激活
     */
    ACTIVATED("2"),

    /**
     * 已使用
     */
    USED("3"),

    /**
     * 已激活待计费
     */
    UNCHARGED("4"),

    /**
     * 已过期
     */
    OVERDUE("5"),

    /**
     * 激活中
     */
    ACTIVATING("6"),

    /**
     * 待发货（订单详情中）
     */
    NOT_DELIVERED("7");

    private String status;

    public static PackageStatusEnum of(String value) {
        for (PackageStatusEnum packageStatusEnum : values()) {
            if (packageStatusEnum.getStatus().equals(value)) {
                return packageStatusEnum;
            }
        }
        throw new IllegalArgumentException(value);
    }

}
