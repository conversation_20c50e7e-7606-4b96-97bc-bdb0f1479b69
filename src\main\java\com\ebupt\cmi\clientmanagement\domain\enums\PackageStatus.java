package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 套餐状态
 * @<PERSON> huang lingsong
 * @Date 2021/5/18 17:39
 */
@Getter
@AllArgsConstructor
public enum PackageStatus {

    NOT_ACTIVATY("1", "未激活"),

    ACTIVATED("2", "已激活"),

    USED("3", "已使用"),

    EXPIRED("5", "已过期"),

    ACTIVATING("6", "激活中");


    private String k;

    private String val;

    public static String getVal(String k) {
        for (PackageStatus p: PackageStatus.values()){
            if (p.getK().equals(k)){
                return p.getVal();
            }
        }
        return "";
    }
}
