package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ConsignmentDetailsDto {

    //活动名称
    private String campaignName;
    //活动开始时间
    private Date startTime;
    //活动结束时间
    private Date endTime;
    //活动状态
    private String campaignStatus;
    //总金额
    private BigDecimal totalAmount;

    //剩余额度
    private BigDecimal rebateAmount;

    //已用额度
    private BigDecimal usedAmount;

    //到账时间
     private Date createTime;

     //过期时间
     private Date expireTime;


}
