package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.codec.binary.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4 14:44
 */

@Getter
@AllArgsConstructor
public enum  BlankCardOrderStatusEnum {
    /**
     * 1：已下单  2 ：已取消 3 ：待付款 4 ：已付款 5 ：待发货 6 ：发货中 7 ：已发货 8 ：发货失败
     */

    ORDERED("1"),

    CANCELLED("2"),

    TO_BE_PAID("3"),

    PAID("4"),

    TO_BE_DELIVER("5"),

    ON_DELIVERY("6"),

    DELIVERED("7"),

    DELIVERED_FAIL ("8");

    private String status;

    /**
     * 判断status是否合法
     *
     * @param status
     * @param statusEnums
     * @return
     */
    public static boolean isIn(String status, BlankCardOrderStatusEnum... statusEnums) {
        return Arrays.asList(statusEnums).parallelStream().
                anyMatch(value -> StringUtils.equals(value.getStatus(), status));

    }

    /**
     * 判断是否订单已终结，取消、关闭、成功、拒绝都属于终结状态
     *
     * @param status
     * @return
     */
    public static boolean isFinish(String status) {
        return isIn(status, DELIVERED_FAIL, CANCELLED);
    }
}
