package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.Address;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsUserAccount;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsUserIccid;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.AddressOptVo;
import com.ebupt.cmi.clientmanagement.service.AccountService;
import com.ebupt.cmi.clientmanagement.service.AddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Desc 账户相关接口
 * <AUTHOR>
 * @Date 2021/4/25 10:50
 */
@Slf4j
@RestController
@RequestMapping("/address")
@Api(tags = "客户地址管理")
public class AddressController {

    @Autowired
    private AddressService addressService;

    @PostMapping("/opt")
    @ApiOperation(value = "个人用户地址管理增删查改接口", notes = "个人用户地址管理增删查改接口")
    public Response<List<Address>> getAddressList(@RequestBody AddressOptVo addressOptVo,@RequestParam("msisdn") String msisdn) throws IOException {
        log.info("请求参数：{}",addressOptVo.toString());
        return addressService.optAddress(addressOptVo,msisdn);
    }
}
