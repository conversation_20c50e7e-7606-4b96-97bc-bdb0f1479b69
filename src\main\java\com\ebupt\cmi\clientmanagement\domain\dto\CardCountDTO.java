package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.*;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CardCountDTO {

    /**
     * 卡片形态
     * 1普通 2esim 3贴片卡
     */
    private String cardForm;

    /**
     * 卡片数量
     */
    private Long cardCount;


    @AllArgsConstructor
    public enum CardFormEnum {

        /**
         * 1：普通卡
         */
        CLASSIC("1"),

        /**
         * 2：ESIM卡
         */
        ESIM("2"),

        /**
         * 3:贴片卡
         */
        SIM_PLUS("3"),

        /**
         * 4:IMSI卡
         */
        IMSI("4");

        @Getter
        private String value;
    }
}
