package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TypeEnum
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>n
 * @Date: 2021/5/8 12:25
 */
@Getter
@AllArgsConstructor
public enum TypeEnum {
    /**
     * 批发商
     */
    WHOLESALER("1"),
    /**
     * 酬金商
     */
    HONORARY("2"),
    /**
     * 合作商
     */
    PARTNER("3"),
    /**
     * 后付费
     */
    POSTPAID("4"),
    /**
     * 测试渠道
     */
    TEST_CHANNEL("5"),
    /**
     * 个人用户
     */
    INDIVIDUAL ("6"),
    /**
     * 终端线上
     */
    TERMINAL_LINE("7"),
    /**
     * 终端线下
     */
    OFFLINE_TERMINAL("8"),
    /**
     * 能力渠道商
     */
    ABILITY_CHANNEL("9"),
    /**
     * 流量池
     */
    FLOW_POOL("10"),
    /**
     * 线下售卡
     */
    OFFLINE_CARD_SALES("11");

    private String type;
}
