package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties("threadpool")
public class ThreadPoolConfigProperties {

    private int coreSize = 1;
    private int maxSize;
    private int queueCapacity;
    private String namePrefix;
    private RejectPolicy rejectPolicy;

}
