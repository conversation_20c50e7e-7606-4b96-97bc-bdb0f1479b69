package com.ebupt.cmi.clientmanagement.consumer.hvshare.repository;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching.ReachingTreatmentContext;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.enums.UpccOpenStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.NotRollBackBizException;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.UnSubscribeServiceVO;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardLuDTO;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardVO;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.PackageOverdueService;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import com.ebupt.cmi.clientmanagement.utils.UpccUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReachingTreatmentRepository.java
 * @Description 达量处理仓库
 * @createTime 2022年02月28日 16:16:00
 */
@Slf4j
@Component
public class HvShareRepository {

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Resource
    CmsCardUpccRecordMapper cmsCardUpccRecordMapper;

    @Resource
    ChannelCardMapper channelCardMapper;

    @Resource
    ChannelPackageCardMapper channelPackageCardMapper;

    @Resource
    ChannelSurfInfoMapper surfInfoMapper;

    @Resource
    PackageSurfStatusLogMapper packageSurfStatusLogMapper;

    @Resource
    ChannelDistributorDetailMapper channelDistributorDetailMapper;

    @Resource
    PackageOverdueService packageOverdueService;

    @Resource
    ControlFeignClient controlFeignClient;

    @Value("${upcc-zero-template-id}")
    private String zeroTemplateId;

    @Resource
    private CoreNetCaller coreNetCaller;

    @Resource
    private CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    @Resource
    private PackageDirectionRelationMapper packageDirectionRelationMapper;

    public HcardInfo getHcard(String himsi) {

        return Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(
                himsi));

    }

    public ChannelCard getChannelCard(String himsi) {

        return channelCardMapper.selectOne(new QueryWrapper<ChannelCard>()
                .eq("imsi", himsi));
    }

    public ChannelPackageCard getChannelPackageCardByPackageUniqueId(String packageUniqueId) {

        return channelPackageCardMapper.selectOne(new QueryWrapper<ChannelPackageCard>()
                .eq("package_unique_id", packageUniqueId));

    }

    public void refreshChannelPackageCardPackageStatus(String packageUniqueId, String status) {

        ChannelPackageCard channelPackageCard = ChannelPackageCard.builder()
                .packageStatus(status)
                .build();

        QueryWrapper<ChannelPackageCard> wrapper = new QueryWrapper<>();

        wrapper.eq("package_unique_id", packageUniqueId);

        channelPackageCardMapper.update(channelPackageCard, wrapper);
    }

    public void refreshChannelPackageCard(
            String packageUniqueId,
            ChannelPackageCard channelPackageCard) {

        QueryWrapper<ChannelPackageCard> wrapper = new QueryWrapper<>();

        wrapper.eq("package_unique_id", packageUniqueId);

        channelPackageCardMapper.update(channelPackageCard, wrapper);
    }

    public void refreshChannelPackageCardSurfStatus(String packageUniqueId, String status, boolean clearSpeedControlId) {

        //上网状态
        // 1：正常
        // 2：限速
        channelPackageCardMapper.update(null, Wrappers.lambdaUpdate(ChannelPackageCard.class)
                .set(ChannelPackageCard::getSurfStatus, status)
                .set(clearSpeedControlId, ChannelPackageCard::getSpeedControlId, null)
                .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
    }


    /**
     * 更新套餐上网状态日志表
     * 与【cms_package_surf_status_log】表交互
     *
     * @param packageUniqueId 套餐唯一ID
     * @param surfStatus      上网状态 1-正常  2-限速
     */
    public void updatePackageSurfLogStatus(String packageUniqueId, String surfStatus) {

        ChannelPackageCard channelPackageCard = getChannelPackageCardByPackageUniqueId(packageUniqueId);

        if (ObjectUtils.isEmpty(channelPackageCard)) {

            log.info("根据当前套餐唯一ID：{}，未在【cms_channel_package_card】表查询到对应信息", packageUniqueId);

            return;
        }

        Date endTime = ObjectUtils.isEmpty(channelPackageCard.getExpireTime()) ? channelPackageCard.getEffectiveDay() : channelPackageCard.getExpireTime();

        Date now = new Date();

        PackageSurfStatusLog packageSurfStatusLog = packageSurfStatusLogMapper.selectOne(Wrappers.lambdaQuery(PackageSurfStatusLog.class)
                .eq(PackageSurfStatusLog::getPackageUniqueId, packageUniqueId)
                .orderByDesc(PackageSurfStatusLog::getId)
                .last("limit 1")
        );


        if (ObjectUtil.isNotNull(packageSurfStatusLog)) {

            if (surfStatus.equals(packageSurfStatusLog.getSurfStatus())) {

                log.info("根据当前套餐唯一ID：{}，查询对应的套餐上网状态为：{}，不需要更新套餐上网日志表", packageUniqueId, packageSurfStatusLog.getSurfStatus());

                return;
            }

            packageSurfStatusLogMapper.update(null, Wrappers.lambdaUpdate(PackageSurfStatusLog.class)
                    .set(PackageSurfStatusLog::getEndTime, now)
                    .eq(PackageSurfStatusLog::getId, packageSurfStatusLog.getId())
            );
        }


        PackageSurfStatusLog build = PackageSurfStatusLog.builder()
                .packageUniqueId(packageUniqueId)
                .surfStatus(surfStatus)
                .startTime(now)
                .endTime(endTime)
                .build();

        packageSurfStatusLogMapper.insert(build);

    }


    public void updateChannelStatus(String corpId, String status) {
        channelDistributorDetailMapper.update(null, Wrappers.lambdaUpdate(ChannelDistributorDetail.class)
                .set(ChannelDistributorDetail::getA2zCardUseSwitch, status)
                .eq(ChannelDistributorDetail::getCorpId, corpId));
    }

    public void updateChannelPackageCardUseStatus(String packageUniqueId, String status) {
        channelPackageCardMapper.update(null, Wrappers.lambdaUpdate(ChannelPackageCard.class)
                .set(ChannelPackageCard::getPackageUseStatus, status)
                .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueId));
    }

    public Set<String> getSurfCardByPackageUniqueId(String packageUniqueId) {
        List<ChannelSurfInfo> channelSurfInfos = surfInfoMapper.selectInfoListAndImsiDistinctByPackageUniqueIds(
                Collections.singletonList(packageUniqueId));
        return channelSurfInfos.stream().filter(channelSurfInfo -> channelSurfInfo.getInternetType().equals("2"))
                .map(ChannelSurfInfo::getImsi).collect(Collectors.toSet());
    }

    public boolean recoverVCard(Set<String> vimsi) {
        boolean vDelHssResult = packageOverdueService.delHssSubscriber(vimsi);
        Set<String> needDelUpccSet = Response.getAndCheckRemoteData(pmsFeignClient.getIsSignUpcc(vimsi));
        boolean vDelUpccResult = true;
        if (needDelUpccSet.size() > 0) {
            vDelUpccResult = packageOverdueService.delUpccSubscriber(needDelUpccSet, CardTypeEnum.V_CARD, false);
        }
        return vDelHssResult && vDelUpccResult;
    }

    public boolean signUpccZeroTemplate(HcardInfo hcardInfo) {

        log.debug("判断UPCC签约状态，是否签约0K模板");

        UpdateOpenStatusReq updateOpenStatusReq = new UpdateOpenStatusReq();

        String upccSignBizId = hcardInfo.getUpccSignBizId();

        String upccNeedSignId = org.springframework.util.StringUtils.isEmpty(hcardInfo.getExpireUpccSignId()) ? zeroTemplateId : hcardInfo.getExpireUpccSignId();

        if (!upccNeedSignId.equals(upccSignBizId)) {
            log.debug("[H流程] 没签约该签得模板，进行upcc签约");
            //签约0k
            Response<Void> response = controlFeignClient.subscribeService(new LoadSubscribe(hcardInfo.getMsisdn(), upccNeedSignId));

            if (response.isOk()) {
                log.debug("UPCC签约成功，更新H卡状态 imsi: {}", hcardInfo.getImsi());
                updateOpenStatusReq.setUpccSignStatus("1");
                updateOpenStatusReq.setUpccSignBizId(upccNeedSignId);
                updateOpenStatusReq.setUpccSignPackageUniqueId(StringUtils.EMPTY);
                updateOpenStatusReq.setImsi(hcardInfo.getImsi());
                pmsFeignClient.updateCardOpenStatus(updateOpenStatusReq);
            }

            return response.isOk();
        }

        return true;
    }


    public CardVO getOneCard(String imsi) {

        return Response.getAndCheckRemoteData(pmsFeignClient.getOneCard(imsi));

    }

    /**
     * 更新channelPackageCard
     *
     * @param newChannelPackageCard 更新后的
     * @param packageUniqueId       uuid
     */
    public void updateSurfInfo(ChannelPackageCard newChannelPackageCard,
                               String packageUniqueId) {

        ChannelSurfInfo surfInfo = ChannelSurfInfo
                .builder()
                .start_time(DateUtil.toLocalDateTime(new Date()))
                .end_time(DateUtil.toLocalDateTime(newChannelPackageCard.getExpireTime()))
                .build();

        QueryWrapper<ChannelSurfInfo> surfInfoQueryWrapper = new QueryWrapper<>();

        surfInfoQueryWrapper.eq("package_unique_id", packageUniqueId);

        surfInfoMapper.update(surfInfo, surfInfoQueryWrapper);

    }

    /**
     * 更新Vcard的upcc签约状态
     *
     * @param vimsi Vimsi
     */
    public void updateVcardUpccSignStatus(String vimsi) {

        //upcc开户状态
        //1：成功
        //2:失败

        //upcc签约状态
        //1:：成功
        //2:失败
        UpdateOpenStatusReq updateOpenStatusReq =
                UpdateOpenStatusReq
                        .builder()
                        .imsi(vimsi)
                        .upccSignStatus("2")
                        .upccSignBizId("")
                        .build();
        log.info("更新Vcard[{}]的upcc签约状态[2]", vimsi);
        Response.getAndCheckRemoteData(pmsFeignClient.updateVimsiOpenStatus(updateOpenStatusReq));

    }

    /**
     * 置空流量通知挡位字段
     *
     * @param packageUniqueIDFromMessage 套餐唯一id
     */
    public void refreshChannelPackageNoticeLevelToNull(String packageUniqueIDFromMessage) {
        // 更新挡位
        channelPackageCardMapper.update(null,
                Wrappers.lambdaUpdate(ChannelPackageCard.class)
                        .set(ChannelPackageCard::getFlowNoticeLevel, null)
                        .eq(ChannelPackageCard::getPackageUniqueId, packageUniqueIDFromMessage));
    }

    public Set<Long> getSurfAppByPackageUniqueId(String packageUniqueIDFromMessage, String appGroupId) {
        QueryWrapper<PackageDirectionRelation> wrapper = new QueryWrapper<>();
        wrapper.eq("package_unique_id", packageUniqueIDFromMessage);
        if (appGroupId == null) {
            wrapper.eq("direct_type", PackageDirectionRelation.DirectType.FREE_FLOW.getValue());
            wrapper.eq("has_used", PackageDirectionRelation.Status.HAS_USED.getValue());
            wrapper.eq("is_use_package", PackageDirectionRelation.IsUsePackage.YES.getValue());
        } else {
            wrapper.eq("app_group_id", appGroupId);
        }
        List<PackageDirectionRelation> packageDirectionRelations = packageDirectionRelationMapper.selectList(wrapper);
        Set<String> appGroupIdList = packageDirectionRelations.stream().map(PackageDirectionRelation::getAppGroupId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(appGroupIdList)) {
            return new HashSet<>();
        }
        List<CmsPackageCardUpccRelation> cmsPackageCardUpccRelations = cmsPackageCardUpccRelationMapper.selectList(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                .in(CmsPackageCardUpccRelation::getAppGroupId, appGroupIdList)
                .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueIDFromMessage));
        return cmsPackageCardUpccRelations.stream().map(CmsPackageCardUpccRelation::getAppId).collect(Collectors.toSet());
    }

    public Set<Long> getLimitSurfAppByPackageUniqueId(String packageUniqueIDFromMessage) {
        QueryWrapper<PackageDirectionRelation> wrapper = new QueryWrapper<>();
        wrapper.eq("package_unique_id", packageUniqueIDFromMessage);
        wrapper.eq("direct_type", PackageDirectionRelation.DirectType.LIMIT_SPEED.getValue());
        List<PackageDirectionRelation> packageDirectionRelations = packageDirectionRelationMapper.selectList(wrapper);
        QueryWrapper<PackageDirectionRelation> wrapper2 = new QueryWrapper<>();
        wrapper2.eq("package_unique_id", packageUniqueIDFromMessage);
        wrapper2.eq("direct_type", PackageDirectionRelation.DirectType.FREE_FLOW.getValue());
        wrapper2.eq("has_used", PackageDirectionRelation.Status.HAS_USED.getValue());
        wrapper2.eq("is_use_package", PackageDirectionRelation.IsUsePackage.YES.getValue());
        List<PackageDirectionRelation> packageDirectionRelations2 = packageDirectionRelationMapper.selectList(wrapper2);
        packageDirectionRelations.addAll(packageDirectionRelations2);
        Set<String> appGroupIdList = packageDirectionRelations.stream().map(PackageDirectionRelation::getAppGroupId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(appGroupIdList)) {
            return null;
        }
        List<CmsPackageCardUpccRelation> cmsPackageCardUpccRelations = cmsPackageCardUpccRelationMapper.selectList(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                .in(CmsPackageCardUpccRelation::getAppGroupId, appGroupIdList)
                .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueIDFromMessage));
        return cmsPackageCardUpccRelations.stream().map(CmsPackageCardUpccRelation::getAppId).collect(Collectors.toSet());
    }


    public void release(ChannelPackageCard channelPackageCard) {
        log.info("=======================进入套餐达量释放流程，imsi:{}============================",
                channelPackageCard.getImsi());
        String packageStatus = channelPackageCard.getPackageStatus();

        //套餐激活状态
        //1：待激活
        //2：已激活
        //3：已使用
        //5：已过期
        //6：激活中
        Date now = new Date();
        ChannelPackageCard packageCardToUpdate = new ChannelPackageCard();
        packageCardToUpdate.setId(channelPackageCard.getId());
        switch (packageStatus) {
            case "2":
                if (channelPackageCard.getExpireTime().before(now)) {
                    log.warn("当前套餐过期时间已在当前时间之前，流程结束");
                    return;
                }
                packageCardToUpdate.setExpireTime(now);
                break;
            case "6":
                if (channelPackageCard.getEffectiveDay().before(now)) {
                    log.warn("当前套餐过期时间已在当前时间之前，流程结束");
                    return;
                }
                packageCardToUpdate.setEffectiveDay(now);
                break;
            default:
                log.info("===========================该套餐的状态不支持提前释放================================");
                return;

        }
        packageOverdueService.preActivated(channelPackageCard);
        channelPackageCardMapper.updateById(packageCardToUpdate);
        channelPackageCard.setUpdateOverdueTime(false);
        packageOverdueService.updateSurfEndTime(channelPackageCard.getPackageUniqueId(), now, false);
        packageOverdueService.handleActivatedOne(channelPackageCard);
        log.info("=======================套餐达量释放流程结束，imsi:{}============================",
                channelPackageCard.getImsi());
    }


    public String getUpccSignIdByAppId(String packageUniqueId, Long appId) {
        CmsPackageCardUpccRelation cmsPackageCardUpccRelation = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                .eq(CmsPackageCardUpccRelation::getAppId, appId)
                .orderByDesc(CmsPackageCardUpccRelation::getConsumption));
        return cmsPackageCardUpccRelation.getUpccSignId();
    }


    public PackageDirectionRelation getPackageDirectionRelation(String appGroupId, String packageUniqueId) {
        PackageDirectionRelation packageDirectionRelation = packageDirectionRelationMapper.selectOne(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                .eq(PackageDirectionRelation::getAppGroupId, appGroupId)
                .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId));
        packageDirectionRelation.setHasUsed("3");
        packageDirectionRelation.setUpdateTime(new Date());
        packageDirectionRelationMapper.updateById(packageDirectionRelation);
        return packageDirectionRelation;
    }


    public void extracted(boolean needUpccSign, Long appId, String upccSignId, String imsi, String msisdn) {

        CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                .eq(CmsCardUpccRecord::getAppId, appId)
                .eq(CmsCardUpccRecord::getImsi, imsi));

        if (needUpccSign && cmsCardUpccRecord != null && cmsCardUpccRecord.getUpccSignBizId().equals(upccSignId)) {
            log.debug("此卡已经签约指定模板，不用处理");
            return;
        }

        if (needUpccSign) {
            log.debug("imsi: {}覆盖签约", imsi);
            LoadSubscribe loadSubscribe = new LoadSubscribe(msisdn, upccSignId);
            Response.getAndCheckRemoteData(controlFeignClient
                    .subscribeService(loadSubscribe));
            if (cmsCardUpccRecord != null) {
                cmsCardUpccRecord.setUpdateTime(new Date());
                cmsCardUpccRecord.setUpccSignBizId(upccSignId);
                cmsCardUpccRecordMapper.updateById(cmsCardUpccRecord);
            } else {
                cmsCardUpccRecordMapper.insert(CmsCardUpccRecord.builder()
                        .imsi(imsi)
                        .appId(appId)
                        .upccSignBizId(upccSignId)
                        .build());
            }
        } else {
            if (cmsCardUpccRecord != null) {
                log.debug("imsi: {}删除签约", imsi);
                UnSubscribeServiceVO unSubscribeServiceVO = new UnSubscribeServiceVO(msisdn, cmsCardUpccRecord.getUpccSignBizId());
                Response.getAndCheckRemoteData(controlFeignClient.unSubscribeUpccService(unSubscribeServiceVO));
                cmsCardUpccRecordMapper.deleteById(cmsCardUpccRecord);
            }
        }

    }

}
