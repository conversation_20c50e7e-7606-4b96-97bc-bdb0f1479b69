package com.ebupt.cmi.clientmanagement.consumer;

import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsFlowpoolInfoCycle;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.UnSubscribeServiceVO;
import com.ebupt.cmi.clientmanagement.job.context.FlowPoolResetContext;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/11 14:17
 */

@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues = "directionalAppTemplate.del.queue")
public class DirectionalAppTemplateDelConsumer {
    private final ControlFeignClient controlFeignClient;

    @RabbitHandler
    public void process(String msg, Channel channel, Message message) throws IOException, InterruptedException {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        UnSubscribeServiceVO vo = JSON.parseObject(msg, UnSubscribeServiceVO.class);
        delTemplate(vo, 0);
    }

    private void delTemplate(UnSubscribeServiceVO vo, int retry) throws InterruptedException {
        try {
            if (retry < 5) {
                controlFeignClient.unSubscribeUpccService(vo);
            }
        } catch (Exception e) {
            Thread.sleep(1000);
            delTemplate(vo, ++retry);
        }
    }
}
