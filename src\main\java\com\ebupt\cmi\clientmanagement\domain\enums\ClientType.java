package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 客户类型
 * @date 2021/5/7 11:03
 */

@AllArgsConstructor
@Getter
public enum ClientType {
    /**
     * 1：批发商
     */
    WHOLESALER("1"),
    /**
     * 2：酬金商
     */
    COMPENSATION("2"),
    /**
     * 3：合作商
     */
    COOPERATIVE_PARTNER("3"),
    /**
     * 4：后付费
     */
    POST_PAID("4"),
    /**
     * 5：测试渠道
     */
    TEST_CHANNEL("5"),
    /**
     * 6：个人用户
     */
    PERSONAL_USER("6"),
    /**
     * 7：终端线上
     */
    TERMINAL_ONLINE("7"),
    /**
     * 8：终端线下
     */
    TERMINAL_OFFLINE("8"),
    /**
     * 9：能力渠道商
     */
    FUNCTION_CHANNEL("9"),
    /**
     * 10:流量池
     */
    FLOW_POOL("10"),
    /**
     * 11：线下售卡
     */
    OFFLINE_SELL_CARD("11");

    String type;
}
