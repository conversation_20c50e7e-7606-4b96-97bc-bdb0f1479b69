package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsPackageCardUpccRelation对象", description="")
@AllArgsConstructor
@NoArgsConstructor
public class CmsPackageCardUpccRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "套餐id")
    private String packageId;

    @ApiModelProperty(value = "套餐类型")
    private String packageType;

    @ApiModelProperty(value = "套餐唯一id")
    private String packageUniqueId;

    @ApiModelProperty(value = "每档用量 单位（byte），20个9表示无上限模板")
    private Long consumption;

    @ApiModelProperty(value = "upcc签约id")
    private String upccSignId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    private Long appId;

    private Long upccSpeed;

    /**
     * 是否支持热点1：是2：否
     */
    private String supportHotspots;

    private String appGroupId;



    @AllArgsConstructor
    @NoArgsConstructor
    public enum SupportHotSpot{


        YES("1"),

        NO("2");

        @Getter
        String value;
    }

}
