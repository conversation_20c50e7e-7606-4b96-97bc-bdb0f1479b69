package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 客户订单表实体
 * @date 2021/4/19 16:34
 */
@Builder
@TableName("cms_malfunction_card")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsMalfunctionCard {
    @TableId
    private Long id;

    private String imsi;

    private String malfunctionId;

    private String mcc;

    private Date createTime;
}
