package com.ebupt.cmi.clientmanagement.domain.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;

/**
 * (CmsChannelBlankcardOrder)表实体类
 *
 * <AUTHOR>
 * @since 2023-08-24 14:08:57
 */
@TableName("cms_channel_blankcard_order")
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsChannelBlankcardOrder {
    //订单ID
    @TableId(value = "order_id")
    private Long orderId;

    //卡片形态1：普通卡（实体卡）2：Esim卡3：贴片卡4：IMSI号
    private String cardForm;

    //收货人
    private String addressee;

    //收货人电话【加密】
    private String phoneNumber;

    //地址，竖线分割【加密】(国家|省份|城市地址|邮寄地址)
    private String address;

    //数量
    private Integer count;

    //订单状态1：已下单2 ：已取消3 ：待付款4 ：已付款5 ：待发货6 ：发货中7 ：已发货8 ：发货失败
    private String orderStatus;

    //购买用户的corpid
    private String orderUserId;

    //订购唯一id，uuid
    private String orderUniqueId;

    //合作模式1：代销2：A2Z
    private String cooperationMode;

    //订单批次
    private String orderBatch;

    //收费模式：1：定制卡2：普通卡
    private String chargingMode;

    //发票文件保存路径(NFS)
    private String invoicePath;

    //付款证明保存路径(NFS)
    private String paymentProofsPath;

    private String deliverFailPath;

    private String logistic;

    private String logisticCompany;

    private Date createTime;

    private Date updateTime;

    private Date invoiceTime;

    private Date deliverTime;

    private Date paymentProofsTime;

    private Date confirmTime;

    private String mcc;

    private String cardPackage;

    private String postcode;

    private Long templateId;

    private String language;

    private Long ruleId;

    private Long freeimsiId;

    private Long groupId;

    @TableField(exist = false)
    private String corpName;

    @AllArgsConstructor
    @Getter
    public enum ChargingMode {

        CUSTOM_CARD("1", "定制卡"),

        COMMON_CARD("2", "普通卡");

        String k;

        String v;

        public static String getName(String k) {
            if (CUSTOM_CARD.k.equals(k)) {
                return CUSTOM_CARD.v;
            } else {
                return COMMON_CARD.v;
            }
        }
    }

}

