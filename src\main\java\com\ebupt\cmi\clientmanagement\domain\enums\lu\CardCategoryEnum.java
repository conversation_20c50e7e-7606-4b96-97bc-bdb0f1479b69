package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 主卡类型枚举
 * @date 2021/4/19 17:11
 */
@AllArgsConstructor
@Getter
public enum CardCategoryEnum {

    /**
     * 普通卡
     */
    NORMAL("1"),
    /**
     * 省移动
     */
    PROV_MOBILE("2"),
    /**
     * 后付费
     */
    CHARGE_AFTER_USED("3"),
    ;

    private String type;
}
