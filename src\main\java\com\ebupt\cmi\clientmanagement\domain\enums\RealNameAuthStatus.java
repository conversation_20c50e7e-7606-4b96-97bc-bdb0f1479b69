package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * zsl
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum  RealNameAuthStatus {
    /**
     *  1、待认证 2、认证中 3、认证通过 4、认证失败 5、证件已过期
     */
    WAIT_AUTHOR("1"),

    IN_AUTHOR("2"),

    PROMISE_IDENTIFICATION("3"),

    DEFAULT_AUTHOR("4"),

    CARD_OUT_TIME("5");

    String status;
}
