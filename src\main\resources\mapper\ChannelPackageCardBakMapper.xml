<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ebupt.cmi.clientmanagement.mapper.ChannelPackageCardBakMapper">

    <select id="getFlowLimitSum" resultType="java.math.BigDecimal">
        select IFNULL(sum(flow_limit_sum),0) as flowLimitSum from cms_channel_package_card_bak ccpcb
        inner join cms_channel_order_detail ccod on ccpcb.package_unique_id =ccod.package_unique_id
        where ccod.order_status in ('2','4')
        AND ccpcb.belong_package_id = #{packageUniqueId}
        <if test="startTime != null">
            AND ccpcb.expire_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND ccpcb.expire_time &lt; #{endTime}
        </if>

    </select>


</mapper>