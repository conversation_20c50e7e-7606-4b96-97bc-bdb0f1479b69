package com.ebupt.cmi.clientmanagement.domain.entity.realname;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelRealNameInfo.java
 * @Description entity
 * @createTime 2021年11月29日 14:16:00
 */

@TableName("cms_channel_realname_info")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelRealNameInfo {


    @TableId
    private Long authId;

    private String msisdn;

    private String imsi;

    private String iccid;

    @TableField(exist = false)
    private String iccidList;

    private String orderUniqueId;

    /**
     * 证件ID
     */
    private String certificatesId;

    /**
     * 证件类型
     * 1：护照
     * 2：港澳通行证
     * 3：香港身份证
     * 4：澳门身份证
     */
    private String certificatesType;

    /**
     * 认证规则编码
     */
    private String ruleCode;

    /**
     * 护照国家
     */
    private String passportCountry;

    /**
     * 出生年月 YYYYMMDD
     */
    private String dateOfBirth;

    /**
     * 用户输入姓名（英文）
     */
    private String inputName;

    /**
     * 用户输入姓名（中文）
     */
    private String inputNameCh;

    /**
     * OCR证件识别-姓名
     */
    private String ocrName;

    /**
     * OCR证件识别-证件号
     */
    private String ocrNumber;

    /**
     * OCR证件识别-出生日期
     * 格式为 YYYYMMDD
     */
    private String ocrBirthDate;

    /**
     * OCR识别 证件类型
     * 1：护照
     * 2：港澳通行证
     * 3：香港身份证
     * 4：澳门身份证
     */
    private String ocrCertificatesType;

    /**
     * OCR证件识别-证件过期日期
     * 格式为 YYYYMMDD
     */
    private String ocrExpireDate;

    /**
     * 用户输入证件过期日期
     * 格式为 YYYYMMDD
     */
    private String expireDate;

    /**
     * OCR证件识别-国家码
     */
    private String ocrCountryCode;

    /**
     * 认证方式
     * 1、自动认证（默认）
     * 2、人工认证
     */
    private String authMode;

    /**
     * 认证通知手机号
     */
    private String phoneNumber;

    /**
     * 认证通知邮箱
     */
    private String email;

    /**
     * 认证规则名称
     */
    private String ruleName;

    /**
     * 图片文件名
     */
    private String fileName;

    /**
     * ocr认证失败原因
     * 1：姓名校验不一致
     * 2：证件已过期
     * 3：证件ID校验不一致
     * 4：未满16周岁
     * 0：OCR识别异常
     */
    private String errorDesc;

    /**
     * 认证状态 1、待认证 2、认证中 3、认证通过 4、认证失败 5、证件已过期
     */
    private String authStatus;

    /**
     * 使用状态 1、处理中 2、在用 3、备份
     */
    private String useStatus;

    /**
     * 认证对象
     * 1：卡
     * 2：订单
     */
    private String authObj;

    /**
     * 证件有效期 默认2099年12月31日
     */
    @TableField("certificatesTime")
    private Date certificatesTime;

    @TableField(exist = false)
    private String certificatesTimeForExcel;

    /**
     * 认证次数，默认值0
     */
    private Integer authNum;


    private Long updateDocumentsId;
    /**
     * 是否重新认证过，默认1
     * 1：未重新认证过
     * 2：已重新认证过
     */
    private String isRepeat;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 认证时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime authTime;

    @TableField(exist = false)
    private String authTimeForExcel;

    @TableField(exist = false)
    private Set<String> orderIds;

    @TableField(exist = false)
    private Set<String> orderuuids;

    @TableField(exist = false)
    private String describe;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;

    @TableField(exist = false)
    private String cancelTimeForExcel;

    @TableField(exist = false)
    private boolean isNeedDesensitization = false;

    private String isAuthed;

    /**
     * 认证状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum AuthStatusEnum {

        /**
         * 待认证
         */
        NOT_AUTH("1"),

        /**
         * 认证中
         */
        AUTHING("2"),

        /**
         * 认证通过
         */
        PASSED("3"),

        /**
         * 认证失败
         */
        FAILED("4"),

        /**
         * 证件已过期
         */
        EXPIRED("5"),

        /**
         * 已取消
         */
        CANCELED("6");

        public boolean matches(String value) {
            return this.value.equals(value);
        }

        @Getter
        private String value;
    }

    /**
     * 使用状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum UseStatusEnum {

        /**
         * 处理中
         */
        PROCESSING("1"),

        /**
         * 在用
         */
        USING("2"),

        /**
         * 备份
         */
        BACKUP("3"),

        /**
         * 停用
         */
        DEACTIVATE("4");

        @Getter
        private String value;

    }

    /**
     * 认证对象
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum AuthObjEnum {

        /**
         * 卡
         */
        CARD("1"),

        /**
         * 订单
         */
        ORDER("2");

        @Getter
        private String value;

    }

    @AllArgsConstructor
    @Getter
    public enum UseStatus{
        /**
         * 处理中
         */
        PROCESSING("1"),
        /**
         * 在用
         */
        USING("2"),
        /**
         * 备份
         */
        BACKUP("3");

        private String status;
    }
}
