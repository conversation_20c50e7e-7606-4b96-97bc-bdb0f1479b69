package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.data.annotation.Transient;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 客户lu上报记录备份表
 * @date 2022/6/17 15:57
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@TableName("cms_channel_lu_report_bak")
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelLuReportBak {

    private Long id;

    private String imsi;

    private String reportType;

    @ApiModelProperty(value = "上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    @ApiModelProperty(value = "上报地点")
    private String mcc;

    @ApiModelProperty(value = "上报地点英文")
    @Transient
    private transient String mccEn;

    @ApiModelProperty(value = "激活类型1自动2手动")
    private String activeType;

    private String himsi;

    /**
     * 卡形态 1普通 2esim 3贴片卡
     */
    private String cardForm;

    private String iccid;

    private String msisdn;

    private String packageUniqueId;

    private Date createTime;

    /**
     * 位置消息类型：
     * 1. CCR_I
     * 2. CCR_U
     * 3. LU
     */
    private String activeMethod;

    /**
     * MCC+MNC
     */
    private String plmnlist;

    /**
     * 网络类型：
     * 1：UTRAN
     * 2：GERAN
     * 3：WLAN
     * 4：GAN
     * 5：HSPA Evolution
     * 6：EUTRAN
     * 1和2是2G网；3和4属于wifi网络；
     * 5是3G网
     * 6是4G网
     */
    private String netType;

    @Getter
    @AllArgsConstructor
    public enum ActiveMethodEnum {

        CCR_I("1"),

        CCR_U("2"),

        LU("3");

        private String value;
    }

    /**
     * 1和2是2G网
     * 3和4属于wifi网络
     * 5是3G网
     * 6是4G网
     */
    @Getter
    @AllArgsConstructor
    public enum NetTypeEnum {
        UTRAN("1"),

        GERAN("2"),

        WLAN("3"),

        GAN("4"),

        HSPA_EVOLUTION("5"),

        EUTRAN("6");

        private String value;
    }

    @Getter
    @AllArgsConstructor
    public enum ReportTypeEnum {

        H("1"),
        V("2");

        private String value;
    }
}
