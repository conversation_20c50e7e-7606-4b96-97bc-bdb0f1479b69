package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsPackageDelayRecordInfo对象", description="")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsPackageDelayRecordInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增id")
      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "主卡imsi")
    private String hImsi;

    @ApiModelProperty(value = "该主卡绑定的v_imsi")
    private String vImsi;

    @ApiModelProperty(value = "主卡所在位置mcc")
    private String mcc;

    @ApiModelProperty(value = "v卡是否可以被继承 1: 是 2：否")
    private String isInherit;

    @ApiModelProperty(value = "v卡是否已被使用 1：是 2：否")
    private String status;

    @ApiModelProperty(value = "旧套餐唯一id")
    private String oldPackageUniqueId;

    @ApiModelProperty(value = "新套餐唯一Id")
    private String newPackageUniqueId;

    @ApiModelProperty(value = "新套餐id")
    private String packageId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
