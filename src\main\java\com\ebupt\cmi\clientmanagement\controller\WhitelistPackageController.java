package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.WhitelistPackageDTO;
import com.ebupt.cmi.clientmanagement.domain.req.WhitelistPackageFileReq;
import com.ebupt.cmi.clientmanagement.domain.req.WhitelistPackageQueryReq;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.WhitelistCorpInfoVO;
import com.ebupt.cmi.clientmanagement.domain.vo.WhitelistPackageVO;
import com.ebupt.cmi.clientmanagement.service.WhitelistPackageService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 白名单套餐Controller
 * @date 2023-03-13
 */
@Slf4j
@RestController
@RequestMapping("/whitelist/package")
@Api(tags = "白名单套餐管理")
public class WhitelistPackageController {

    @Autowired
    private WhitelistPackageService whitelistPackageService;

    @GetMapping("/page")
    @ApiOperation(value = "分页查询白名单套餐", notes = "分页查询白名单套餐")
    public Response<PageResult<WhitelistCorpInfoVO>> pageWhitelistPackage(@RequestParam("corpId") String corpId, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
       // 创建WhitelistPackageQueryReq对象
       WhitelistPackageQueryReq req = new WhitelistPackageQueryReq();
       req.setCorpId(corpId);
       req.setPageNum(pageNum);
       req.setPageSize(pageSize);
       return Response.ok(whitelistPackageService.pageWhitelistPackage(req));
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加白名单套餐（号段新增）", notes = "添加白名单套餐（号段新增）")
    @OperationLog(operationName = "白名单套餐管理——添加白名单套餐（号段新增）", operationType = OperationTypeEnum.ADD)
    public Response<Void> addWhitelistPackage(@RequestBody @Valid WhitelistPackageDTO dto) {
        whitelistPackageService.addWhitelistPackage(dto);
        return Response.ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改白名单套餐（号段编辑）", notes = "添加白名单套餐（号段编辑）")
    @OperationLog(operationName = "白名单套餐管理——添加白名单套餐（号段编辑）", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> updateWhitelistPackage(@RequestBody @Valid WhitelistPackageDTO dto) {
        whitelistPackageService.updateWhitelistPackage(dto);
        return Response.ok();
    }

    @PostMapping(value ="/file/update", headers = "content-type=multipart/form-data")
    @ApiOperation(value = "白名单套餐文件操作", notes = "白名单套餐文件操作，支持号码新增和删除")
    @OperationLog(operationName = "白名单套餐管理——白名单套餐文件操作", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> handleWhitelistPackageFile(@Validated WhitelistPackageFileReq req) throws IOException {
        whitelistPackageService.handleWhitelistPackageFile(req);
        return Response.ok(null, "操作已提交，请稍后在列表中查看结果");
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除白名单套餐", notes = "删除白名单套餐")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "corpId", value = "渠道商ID", required = true)
    })
    @OperationLog(operationName = "白名单套餐管理——删除白名单套餐", operationType = OperationTypeEnum.DELETE)
    public Response<Void> deleteWhitelistPackage(@RequestBody Map<String, String> requestBody) {
        String corpId = requestBody.get("corpId");
        if (corpId == null || corpId.trim().isEmpty()) {
            return Response.error("corpId不能为空");
        }
        whitelistPackageService.deleteWhitelistCorpId(corpId);
        return Response.ok();
    }

    @GetMapping("/detail")
    @ApiOperation(value = "查询白名单套餐详情", notes = "查询白名单套餐详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "corpId", value = "渠道商ID", required = true),
    })
    public Response<WhitelistPackageVO> getWhitelistPackageDetail(@RequestParam String corpId,
                                                                  @RequestParam String iccid) {
        return Response.ok(whitelistPackageService.getWhitelistPackageDetail(corpId, iccid));
    }
    //根据type下载白名单套餐模板文件，参照：
     /**
     * 下载模板
     */
    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载模板", notes = "根据模板类型下载xlsx模板文件")
    public ResponseEntity<byte[]> downloadTemplate(@RequestParam String templateType, HttpServletRequest request) {
        try {
            log.info("开始下载模板文件，templateType: {}", templateType);
            Resource resource = whitelistPackageService.getTemplateResource(templateType);
            byte[] fileContent = StreamUtils.copyToByteArray(resource.getInputStream());
            String fileName = resource.getFilename();
            log.info("成功读取模板文件: {}, 大小: {} 字节", fileName, fileContent.length);

            HttpHeaders headers = new HttpHeaders();

            // 获取请求头中的User-Agent信息
            String userAgent = request.getHeader("User-Agent");

            // 根据不同的浏览器使用不同的编码方式
            if (userAgent.contains("MSIE") || userAgent.contains("Trident") || userAgent.contains("Edge")) {
                // IE、Edge浏览器
                fileName = URLEncoder.encode(fileName, "UTF-8");
                // 替换空格为%20
                fileName = fileName.replace("+", "%20");
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            } else if (userAgent.contains("Mozilla")) {
                // 火狐、Chrome等浏览器
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            } else {
                // 其他浏览器
                fileName = URLEncoder.encode(fileName, "UTF-8");
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            }

            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            headers.setContentLength(fileContent.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileContent);
        } catch (Exception e) {
            log.error("下载模板文件失败，templateType: {}, 错误: {}", templateType, e.getMessage(), e);
            return ResponseEntity.status(500).body(("下载模板文件失败: " + e.getMessage()).getBytes());
        }
    }

    @GetMapping("/getWhitelistPackageId")
    @ApiOperation(value = "根据企业ID和ICCID查询白名单套餐ID", notes = "根据企业ID和ICCID查询白名单套餐ID")
    public Response<String> getWhitelistPackageId(@RequestParam String corpId, @RequestParam String iccid) {
        return Response.ok(whitelistPackageService.getWhitelistPackageId(corpId, iccid));
    }

    @GetMapping("/getWhitelistPackageIdByRange")
    @ApiOperation(value = "根据企业ID和ICCID查询号段范围内的白名单套餐ID", notes = "根据企业ID和ICCID查询号段范围内的白名单套餐ID")
    public Response<String> getWhitelistPackageIdByRange(@RequestParam String corpId, @RequestParam String iccid) {
        return Response.ok(whitelistPackageService.getWhitelistPackageIdByRange(corpId, iccid));
    }   

 


}