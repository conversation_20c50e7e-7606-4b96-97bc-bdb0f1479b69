package com.ebupt.cmi.clientmanagement.domain.dto.channel;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class MktChannelPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合作模式
     */
    private String companyName;
    /**
     * 公司名称
     */
    private String cooperationMode;
    /**
     * 渠道商id集合
     */
    private List<String> corpId;

    @NotNull(message = "页数不能为空")
    private Integer pageNum;

    @NotNull(message = "每条页数不能为空")
    private Integer pageSize;
}
