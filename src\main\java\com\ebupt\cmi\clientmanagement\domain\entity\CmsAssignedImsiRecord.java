package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * cms_assigned_imsi_record
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsAssignedImsiRecord implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * imsi号
     */
    private String imsi;

    private String msisdn;

    /**
     * 渠道商id
     */
    private String corpId;

    /**
     * 下行流量
     */
    private Long flowDownlink;

    /**
     * 上行流量
     */
    private Long flowUplink;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 国家码
     */
    private String mcc;

    /**
     * mcc+mnc
     */
    private String plmnlist;

    /**
     * 网络类型
     */
    private String ratType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 总流量(byte)
     */
    private Long flowCount;

    private String dateBelongTo;

    /**
     * 创建时间默认值：CURRENT_TIMESTAMP
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}