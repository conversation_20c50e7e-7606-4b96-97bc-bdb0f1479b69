package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsChannelRealnameInfo对象", description="")
public class CmsChannelRealnameInfo implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "auth_id")
    private Long authId;

    @ApiModelProperty(value = "MSISDN号码")
    private String msisdn;

    @ApiModelProperty(value = "imsi号码")
    private String imsi;

    @ApiModelProperty(value = "iccid号码")
    private String iccid;

    @ApiModelProperty(value = "订购唯一ID")
    private String orderUniqueId;

    @ApiModelProperty(value = "证件ID")
    private String certificatesId;

    @ApiModelProperty(value = "证件类型 1、护照 2、港澳通行证 3、香港身份证 4、澳门身份证")
    private String certificatesType;

    @ApiModelProperty(value = "认证规则编码")
    private String ruleCode;

    @ApiModelProperty(value = "护照国家")
    private String passportCountry;

    @ApiModelProperty(value = "出生年月 YYYYMMDD")
    private String dateOfBirth;

    @ApiModelProperty(value = "用户输入姓名(英文)")
    private String inputName;

    @ApiModelProperty(value = "用户输入姓名(中文)")
    private String inputNameCh;

    @ApiModelProperty(value = "ocr证件识别姓名")
    private String ocrName;

    @ApiModelProperty(value = "认证通知手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "认证通知邮箱")
    private String email;

    @ApiModelProperty(value = "认证规则名称")
    private String ruleName;

    @ApiModelProperty(value = "图片文件名")
    private String fileName;

    @ApiModelProperty(value = "ocr认证失败原因 1、姓名校验不一致 2、证件已过期 3、证件ID校验不一致 4、未满16周岁 0、OCR识别异常")
    private String errorDesc;

    @ApiModelProperty(value = "认证状态 1、待认证 2、认证中 3、认证通过 4、认证失败 5、证件已过期")
    private String authStatus;

    @ApiModelProperty(value = "使用状态 1、处理中 2、在用 3、备份")
    private String useStatus;

    @ApiModelProperty(value = "认证对象 1、卡 2、订单")
    private String authObj;

    @ApiModelProperty(value = "证件有效期默认2099年12月31日")
    @TableField("certificatesTime")
    private Date certificatesTime;

    @ApiModelProperty(value = "认证次数，默认值0")
    private Integer authNum;

    @ApiModelProperty(value = "是否重新认证过，默认1 1：未重新认证过 2：已重新认证过")
    private String isRepeat;

    @ApiModelProperty(value = "创建时间")
      @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
      @TableField(fill = FieldFill.INSERT)
    private Date updateTime;

    @ApiModelProperty(value = "认证时间")
    private Date authTime;

    @ApiModelProperty(value = "出生年月YYYYMMDD")
    private String ocrBirthDate;

    @ApiModelProperty(value = "证件有效期YYYYMMDD ")
    private String ocrExpireDate;

    @ApiModelProperty(value = "证件国家码")
    private String ocrCountryCode;

    @ApiModelProperty(value = "认证方式")
    private String authMode;

    @ApiModelProperty(value = "证件号")
    private String ocrNumber;

    @ApiModelProperty(value = "取消时间")
    private String cancelTime;

    /**
     * OCR识别 证件类型
     * 1：护照
     * 2：港澳通行证
     * 3：香港身份证
     * 4：澳门身份证
     */
    private String ocrCertificatesType;

    /**
     * 用户输入证件过期日期
     * 格式为 YYYYMMDD
     */
    private String expireDate;



}
