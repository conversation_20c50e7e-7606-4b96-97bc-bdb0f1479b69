package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.vo.AssociatedCard;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * HImsiVO
 * 主卡信息
 * @Author: <PERSON>haoqiankun
 * @Date: 2021/5/17 10:31
 */
@Data
@ToString
public class HImsiVO {
    private String hImsi;
    private String msisdn;
    private String iccid;
    private Integer status ;
    private Integer imsiChangeMode ;
    private String cardHlrId;
    private String cardOtaId;
    private String createTime;
    private String expireTime;
    private String lastModifyTime;
    private Map<String,String> ext;
    /**
     * 卡套餐的激活方式(新增硬卡该参数无意义)
     * 0: 手动激活 1: 自动激活
     */
    private String serviceUsageMode;

    private String limited;

    private List<AssociatedCard.RealRule> realRuleList;

    @JsonIgnore
    private String realnameId;
}
