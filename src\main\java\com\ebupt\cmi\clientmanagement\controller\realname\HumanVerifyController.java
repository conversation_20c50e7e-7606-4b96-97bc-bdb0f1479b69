package com.ebupt.cmi.clientmanagement.controller.realname;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ReUtil;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.AuthEditVo;
import com.ebupt.cmi.clientmanagement.domain.vo.VerifyVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.service.realname.HumanVerifyService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HumanVerifyController.java
 * @Description 实名认证人工审核接口
 * @createTime 2021年12月02日 11:30:00
 */

@Slf4j
@Api(tags = "实名认证人工审核接口")
@RestController
@RequestMapping("/humanVerify")
public class HumanVerifyController {

    @Autowired
    HumanVerifyService humanVerifyService;

    private final static String EMAILPATTERN = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";

    @ApiOperation(value = "人工审核")
    @GetMapping("/verify")
    @NormalLog
    @OperationLog(operationName = "实名认证-人工审核接口", operationType = OperationTypeEnum.AUDIT)
    public Response humanVerify(@Validated VerifyVO verifyVO) {

        return humanVerifyService.verify(verifyVO);
    }

    @ApiOperation(value = "人工认证修改")
    @PostMapping("/authenticate/update")
    @NormalLog
    @OperationLog(operationName = "实名认证-人工认证修改", operationType = OperationTypeEnum.UPDATE)
    public Response authenticateUpdate(@Validated AuthEditVo vo) {
        humanVerifyService.authenticateUpdate(vo);
        return Response.ok();
    }

    @PostMapping("/exportInfo/{exportType}/{emailAddress}")
    @ApiOperation(value = "认证信息导出接口")
    public Response exportInfo(MultipartFile file, @PathVariable(value = "exportType") String exportType,
                               @PathVariable("emailAddress") String emailAddress, @RequestHeader("userName") String username) {

        //1、全部导出，2、文件导出
        username = URLDecoder.decode(username, StandardCharsets.UTF_8);
        boolean isMatch = ReUtil.isMatch(EMAILPATTERN, emailAddress);

        if (!isMatch) {
            throw new BizException("邮箱不符合规则");
        }

        //对文件进行校验
        if ("2".equals(exportType)) {
            Optional.ofNullable(file).orElseThrow(() -> new BizException("Upload file cannot be empty"));
            String[] split = file.getOriginalFilename().split("\\.");
            String suffix = split[split.length - 1];
            if (!"csv".equals(suffix.toLowerCase())) {
                Response.error("Please upload a text file in csv format");
            }
            if (file.isEmpty()) {
                Response.error("Please do not upload empty files");
            }

        }

        humanVerifyService.handleStartPoint(file, exportType, emailAddress, username);

        return Response.ok();
    }
}
