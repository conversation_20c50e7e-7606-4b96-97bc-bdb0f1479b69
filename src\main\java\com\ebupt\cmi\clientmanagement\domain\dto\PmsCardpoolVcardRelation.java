package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PmsCardpoolVcardRelation implements Serializable {
    private Long id;

    /**
     * IMSI号码
     */
    private String imsi;

    /**
     * 手机号
     */
    private String msisdn;

    /**
     * 制卡IMSI
     */
    private String madeImsi;

    /**
     * ICCID号码
     */
    private String iccid;

    /**
     * 状态：1：待分配,2：已分配,3: 使用中,4：暂停,5：冻结
     */
    private String status;

    /**
     * KI
     */
    private String ki;

    /**
     * OPC
     */
    private String opc;

    /**
     * HSS开户状态 1成功,2失败
     */
    private String hssOpenStatus;

    /**
     * UPCC开户状态：1成功,2失败
     */
    private String upccOpenStatus;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 归属卡池ID
     */
    private String poolId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     *冻结开始时间
     */
    private Date freezeStartTime;

    /**
     *冻结结束时间
     */
    private Date freezeEndTime;

    private String upccSignBizId;

    private String isSignUpcc;

    private Integer upccRate;

    private String supportHotspot;

    private String unit;

    private static final long serialVersionUID = 1L;
}