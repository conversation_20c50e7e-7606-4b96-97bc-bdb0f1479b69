package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * LanguageEnum
 * 语音类型
 *
 * @Author: <PERSON><PERSON>qiankun
 * @Date: 2021/5/11 14:34
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum LanguageEnum {

    /**
     * 中文繁体
     */
    ZH_TW("1", "繁体中文"),
    /**
     * 英文
     */
    ES("2", "英文"),
    /**
     * 中文简体
     */
    ZH_CN("3", "简体中文");


    private String type;
    private String language;

    public static String getType(String language) {
        for (LanguageEnum p : LanguageEnum.values()) {
            if (p.getLanguage().equals(language)) {
                return p.getType();
            }
        }
        log.warn("输入不符合要求：{}", language);
        return "";
    }

    public static String getLanguage(String type) {
        for (LanguageEnum p : LanguageEnum.values()) {
            if (p.getType().equals(type)) {
                return p.getLanguage();
            }
        }
        log.warn("输入不符合要求：{}", type);
        return "";
    }
}
