package com.ebupt.cmi.clientmanagement.consumer.uitils;

import com.ebupt.cmi.clientmanagement.config.CustonIdentifierGenerator;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsDelayMessageRecord;
import com.ebupt.cmi.clientmanagement.mapper.CmsDelayMessageRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SendMessageWrapper.java
 * @Description 统一丢队方法，封装对外提供
 * @createTime 2022年02月28日 14:50:00
 */

@Component
@Slf4j
public class SendMessageWrapper {

    @Autowired
    protected RabbitTemplate rabbitTemplate;
    @Resource
    private CmsDelayMessageRecordMapper cmsDelayMessageRecordMapper;

    @Value("${MAX_DELAY_TIME:**********}")
    private long MAX_DELAY_TIME;

    /**
     * 通用丢队方法
     *
     * @param message   消息
     * @param queueEnum 队列枚举
     */
    public void throwMessageToQueue(String message, QueueEnum queueEnum) {
        rabbitTemplate.convertAndSend(queueEnum.getExchangeName(),
                queueEnum.getRoutingKey(), message);
        log.info("===========Message had been sended:{}==============", message);
    }

    /**
     * 带有过期时间的方法
     *
     * @param message   消息
     * @param queueEnum 队列枚举
     * @param ttl       消息过期时间，单位毫秒
     */
    public void throwMessageToQueue(String message, QueueEnum queueEnum, Long ttl) {
        Date date = new Date(System.currentTimeMillis() + ttl);
        CmsDelayMessageRecord cmsDelayMessageRecord = new CmsDelayMessageRecord(null, queueEnum.getName(), message, date, new Date());
        if (ttl > MAX_DELAY_TIME) {
            log.warn("延迟时间大于:{}ms，延迟队列失效, 用定时程序补偿", MAX_DELAY_TIME);
            cmsDelayMessageRecord.setScheduledCompensation("1");
            cmsDelayMessageRecordMapper.insert(cmsDelayMessageRecord);
            return;
        }
        cmsDelayMessageRecordMapper.insert(cmsDelayMessageRecord);
        //给延迟队列发送消息
        rabbitTemplate.convertAndSend(queueEnum.getExchangeName(), queueEnum.getRoutingKey(), message, message1 -> {
            //给消息设置延迟毫秒值
            message1.getMessageProperties().setMessageId(String.valueOf(cmsDelayMessageRecord.getId()));
            message1.getMessageProperties().setHeader("x-delay", ttl);
            return message1;
        });

        log.info("===========Message had been sended:{}==============", message);
    }

    public void throwMessageToQueue(String message, QueueEnum queueEnum, Long ttl, long id) {

        //给延迟队列发送消息
        rabbitTemplate.convertAndSend(queueEnum.getExchangeName(), queueEnum.getRoutingKey(), message, message1 -> {
            //给消息设置延迟毫秒值
            message1.getMessageProperties().setMessageId(String.valueOf(id));
            message1.getMessageProperties().setHeader("x-delay", ttl);
            return message1;
        });

        log.info("===========Message had been sended:{}==============", message);
    }

}
