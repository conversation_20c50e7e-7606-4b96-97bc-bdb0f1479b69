package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MyOwnPage.java
 * @Description 自定义分页类
 * @createTime 2021年05月10日 18:06:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MyOwnPage<T> {

    public MyOwnPage(List<T> record, Long total, Long current, Long pageSize) {
        this.record = record;
        this.total = total;
        this.current = current;
        if (total % pageSize == 0) {
            this.pages = (total / pageSize);
        } else {
            this.pages = (total / pageSize + 1);
        }
        this.size = pageSize;
    }

    List<T> record;

    /**
     * 总页数
     */
    Long pages;

    /**
     * 总数据量
     */
    Long total;

    /**
     * 单页总数
     */
    Long size;

    /**
     * 当前页码
     */
    Long current;
}
