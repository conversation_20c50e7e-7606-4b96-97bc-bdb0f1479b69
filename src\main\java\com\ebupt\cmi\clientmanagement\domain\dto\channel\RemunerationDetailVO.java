package com.ebupt.cmi.clientmanagement.domain.dto.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * RemunerationDetailVO
 *
 * @Author: zhaoqiankun
 * @Date: 2021/6/17 15:53
 */
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@ApiModel(description = "酬金详情")
public class RemunerationDetailVO {

    @ApiModelProperty(value = "iccid")
    private String iccid;

    @ApiModelProperty(value = "套餐名称")
    private String nameEn;

    @ApiModelProperty(value = "购买时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderDate;

    @ApiModelProperty(value = "激活时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activeTime;

    @ApiModelProperty(value = "购买币种")
    private String currencyCode;

    @ApiModelProperty(value = "酬金类型")
    private String remunerationType;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "酬金")
    private BigDecimal remunerationAmount;

    @ApiModelProperty(value = "订单ID")
    private String orderId;

    @ApiModelProperty(value = "订购渠道")
    private String orderChannel;

    @ApiModelProperty(value = "渠道商名称")
    private String corpName;

    @ApiModelProperty(value = "数量")
    private Integer quantity;
}
