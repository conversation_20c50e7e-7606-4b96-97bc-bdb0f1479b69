package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 流量统计实体
 * @date 2021/6/9 10:41
 */
@Data
@TableName
public class StatImsiflowDay {

    private Long id;

    private Date statTime;

    private String imsi;

    private String himsi;

    private String mcc;

    private Float flowCount;

    private String internetType;

    private String apn;

    private String packageUniqueId;

    private String packageId;

    private String packageName;

    private String nameTw;

    private String nameEn;

    private Date createTime;

}
