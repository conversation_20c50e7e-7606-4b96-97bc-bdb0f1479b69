package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 审批状态
 * @date 2021/5/7 11:21
 */

@AllArgsConstructor
@Getter
public enum CheckStatus {
    /**
     * 1：新建待审批
     */
    NEW_NEED_APPROVAL("1"),
    /**
     * 2：通过
     */
    PASS("2"),
    /**
     * 3：不通过
     */
    NOT_PASS("3"),
    /**
     * 4：删除待审批
     */
    DELETE_NEED_APPROVAL("4"),

    /**
     * 5：修改待审批
     */
    ALTER_NEED_APPROVAL("5");

    String status;
}
