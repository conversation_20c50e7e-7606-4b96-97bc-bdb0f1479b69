package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 酬金类型枚举
 * <AUTHOR>
 * @date 2021-5-26 20:05:31
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum RemunerationTypeEnum {

    /**
     * 直接收入
     */
    DIRECT_INCOME("1"),

    /**
     * 间接收入
     */
    INDIRECT_INCOME("2"),

    /**
     * 白卡
     */
    CARD_ONLY("3");

    private String value;

}
