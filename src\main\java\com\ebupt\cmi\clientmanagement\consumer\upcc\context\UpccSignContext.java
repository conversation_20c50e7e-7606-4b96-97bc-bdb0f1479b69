package com.ebupt.cmi.clientmanagement.consumer.upcc.context;


import com.ebupt.cmi.clientmanagement.domain.entity.CmsCardUpccRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageDirectionRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpccSignContext {

    private String msisdn;

    private String newUpccSignBizId;

    private String oldUpccSignBizId;

    private CmsCardUpccRecord cmsCardUpccRecord;

}
