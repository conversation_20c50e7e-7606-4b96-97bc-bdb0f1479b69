# 用户订单查询接口实现分析报告

## 1. 概述

### 1.1 业务背景
userQueryOrder方法是PackageEndServiceImpl类中的核心订单查询接口，负责为不同角色的用户提供订单查询服务。该接口支持多种用户角色（手机号、邮箱、渠道商等），提供灵活的订单状态筛选和分页查询功能。

### 1.2 方法签名和核心职责
```java
@Override
public List<UserQueryOrderDTO> userQueryOrder(UserQueryOrderVO userQueryOrderVO) {
    // 核心职责：
    // 1. 用户角色权限验证
    // 2. 订单状态条件映射
    // 3. 分页查询订单主表
    // 4. 查询订单详情和套餐信息
    // 5. 数据组装和地址解析
    // 6. 返回完整的订单信息
}
```

## 2. 方法实现逻辑深度分析

### 2.1 输入参数分析 - UserQueryOrderVO

#### 2.1.1 核心字段定义
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户查询套餐接口")
public class UserQueryOrderVO {
    private String role;                    // 用户角色：104-手机号，106-渠道商，107-邮箱
    private String userId;                  // 用户ID（根据角色类型确定具体含义）
    private OrderQueryCond orderQueryCond; // 订单查询条件
    private int pageNum;                    // 页码
    private int pageSize;                   // 页大小
    private String language;                // 语言设置
    private String country;                 // 国家代码
}
```

#### 2.1.2 OrderQueryCond查询条件
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderQueryCond {
    private String orderID;     // 订单ID（精确查询）
    private Integer status;     // 订单状态筛选条件
    // 状态值含义：
    // 1 - "待发货"
    // 2 - "已发货" 
    // 3 - "使用中"
    // 4 - "部分退订"
    // 5 - "已退订"
}
```

### 2.2 返回结果分析 - UserQueryOrderDTO

#### 2.2.1 主要字段结构
```java
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserQueryOrderDTO {
    private String orderID;                 // 订单唯一标识
    private String createUserID;            // 创建用户ID
    private String orderType;               // 订单类型（1-卡，2-套餐，3-卡+套餐，7-加油包）
    private Integer status;                 // 订单状态（1-待发货，2-已发货，3-部分退订，4-全额退订）
    private List<OrderItem> orderItems;     // 订单项列表
    private Long totalAmount;               // 订单总金额
    private Integer currency;               // 货币代码
    private String channelID;               // 渠道ID（仅渠道用户返回）
    private Date createTime;                // 创建时间
    private String comments;                // 订单备注
    private Address address;                // 收货地址信息
    private Logistic logistic;              // 物流信息
}
```

#### 2.2.2 OrderItem订单项结构
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderItem {
    private String orderID;                 // 主订单ID
    private String orderItemID;             // 子订单ID
    private String price;                   // 价格
    private String orderStatus;             // 子订单状态
    private ProductOffering productOffering; // 产品信息
    private Date createTime;                // 创建时间
}
```

### 2.3 执行流程详细分析

#### 2.3.1 用户角色处理逻辑
```java
String userId = userQueryOrderVO.getUserId();
if (RoleEnum.PHONENUMBER.getRole().equals(userQueryOrderVO.getRole()) ||
    CreateOrderReq.RoleTypeEnum.EMAIL.matches(Integer.valueOf(userQueryOrderVO.getRole()))) {
    userId = userQueryOrderVO.getUserId(); // 手机号和邮箱用户直接使用userId
}
```

**角色类型说明**：
- **104 (PHONENUMBER)**: 手机号用户，userId为手机号
- **106 (CHANNEL)**: 渠道商用户，userId为渠道商corpId
- **107 (EMAIL)**: 邮箱用户，userId为邮箱地址
- **105 (TEMP_ACOUNT)**: 临时账户
- **102 (ICCID)**: ICCID用户

#### 2.3.2 分页查询构建
```java
// 构建分页对象
IPage<ChannelOrder> page = new Page<>(userQueryOrderVO.getPageNum(), userQueryOrderVO.getPageSize());

// 构建查询条件
LambdaQueryWrapper<ChannelOrder> orderQueryWrapper = new LambdaQueryWrapper<ChannelOrder>()
    .eq(ChannelOrder::getOrderUserId, userId)           // 按用户ID查询
    .orderByDesc(ChannelOrder::getCreateTime);          // 按创建时间倒序

// 订单ID精确查询
if (StringUtils.isNotBlank(orderQueryCond.getOrderID())) {
    orderQueryWrapper.eq(ChannelOrder::getOrderUniqueId, orderQueryCond.getOrderID());
}
```

## 3. 订单状态映射逻辑深度分析

### 3.1 查询条件状态映射

#### 3.1.1 输入状态到数据库状态映射
```java
if (Objects.nonNull(orderQueryCond.getStatus())) {
    String orderStatusCondition = orderQueryCond.getStatus().toString();
    
    if ("1".equals(orderStatusCondition)) {
        // 待发货状态映射
        orderQueryWrapper.in(ChannelOrder::getOrderStatus, Arrays.asList(
            OrderStatus.WAIT_FOR_DELIVERY.getStatus(),              // "1" - 待发货
            ChannelOrder.OrderStatusEnum.ORDER_PROCESS.getValue(),   // 订单处理中
            ChannelOrder.OrderStatusEnum.ORDER_FAIL.getValue(),      // 订单失败
            ChannelOrder.OrderStatusEnum.DELIVER_PROCESS.getValue(), // 发货处理中
            ChannelOrder.OrderStatusEnum.UNBIND_PROCESS.getValue()   // 解绑处理中
        ));
    } else if ("2".equals(orderStatusCondition) || "3".equals(orderStatusCondition)) {
        // 已发货/使用中状态映射
        orderQueryWrapper.eq(ChannelOrder::getOrderStatus, OrderStatus.COMPLETE.getStatus()); // "2" - 完成
    } else if ("4".equals(orderStatusCondition)) {
        // 部分退订状态映射
        orderQueryWrapper.in(ChannelOrder::getOrderStatus, Arrays.asList(
            OrderStatus.PART_SALES_RETURN.getStatus(),                    // "5" - 部分退订
            ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_PROCESS.getValue()   // 退订处理中
        ));
    } else if ("5".equals(orderStatusCondition)) {
        // 全额退订状态映射
        orderQueryWrapper.eq(ChannelOrder::getOrderStatus, OrderStatus.SALES_RETURN.getStatus()); // "3" - 已退订
    }
}
```

### 3.2 输出状态映射逻辑

#### 3.2.1 数据库状态到前端展示状态映射
```java
final String orderStatus = singleOrder.getOrderStatus();

if (ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.ORDER_PROCESS.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.DELIVER_PROCESS.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.UNBIND_PROCESS.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.ORDER_FAIL.getValue().equals(orderStatus)) {
    orderDTO.setStatus(1); // 待发货
} else if (ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.PARTIAL_DELIVERED.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue().equals(orderStatus)) {
    orderDTO.setStatus(2); // 已发货
} else if (ChannelOrder.OrderStatusEnum.COMBINATION.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_PROCESS.getValue().equals(orderStatus)
    || ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE.getValue().equals(orderStatus)) {
    orderDTO.setStatus(3); // 部分退订
} else if (ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue().equals(orderStatus)) {
    orderDTO.setStatus(4); // 全额退订
} else {
    orderDTO.setStatus(999); // 未知状态
}
```

### 3.3 订单状态生命周期

#### 3.3.1 完整状态流转图
```
订单创建 → 待发货(1) → 已发货(2) → 完成
    ↓         ↓         ↓
  订单失败   部分退订(4)  全额退订(5)
    ↓         ↓         ↓
  重新处理   补发/退款   全额退款
```

#### 3.3.2 状态枚举定义
```java
public enum OrderStatusEnum {
    NOT_DELIVERY("1"),          // 待发货
    FINISHED("2"),              // 已完成
    UNSUBSCRIBE("3"),           // 已退订
    ORDER_PROCESS("4"),         // 订单处理中
    ORDER_FAIL("5"),            // 订单失败
    DELIVER_PROCESS("6"),       // 发货处理中
    PARTIAL_DELIVERED("7"),     // 部分发货
    UNBIND_PROCESS("8"),        // 解绑处理中
    UNSUBSCRIBE_PROCESS("9"),   // 退订处理中
    COMBINATION("10"),          // 复合状态
    PARTIAL_UNSUBSCRIBE("11"),  // 部分退订
    UNSUBSCRIBE_NOT_AUDIT("12") // 退订待审核
}
```

## 4. 数据查询和组装逻辑分析

### 4.1 主订单查询逻辑

#### 4.1.1 分页查询实现
```java
// 执行分页查询
IPage<ChannelOrder> orderList = channelOrderMapper.selectPage(page, orderQueryWrapper);
final List<ChannelOrder> orders = orderList.getRecords();

// 空结果处理
if (orders.isEmpty()) {
    log.debug("未查询到订单数据");
    return Collections.emptyList();
}
```

#### 4.1.2 套餐信息批量查询
```java
// 提取所有套餐ID
final Set<String> packageIdHashSet = orders.stream()
    .map(ChannelOrder::getPackageId)
    .collect(Collectors.toSet());

// 批量查询套餐信息
Map<String, CmsOrderQueryPackageDTO> packageDTOMap = pmsFeignClient.getPackageList(
    CmsOrderQueryPackageVO.builder()
        .packageIdHashSet(packageIdHashSet)
        .build()
).getData();

// 设置语言信息
packageDTOMap.values().forEach(packageDTO -> 
    packageDTO.setInfoByLanguage(userQueryOrderVO.getCountry()));
```

### 4.2 订单详情查询和组装

#### 4.2.1 大订单检查逻辑
```java
// 检查是否为大订单（批量订单）
if (cmsBigOrderMapper.selectOne(Wrappers.lambdaQuery(CmsBigOrder.class)
    .eq(CmsBigOrder::getOrderUniqueId, singleOrder.getOrderUniqueId())) == null) {
    
    // 非大订单，查询详细的子订单信息
    List<ChannelOrderDetail> channelOrderDetails = channelOrderDetailMapper.selectList(
        Wrappers.lambdaQuery(ChannelOrderDetail.class)
            .select(ChannelOrderDetail::getId, ChannelOrderDetail::getCreateTime, 
                   ChannelOrderDetail::getAmount, ChannelOrderDetail::getPackageId, 
                   ChannelOrderDetail::getIccid, ChannelOrderDetail::getOrderStatus,
                   ChannelOrderDetail::getPackageName, ChannelOrderDetail::getNameEn, 
                   ChannelOrderDetail::getNameTw)
            .eq(ChannelOrderDetail::getOrderId, singleOrder.getId()));
}
```

#### 4.2.2 订单项数据组装
```java
for (ChannelOrderDetail seedOrder : channelOrderDetails) {
    OrderItem orderItem = new OrderItem();
    orderItem.setOrderID(singleOrder.getOrderUniqueId());
    orderItem.setOrderItemID(seedOrder.getId().toString());
    orderItem.setPrice(Optional.ofNullable(seedOrder.getAmount())
        .map(BigDecimal::toString)
        .orElse(StringPool.ZERO));
    orderItem.setCreateTime(toDate(seedOrder.getCreateTime()));
    orderItem.setOrderStatus(seedOrder.getOrderStatus());
    
    // 构建产品信息
    final String packageId = seedOrder.getPackageId();
    final CmsOrderQueryPackageDTO packageDTO = packageDTOMap.get(packageId);
    ProductOffering productOffering = ProductOffering.builder()
        .orderItemID(orderItemID)
        .productOfferingID(packageId)
        .iccid(seedOrder.getIccid())
        .packageID(packageId)
        .build();
    
    // 区分普通套餐和加油包
    if (!ChannelOrder.orderTypeEnum.FUELPACK.getValue().equals(singleOrder.getOrderType())) {
        // 普通套餐：从PMS获取套餐信息
        productOffering.setPackageName(Optional.ofNullable(packageDTO)
            .map(CmsOrderQueryPackageDTO::getName).orElse(null));
        productOffering.setPackageDesc(Optional.ofNullable(packageDTO)
            .map(CmsOrderQueryPackageDTO::getDesc).orElse(null));
        productOffering.setStartTime(Optional.ofNullable(packageDTO)
            .map(CmsOrderQueryPackageDTO::getStartTime).orElse(null));
        productOffering.setEndTime(Optional.ofNullable(packageDTO)
            .map(CmsOrderQueryPackageDTO::getEndTime).orElse(null));
    } else {
        // 加油包：使用本地存储的名称
        productOffering.setPackageName(seedOrder.getName(userQueryOrderVO.getCountry()));
    }
    
    orderItem.setProductOffering(productOffering);
    orderItems.add(orderItem);
}
```

### 4.3 地址信息解析逻辑

#### 4.3.1 地址字符串解析
```java
// 地址格式：国家|省份|城市地址|邮寄地址
String[] initAddress = {"", "", "", ""};
String address = singleOrder.getAddress();
String[] locationInfo = address == null ? initAddress : address.split("\\|");

final int length = locationInfo.length;
String countryName = length > 0 ? locationInfo[0] : "";
String province = length > 1 ? locationInfo[1] : "";
String cityName = length > 2 ? locationInfo[2] : "";
String mAddress = length > 3 ? locationInfo[3] : "";
```

#### 4.3.2 地址对象构建
```java
orderDTO.setAddress(Address.builder()
    .postCode(singleOrder.getPostCode())        // 邮编
    .phoneNumber(singleOrder.getPhoneNumber())  // 联系电话
    .email(singleOrder.getEmail())              // 邮箱
    .addressee(singleOrder.getAddressee())      // 收件人
    .countryName(countryName)                   // 国家
    .province(province)                         // 省份
    .cityName(cityName)                         // 城市
    .mAddress(mAddress)                         // 详细地址
    .build());
```

### 4.4 物流信息组装
```java
orderDTO.setLogistic(Logistic.builder()
    .logisticsOrderID(singleOrder.getLogistic())        // 物流单号
    .logisticsCompany(singleOrder.getLogisticCompany()) // 物流公司
    .build());
```

## 5. 业务规则和边界条件分析

### 5.1 用户角色权限处理逻辑

#### 5.1.1 权限验证机制
```java
// channelApiAccessCheck方法实现
@Override
public Boolean channelApiAccessCheck(String corpId, String role, String imsi, String iccid, String msisdn) {
    if (RoleEnum.CHANNEL.getRole().equals(role)) {
        log.debug("外部api渠道商接入 corpId：{} ,imsi：{} ,iccid：{}", corpId, imsi, iccid);

        // 查询卡所属的渠道商
        String checkCorpId = Optional.ofNullable(channelCardMapper.selectOne(
            Wrappers.lambdaQuery(ChannelCard.class)
                .select(ChannelCard::getCorpId)
                .eq(StringUtils.isNotBlank(iccid), ChannelCard::getIccid, iccid)
                .eq(StringUtils.isNotBlank(imsi), ChannelCard::getImsi, imsi)
                .eq(StringUtils.isNotBlank(msisdn), ChannelCard::getMsisdn, msisdn)
        )).map(ChannelCard::getCorpId)
          .orElseThrow(() -> new BizException("The card was not queried", "1000156"));

        // 获取渠道商及其子渠道商列表
        List<String> cropIdList = channelDistributorsService.getCropIdByParentId(corpId);

        // 检查权限：如果卡不属于该渠道商或其子渠道商，返回true（无权限）
        return !cropIdList.contains(checkCorpId);
    }
    return false; // 非渠道商用户默认有权限
}
```

#### 5.1.2 角色权限矩阵
| 角色类型 | 角色代码 | 权限范围 | 验证逻辑 |
|---------|---------|---------|---------|
| 手机号用户 | 104 | 查询自己的订单 | 直接使用userId查询 |
| 邮箱用户 | 107 | 查询自己的订单 | 直接使用userId查询 |
| 渠道商用户 | 106 | 查询渠道下所有订单 | 验证渠道商权限范围 |
| 临时账户 | 105 | 受限查询 | 特殊权限控制 |
| ICCID用户 | 102 | 基于ICCID查询 | ICCID关联验证 |

#### 5.1.3 渠道商特殊处理
```java
// 渠道商用户需要返回channelID
if (RoleEnum.CHANNEL.getRole().equals(userQueryOrderVO.getRole())) {
    final ChannelDistributorDetail distributorDetail = distributorDetailMapper.selectOne(
        Wrappers.lambdaQuery(ChannelDistributorDetail.class)
            .eq(ChannelDistributorDetail::getCorpId, userId));

    // 为所有订单设置渠道ID
    result.forEach(order -> order.setChannelID(distributorDetail.getAppKey()));
}
```

### 5.2 订单类型区分和处理差异

#### 5.2.1 订单类型枚举
```java
public enum orderTypeEnum {
    CARD("1"),          // 卡
    PACKAGE("2"),       // 套餐
    CARD_PACKAGE("3"),  // 卡+套餐
    FUELPACK("7");      // 加油包
}
```

#### 5.2.2 普通订单vs加油包订单处理差异
```java
// 订单类型判断
if (!ChannelOrder.orderTypeEnum.FUELPACK.getValue().equals(singleOrder.getOrderType())) {
    // 普通订单处理
    productOffering.setPackageName(Optional.ofNullable(packageDTO)
        .map(CmsOrderQueryPackageDTO::getName).orElse(null));
    productOffering.setPackageDesc(Optional.ofNullable(packageDTO)
        .map(CmsOrderQueryPackageDTO::getDesc).orElse(null));
    productOffering.setStartTime(Optional.ofNullable(packageDTO)
        .map(CmsOrderQueryPackageDTO::getStartTime).orElse(null));
    productOffering.setEndTime(Optional.ofNullable(packageDTO)
        .map(CmsOrderQueryPackageDTO::getEndTime).orElse(null));
} else {
    // 加油包订单处理
    productOffering.setPackageName(seedOrder.getName(userQueryOrderVO.getCountry()));
    // 加油包不设置描述、开始时间、结束时间
}
```

**处理差异说明**：
- **普通订单**: 从PMS系统获取完整的套餐信息，包括多语言名称、描述、有效期等
- **加油包订单**: 使用本地存储的名称信息，根据国家代码返回对应语言的名称

### 5.3 大订单vs普通订单处理

#### 5.3.1 大订单检查逻辑
```java
// 检查是否为大订单
if (cmsBigOrderMapper.selectOne(Wrappers.lambdaQuery(CmsBigOrder.class)
    .eq(CmsBigOrder::getOrderUniqueId, singleOrder.getOrderUniqueId())) == null) {

    // 非大订单：查询详细的子订单信息
    List<ChannelOrderDetail> channelOrderDetails = channelOrderDetailMapper.selectList(...);
    // 处理每个子订单详情
} else {
    // 大订单：不查询子订单详情，orderItems为空列表
    List<OrderItem> orderItems = new ArrayList<>();
}
```

**处理差异**：
- **普通订单**: 查询并返回详细的子订单信息，包括每个商品的价格、状态等
- **大订单**: 不返回子订单详情，减少数据传输量和查询复杂度

### 5.4 异常情况处理机制

#### 5.4.1 数据为空处理
```java
// 订单列表为空
if (orders.isEmpty()) {
    log.debug("未查询到订单数据");
    return Collections.emptyList();
}

// 套餐信息缺失处理
productOffering.setPackageName(Optional.ofNullable(packageDTO)
    .map(CmsOrderQueryPackageDTO::getName)
    .orElse(null)); // 优雅处理空值

// 金额信息处理
orderItem.setPrice(Optional.ofNullable(seedOrder.getAmount())
    .map(BigDecimal::toString)
    .orElse(StringPool.ZERO)); // 默认为"0"
```

#### 5.4.2 异常捕获和处理
```java
try {
    // 主要业务逻辑
    return result;
} catch (BizException e) {
    throw e; // 业务异常直接抛出
} catch (Exception e) {
    throw new BizException("查询订单失败", e); // 系统异常包装为业务异常
}
```

#### 5.4.3 参数验证
```java
// 状态参数验证
if (Objects.nonNull(orderQueryCond.getStatus())) {
    String orderStatusCondition = orderQueryCond.getStatus().toString();
    if (!"1".equals(orderStatusCondition) && !"2".equals(orderStatusCondition)
        && !"3".equals(orderStatusCondition) && !"4".equals(orderStatusCondition)
        && !"5".equals(orderStatusCondition)) {
        throw new BizException("请求参数status错误");
    }
}
```

## 6. 性能和安全考虑分析

### 6.1 大数据量场景性能分析

#### 6.1.1 性能瓶颈点识别
```java
// 1. 主订单分页查询 - 性能良好
IPage<ChannelOrder> orderList = channelOrderMapper.selectPage(page, orderQueryWrapper);

// 2. 套餐信息批量查询 - 潜在瓶颈
Map<String, CmsOrderQueryPackageDTO> packageDTOMap = pmsFeignClient.getPackageList(...);

// 3. 子订单详情查询 - 主要瓶颈
for (ChannelOrder singleOrder : orders) {
    List<ChannelOrderDetail> channelOrderDetails = channelOrderDetailMapper.selectList(...);
    // N+1查询问题
}
```

#### 6.1.2 性能优化建议
```java
// 1. 批量查询子订单详情
List<String> orderIds = orders.stream().map(ChannelOrder::getId).collect(Collectors.toList());
List<ChannelOrderDetail> allOrderDetails = channelOrderDetailMapper.selectList(
    Wrappers.lambdaQuery(ChannelOrderDetail.class)
        .in(ChannelOrderDetail::getOrderId, orderIds));

// 按订单ID分组
Map<String, List<ChannelOrderDetail>> orderDetailMap = allOrderDetails.stream()
    .collect(Collectors.groupingBy(detail -> detail.getOrderId().toString()));

// 2. 缓存套餐信息
@Cacheable(value = "packageInfo", key = "#packageId")
public CmsOrderQueryPackageDTO getPackageInfo(String packageId) {
    return pmsFeignClient.getPackageInfo(packageId);
}

// 3. 分页大小限制
if (userQueryOrderVO.getPageSize() > 100) {
    userQueryOrderVO.setPageSize(100); // 限制最大页大小
}
```

### 6.2 数据库查询效率评估

#### 6.2.1 查询语句分析
```sql
-- 主订单查询（性能良好）
SELECT * FROM cms_channel_order
WHERE order_user_id = ?
  AND order_unique_id = ?
  AND order_status IN (?, ?, ?)
ORDER BY create_time DESC
LIMIT ?, ?;

-- 子订单查询（需要优化）
SELECT id, create_time, amount, package_id, iccid, order_status, package_name, name_en, name_tw
FROM cms_channel_order_detail
WHERE order_id = ?;

-- 大订单检查（可优化）
SELECT * FROM cms_big_order
WHERE order_unique_id = ?;
```

#### 6.2.2 索引优化建议
```sql
-- 主订单表索引
CREATE INDEX idx_order_user_status_time ON cms_channel_order(order_user_id, order_status, create_time);
CREATE INDEX idx_order_unique_id ON cms_channel_order(order_unique_id);

-- 子订单表索引
CREATE INDEX idx_order_detail_order_id ON cms_channel_order_detail(order_id);

-- 大订单表索引
CREATE INDEX idx_big_order_unique_id ON cms_big_order(order_unique_id);
```

### 6.3 数据安全和权限控制

#### 6.3.1 数据脱敏处理
```java
// 敏感信息脱敏
public class SensitiveDataMasker {
    public static String maskPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber) || phoneNumber.length() < 8) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }

    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email) || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String username = parts[0];
        if (username.length() <= 2) {
            return email;
        }
        return username.substring(0, 2) + "****@" + parts[1];
    }
}

// 在返回结果中应用脱敏
orderDTO.getAddress().setPhoneNumber(SensitiveDataMasker.maskPhoneNumber(phoneNumber));
orderDTO.getAddress().setEmail(SensitiveDataMasker.maskEmail(email));
```

#### 6.3.2 权限控制增强
```java
// 增强权限验证
private void enhancedPermissionCheck(UserQueryOrderVO userQueryOrderVO) {
    String role = userQueryOrderVO.getRole();
    String userId = userQueryOrderVO.getUserId();

    // 1. 角色合法性验证
    if (!Arrays.asList("102", "104", "105", "106", "107").contains(role)) {
        throw new BizException("非法的用户角色");
    }

    // 2. 用户ID格式验证
    if (RoleEnum.PHONENUMBER.getRole().equals(role)) {
        if (!userId.matches("^1[3-9]\\d{9}$")) {
            throw new BizException("手机号格式不正确");
        }
    } else if ("107".equals(role)) {
        if (!userId.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$")) {
            throw new BizException("邮箱格式不正确");
        }
    }

    // 3. 查询频率限制
    String rateLimitKey = "order_query_" + role + "_" + userId;
    if (redisTemplate.opsForValue().increment(rateLimitKey) > 100) {
        redisTemplate.expire(rateLimitKey, 3600, TimeUnit.SECONDS);
        throw new BizException("查询频率过高，请稍后再试");
    }
}
```

#### 6.3.3 SQL注入防护
```java
// 使用MyBatis-Plus的LambdaQueryWrapper避免SQL注入
LambdaQueryWrapper<ChannelOrder> orderQueryWrapper = new LambdaQueryWrapper<ChannelOrder>()
    .eq(ChannelOrder::getOrderUserId, userId)  // 参数化查询
    .orderByDesc(ChannelOrder::getCreateTime);

// 避免直接字符串拼接
// 错误示例：
// String sql = "SELECT * FROM cms_channel_order WHERE order_user_id = '" + userId + "'";

// 正确示例：使用预编译语句
@Select("SELECT * FROM cms_channel_order WHERE order_user_id = #{userId}")
List<ChannelOrder> selectByUserId(@Param("userId") String userId);
```

### 6.4 系统稳定性改进建议

#### 6.4.1 连接池优化
```yaml
# 数据库连接池配置
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

#### 6.4.2 外部服务调用优化
```java
// Feign客户端超时配置
@FeignClient(name = "pms-service", configuration = PmsFeignConfig.class)
public interface PmsFeignClient {
    @GetMapping("/package/list")
    Response<Map<String, CmsOrderQueryPackageDTO>> getPackageList(CmsOrderQueryPackageVO vo);
}

@Configuration
public class PmsFeignConfig {
    @Bean
    public Request.Options options() {
        return new Request.Options(5000, 10000); // 连接超时5s，读取超时10s
    }

    @Bean
    public Retryer retryer() {
        return new Retryer.Default(100, 1000, 3); // 重试3次
    }
}
```

#### 6.4.3 监控和告警
```java
// 性能监控
@Component
public class OrderQueryMetrics {
    private final MeterRegistry meterRegistry;

    public void recordQueryTime(String role, long timeMs) {
        Timer.builder("order_query_time")
            .tag("role", role)
            .register(meterRegistry)
            .record(timeMs, TimeUnit.MILLISECONDS);
    }

    public void recordQueryCount(String role, String status) {
        Counter.builder("order_query_count")
            .tag("role", role)
            .tag("status", status)
            .register(meterRegistry)
            .increment();
    }
}

// 在方法中使用
@Around("execution(* com.ebupt.cmi.clientmanagement.service.impl.PackageEndServiceImpl.userQueryOrder(..))")
public Object monitorOrderQuery(ProceedingJoinPoint joinPoint) throws Throwable {
    long startTime = System.currentTimeMillis();
    UserQueryOrderVO vo = (UserQueryOrderVO) joinPoint.getArgs()[0];

    try {
        Object result = joinPoint.proceed();
        long executionTime = System.currentTimeMillis() - startTime;

        orderQueryMetrics.recordQueryTime(vo.getRole(), executionTime);
        orderQueryMetrics.recordQueryCount(vo.getRole(), "success");

        return result;
    } catch (Exception e) {
        orderQueryMetrics.recordQueryCount(vo.getRole(), "error");
        throw e;
    }
}
```

## 7. 代码重构建议

### 7.1 方法拆分优化

#### 7.1.1 当前方法过长问题
当前userQueryOrder方法包含约180行代码，承担了太多职责，建议拆分为以下几个方法：

```java
@Override
public List<UserQueryOrderDTO> userQueryOrder(UserQueryOrderVO userQueryOrderVO) {
    try {
        // 1. 参数验证和用户ID处理
        String userId = processUserId(userQueryOrderVO);

        // 2. 构建查询条件
        LambdaQueryWrapper<ChannelOrder> queryWrapper = buildQueryWrapper(userId, userQueryOrderVO.getOrderQueryCond());

        // 3. 执行分页查询
        List<ChannelOrder> orders = executePageQuery(queryWrapper, userQueryOrderVO.getPageNum(), userQueryOrderVO.getPageSize());

        // 4. 批量查询套餐信息
        Map<String, CmsOrderQueryPackageDTO> packageDTOMap = batchQueryPackageInfo(orders, userQueryOrderVO.getCountry());

        // 5. 组装订单数据
        List<UserQueryOrderDTO> result = assembleOrderData(orders, packageDTOMap, userQueryOrderVO);

        // 6. 处理渠道商特殊逻辑
        handleChannelSpecialLogic(result, userQueryOrderVO);

        return result;
    } catch (BizException e) {
        throw e;
    } catch (Exception e) {
        throw new BizException("查询订单失败", e);
    }
}

// 拆分后的子方法
private String processUserId(UserQueryOrderVO userQueryOrderVO) {
    String userId = userQueryOrderVO.getUserId();
    if (RoleEnum.PHONENUMBER.getRole().equals(userQueryOrderVO.getRole()) ||
        CreateOrderReq.RoleTypeEnum.EMAIL.matches(Integer.valueOf(userQueryOrderVO.getRole()))) {
        return userId;
    }
    return userId;
}

private LambdaQueryWrapper<ChannelOrder> buildQueryWrapper(String userId, OrderQueryCond orderQueryCond) {
    LambdaQueryWrapper<ChannelOrder> orderQueryWrapper = new LambdaQueryWrapper<ChannelOrder>()
        .eq(ChannelOrder::getOrderUserId, userId)
        .orderByDesc(ChannelOrder::getCreateTime);

    // 添加订单ID条件
    if (StringUtils.isNotBlank(orderQueryCond.getOrderID())) {
        orderQueryWrapper.eq(ChannelOrder::getOrderUniqueId, orderQueryCond.getOrderID());
    }

    // 添加状态条件
    addStatusCondition(orderQueryWrapper, orderQueryCond.getStatus());

    return orderQueryWrapper;
}
```

#### 7.1.2 状态映射逻辑优化
```java
// 使用策略模式优化状态映射
public interface OrderStatusStrategy {
    void applyStatusCondition(LambdaQueryWrapper<ChannelOrder> wrapper);
    Integer mapToDisplayStatus(String dbStatus);
}

@Component
public class OrderStatusStrategyFactory {
    private final Map<String, OrderStatusStrategy> strategies = new HashMap<>();

    @PostConstruct
    public void init() {
        strategies.put("1", new WaitingDeliveryStrategy());
        strategies.put("2", new DeliveredStrategy());
        strategies.put("3", new InUseStrategy());
        strategies.put("4", new PartialRefundStrategy());
        strategies.put("5", new FullRefundStrategy());
    }

    public OrderStatusStrategy getStrategy(String status) {
        return strategies.getOrDefault(status, new DefaultStrategy());
    }
}

// 具体策略实现
@Component
public class WaitingDeliveryStrategy implements OrderStatusStrategy {
    @Override
    public void applyStatusCondition(LambdaQueryWrapper<ChannelOrder> wrapper) {
        wrapper.in(ChannelOrder::getOrderStatus, Arrays.asList(
            OrderStatus.WAIT_FOR_DELIVERY.getStatus(),
            ChannelOrder.OrderStatusEnum.ORDER_PROCESS.getValue(),
            ChannelOrder.OrderStatusEnum.ORDER_FAIL.getValue(),
            ChannelOrder.OrderStatusEnum.DELIVER_PROCESS.getValue(),
            ChannelOrder.OrderStatusEnum.UNBIND_PROCESS.getValue()
        ));
    }

    @Override
    public Integer mapToDisplayStatus(String dbStatus) {
        if (Arrays.asList("1", "4", "5", "6", "8").contains(dbStatus)) {
            return 1; // 待发货
        }
        return 999; // 未知状态
    }
}
```

### 7.2 数据访问层优化

#### 7.2.1 批量查询优化
```java
// 优化前：N+1查询问题
for (ChannelOrder singleOrder : orders) {
    List<ChannelOrderDetail> channelOrderDetails = channelOrderDetailMapper.selectList(...);
}

// 优化后：批量查询
@Service
public class OrderDataService {

    public Map<Long, List<ChannelOrderDetail>> batchQueryOrderDetails(List<ChannelOrder> orders) {
        List<Long> orderIds = orders.stream().map(ChannelOrder::getId).collect(Collectors.toList());

        if (orderIds.isEmpty()) {
            return Collections.emptyMap();
        }

        List<ChannelOrderDetail> allDetails = channelOrderDetailMapper.selectList(
            Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .select(ChannelOrderDetail::getId, ChannelOrderDetail::getCreateTime,
                       ChannelOrderDetail::getAmount, ChannelOrderDetail::getPackageId,
                       ChannelOrderDetail::getIccid, ChannelOrderDetail::getOrderStatus,
                       ChannelOrderDetail::getPackageName, ChannelOrderDetail::getNameEn,
                       ChannelOrderDetail::getNameTw)
                .in(ChannelOrderDetail::getOrderId, orderIds));

        return allDetails.stream().collect(Collectors.groupingBy(ChannelOrderDetail::getOrderId));
    }

    public Set<String> batchCheckBigOrders(List<ChannelOrder> orders) {
        List<String> orderUniqueIds = orders.stream()
            .map(ChannelOrder::getOrderUniqueId)
            .collect(Collectors.toList());

        List<CmsBigOrder> bigOrders = cmsBigOrderMapper.selectList(
            Wrappers.lambdaQuery(CmsBigOrder.class)
                .select(CmsBigOrder::getOrderUniqueId)
                .in(CmsBigOrder::getOrderUniqueId, orderUniqueIds));

        return bigOrders.stream()
            .map(CmsBigOrder::getOrderUniqueId)
            .collect(Collectors.toSet());
    }
}
```

#### 7.2.2 缓存机制引入
```java
@Service
public class PackageInfoCacheService {

    @Cacheable(value = "packageInfo", key = "#packageIds.hashCode()", unless = "#result.isEmpty()")
    public Map<String, CmsOrderQueryPackageDTO> getPackageInfoBatch(Set<String> packageIds, String country) {
        Map<String, CmsOrderQueryPackageDTO> packageDTOMap = pmsFeignClient.getPackageList(
            CmsOrderQueryPackageVO.builder().packageIdHashSet(packageIds).build()
        ).getData();

        packageDTOMap.values().forEach(packageDTO -> packageDTO.setInfoByLanguage(country));
        return packageDTOMap;
    }

    @CacheEvict(value = "packageInfo", allEntries = true)
    public void clearPackageInfoCache() {
        // 清除缓存的方法
    }
}
```

### 7.3 异常处理优化

#### 7.3.1 统一异常处理
```java
@Component
public class OrderQueryExceptionHandler {

    public <T> T handleWithFallback(Supplier<T> supplier, T fallbackValue, String operation) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.warn("操作失败，使用降级值: operation={}, error={}", operation, e.getMessage());
            return fallbackValue;
        }
    }

    public void validateOrderQueryRequest(UserQueryOrderVO request) {
        Assert.notNull(request, "查询请求不能为空");
        Assert.hasText(request.getUserId(), "用户ID不能为空");
        Assert.hasText(request.getRole(), "用户角色不能为空");
        Assert.notNull(request.getOrderQueryCond(), "查询条件不能为空");

        if (request.getPageSize() <= 0 || request.getPageSize() > 100) {
            throw new BizException("页大小必须在1-100之间");
        }

        if (request.getPageNum() < 0) {
            throw new BizException("页码不能为负数");
        }
    }
}
```

## 8. 总结

### 8.1 核心技术亮点

#### 8.1.1 灵活的权限控制机制
1. **多角色支持**: 支持手机号、邮箱、渠道商等多种用户角色
2. **细粒度权限**: 渠道商用户支持层级权限控制
3. **安全验证**: 完善的参数验证和权限检查机制

#### 8.1.2 复杂的状态映射逻辑
1. **双向映射**: 查询条件状态到数据库状态，数据库状态到展示状态
2. **状态聚合**: 将多个数据库状态映射为统一的业务状态
3. **状态生命周期**: 完整的订单状态流转管理

#### 8.1.3 完善的数据组装机制
1. **批量查询优化**: 避免N+1查询问题
2. **多表关联**: 订单主表、详情表、套餐信息的关联查询
3. **数据转换**: 地址解析、多语言支持、货币处理

### 8.2 业务价值体现

#### 8.2.1 用户体验优化
1. **统一接口**: 为不同角色用户提供统一的订单查询接口
2. **灵活筛选**: 支持订单ID、状态等多维度筛选
3. **分页支持**: 大数据量场景下的性能保障
4. **多语言**: 支持中英文等多语言显示

#### 8.2.2 业务功能完整性
1. **订单全生命周期**: 从创建到完成/退订的完整状态管理
2. **订单类型支持**: 普通订单、加油包订单、大订单的差异化处理
3. **详细信息**: 包含地址、物流、价格等完整订单信息

### 8.3 架构设计优势

#### 8.3.1 可扩展性
1. **角色扩展**: 新增用户角色类型的扩展机制
2. **状态扩展**: 新增订单状态的映射机制
3. **字段扩展**: 订单信息字段的灵活扩展

#### 8.3.2 可维护性
1. **清晰的业务逻辑**: 状态映射、权限控制等逻辑清晰
2. **完善的异常处理**: 多层次的异常捕获和处理
3. **详细的日志记录**: 便于问题排查和监控

### 8.4 存在的问题和改进空间

#### 8.4.1 性能问题
1. **N+1查询**: 子订单详情的循环查询
2. **外部服务依赖**: PMS服务调用的性能瓶颈
3. **缓存缺失**: 套餐信息等热点数据缺乏缓存

#### 8.4.2 代码质量问题
1. **方法过长**: 主方法承担过多职责
2. **重复代码**: 状态映射逻辑存在重复
3. **硬编码**: 状态值、角色代码等存在硬编码

#### 8.4.3 安全性问题
1. **数据脱敏**: 敏感信息缺乏脱敏处理
2. **频率限制**: 缺乏查询频率限制机制
3. **权限细化**: 权限控制可以更加细化

### 8.5 改进建议总结

#### 8.5.1 短期改进（1-2周）
1. **方法拆分**: 将主方法拆分为多个职责单一的子方法
2. **批量查询**: 优化子订单详情的批量查询
3. **异常处理**: 完善异常处理和参数验证

#### 8.5.2 中期改进（1-2月）
1. **缓存机制**: 引入Redis缓存热点数据
2. **策略模式**: 使用策略模式重构状态映射逻辑
3. **监控告警**: 增加性能监控和异常告警

#### 8.5.3 长期改进（3-6月）
1. **微服务拆分**: 考虑将订单查询独立为微服务
2. **读写分离**: 查询操作使用只读数据库
3. **搜索引擎**: 引入Elasticsearch支持复杂查询

通过本次深入分析，我们全面了解了userQueryOrder方法的实现细节和技术特点。该方法虽然功能完整，但在性能优化、代码质量和安全性方面还有较大的改进空间。建议按照上述改进计划逐步优化，提升系统的整体质量和用户体验。
```
```
