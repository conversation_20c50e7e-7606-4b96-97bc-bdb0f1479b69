package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.through.ChannelNotifyActivationVO;
import com.ebupt.cmi.clientmanagement.feign.external.req.UnsubscribeNotifyReq;
import com.ebupt.cmi.clientmanagement.service.UnsubscribeNotifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 南向通知相关接口
 * <AUTHOR>
 * @date 2021-5-13 21:00:10
 */
@Slf4j
@Api(tags = "南向通知相关接口")
@AllArgsConstructor
@RestController
@RequestMapping("notification")
public class NotificationController {

    private final UnsubscribeNotifyService unsubscribeNotifyService;

    @ApiOperation("退订成功通知")
    @PostMapping("/unsubscribe")
    public Response unsubscribe(@RequestBody @Valid UnsubscribeNotifyReq unsubscribeNotifyReq) {
        unsubscribeNotifyService.notify(unsubscribeNotifyReq);
        return Response.ok();
    }

    @ApiOperation(value = "渠道商激活通知接口，内部服务调用本接口，透传别人，南向接口", notes = "渠道商激活通知接口")
    @PostMapping("/channelActivation")
    public Response channelNotifyActivationSouth(@RequestBody @Valid ChannelNotifyActivationVO channelNotifyVO) {

        unsubscribeNotifyService.channelNotifyActivation(channelNotifyVO);
        return Response.ok();

    }

}
