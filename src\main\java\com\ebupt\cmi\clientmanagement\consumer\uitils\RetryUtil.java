package com.ebupt.cmi.clientmanagement.consumer.uitils;

import com.ebupt.cmi.clientmanagement.consumer.upcc.UpccConsumer;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.RetryContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccSignContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailSendException;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@Data
@Slf4j
public abstract class RetryUtil {

    @Value("${flowPool.rabbit.resetTime}")
    protected Integer resetTime;

    @Value("${flowPool.rabbit.retryInterval}")
    protected Integer retryInterval;

    public abstract void doBiz(UpccSignContext context);

    public abstract void afterFail(RetryContext retryContext);

    public abstract void afterSuccess(RetryContext retryContext);
    public abstract void afterSuccess(UpccSignContext upccSignContext);

    public void execute(RetryContext retryContext) {

        for (UpccSignContext upccSignContext : retryContext.getUpccSignContext()) {

            log.debug("需要签约得内容，{}", upccSignContext);

            boolean success = false;

            for (int i = 1; i <= resetTime; i++) {

                log.debug("这是第{}次执行流程", i);
                try {
                    doBiz(upccSignContext);
                    afterSuccess(upccSignContext);
                    log.debug("流程执行成功，执行次数：{}", i);
                    success = true;
                    break;
                } catch (Exception e) {
                    log.warn("流程执行失败，需要重试开始睡眠：{}ms，睡眠后进行重试", retryInterval);
                    try {
                        Thread.sleep(retryInterval);
                    } catch (InterruptedException ex) {
                        throw new RuntimeException(ex);
                    }
                }
            }

            if (success) {
                continue;
            }

            log.warn("流程执行失败，重试{}次后仍未成功，请排查原因", resetTime);

            afterFail(retryContext);

            return;

        }

        afterSuccess(retryContext);

    }

    public void submit(ExecutorService executorService) {
        CompletableFuture.runAsync(() -> {
            try {
                execute(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, executorService);
    }
}
