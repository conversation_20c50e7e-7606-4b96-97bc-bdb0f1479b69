package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * cms_channel_selfpackage_country_relation
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelSelfpackageCountryRelation implements Serializable {
    private Long id;

    private String corpId;

    private Long groupId;

    @TableField(exist = false)
    private String groupName;

    private Date createTime;

    private static final long serialVersionUID = 1L;
}