package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/01/17 10:00
 */

@Data
@Builder
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class FlowpoolListDTO {
    /**
     * 流量池id
     */
    private String flowPoolId;

    /**
     * 流量池名称
     */
    private String flowPoolName;

    /**
     * 使用状态
     */
    private String useStatus;

    /**
     * 上架状态
     */
    private String shelfStatus;

    /**
     * 总流量
     */
    private String flowPoolTotal;

    /**
     * 已用流量
     */
    private String usedFlowPool;

    /**
     * 卡号数量
     */
    private Integer cardCount;

    /**
     * 支持的国家
     */
    private List<String> supportMcc;

    /**
     * 控制逻辑（1：达量停用 2：达量限速 3：达量继续使用）
     */
    private String controlLogic;

    /**
     * 流量池价格
     */
    private String flowPoolPrice;

    /**
     * 超量后单价
     */
    private String flowPoolExtraPrice;

    /**
     * 到期是否重置
     * 暂时默认1 后续业务保留字段
     */
    private String isReset = "1";
    /**
     * ICCID列表（入参需要则返回）
     */
    private List<String> iccidList;
}
