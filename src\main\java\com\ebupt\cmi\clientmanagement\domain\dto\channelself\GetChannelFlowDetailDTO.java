package com.ebupt.cmi.clientmanagement.domain.dto.channelself;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class GetChannelFlowDetailDTO {

    @NotBlank
    private String corpId;

    @NotNull
    private Integer num;

    @NotNull
    private Integer size;
}
