package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 卡片类型
 * @<PERSON> huang lingsong
 * @Date 2021/4/21 16:54
 */
@Getter
@AllArgsConstructor
public enum CardFormEnum {

    /**
     * 普通卡
     */
    ORDINARY(1,"普通卡"),

    /**
     * Esim卡
     */
    ESIM( 2,"ESIM卡"),

    /**
     * 贴片卡
     */
    PADPASTING(3,"贴片卡");

    private Integer type;

    private String typeName;

    public static String getTypeName(String type){
        if(ORDINARY.getType().equals(Integer.valueOf(type))){
            return ORDINARY.typeName;
        }else if(ESIM.getType().equals(Integer.valueOf(type))){
            return ESIM.typeName;
        }else {
            return PADPASTING.typeName;
        }
    }

    public boolean matches(String value) {
        return type.toString().equals(value);
    }

}
