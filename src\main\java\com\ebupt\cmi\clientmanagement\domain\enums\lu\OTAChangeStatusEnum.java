package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description OTA首次切换状态枚举
 * @date 2021/4/22 11:00
 */
@AllArgsConstructor
@Getter
public enum OTAChangeStatusEnum {

    /**
     * 写卡失败
     */
    WRITE_CARD_ERROR("1"),
    /**
     * 写卡成功未上报
     */
    WRITE_CARD_OK_NOT_REPORT("2"),
    /**
     * 写卡成功已切换
     */
    WRITE_CARD_OK_CHANGED("3"),
    /**
     * 写卡中
     */
    WRITING("4")
    ;

    private String status;

}
