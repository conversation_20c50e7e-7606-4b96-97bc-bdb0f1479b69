package com.ebupt.cmi.clientmanagement.annotion;



import com.ebupt.cmi.clientmanagement.domain.enums.DisasterEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * <AUTHOR>
 * @date 2021-7-26 15:51:10
 */
@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SdcMultipleQueues {

    /**
     * 操作类型
     * @return
     */
    DisasterEnum operationName();


}
