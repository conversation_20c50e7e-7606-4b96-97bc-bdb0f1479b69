package com.ebupt.cmi.clientmanagement.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelDetailDTO {

    private String corpId;

    /**
     * 是否允许订购
     * 1：允许
     * 2：不允许
     */
    private String isSub;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal deposit;

    @TableField("currency_code")
    private String currencyCode;

    /**
     * 折扣，0-100
     */
    private Integer discount;


    private Integer directRatio;

}
