package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@Getter
@Slf4j
public enum ChargingModeEnum {
    /**
     * 1、定制卡
     */
    CUSTOMIZED("1","定制卡","Customized SIM"),

    /**
     * 2、普通卡
     */
    NORMAL("2","普通卡","CMLink SIM");


    String type;
    String name;
    String nameEn;
    public static String getName(String type) {
        for (ChargingModeEnum p : ChargingModeEnum.values()) {
            if (p.getType().equals(type)) {
                return p.getName();
            }
        }
        log.warn("输入不符合要求：{}", type);
        return "";
    }

    public static String getNameEn(String type) {
        for (ChargingModeEnum p : ChargingModeEnum.values()) {
            if (p.getType().equals(type)) {
                return p.getNameEn();
            }
        }
        log.warn("输入不符合要求：{}", type);
        return "";
    }
}
