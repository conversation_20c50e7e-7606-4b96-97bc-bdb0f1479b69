package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_package_card_bak")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelPackageCardBak extends BaseEntity {


    private Long id;

    private String imsi;

    private String msisdn;

    private String iccid;

    /**
     * 套餐类型：
     * 1：套餐
     * 2：终端线下卡池
     * 3：流量池
     * 4：加油包
     */
    private String packageType;

    private String packageId;

    private String packageName;
    private String nameTw;
    private String nameEn;

    private String corpId;

    private String periodUnit;

    private Integer keepPeriod;

    /**
     * 套餐订购有效期，套餐有效期（天） + 当前时间
     */
    private Date effectiveDay;

    /**
     * 高速签约模板
     */
    private String signBizId;

    /**
     * 低速签约模板
     */
    private String limitSpeedSignBizId;

    /**
     * 限速签约模板
     */
    private String slowSpeedSignBizId;

    /**
     * 激活方式
     * 1：自动
     * 2：手动
     */
    private String activeType;

    /**
     * 首次激活类型
     * 1：H
     * 2：V
     */
    private String activeCategory;

    /**
     * 首次激活时间
     */
    private Date activeTime;

    /**
     * 过期时间，激活时：持续周期 * 周期类型 + 当前时间
     */
    private Date expireTime;

    /**
     * 首次激活国家
     */
    private String mcc;

    /**
     * 首次激活号码-imsi
     */
    private String firstActiveNumber;

    private String packageStatus;

    /**
     * 首次切换状态 1：VIMSI激活写卡中，2：VIMSI激活未切换，3：VIMSI激活已切换，4：HIMSI激活
     */
    private String changeStatus;

    private String orderUniqueId;

    private String packageUniqueId;

    /**
     * 是否是绑定套餐
     * 1：是
     * 2：否
     */
    private String isBindPackage;

    /**
     * Package_type = 2 时必填,同时package_id为当前卡池关联的套餐id
     */
    private String poolId;

    /**
     * 归属的流量池唯一id，当Package_type = 3 时填写
     */
    private String belongPackageId;

    /**
     * 流量限制类型
     * 1：周期内限量
     * 2：单日限量
     */
    private String flowLimitType;

    /**
     * 达量控制逻辑
     * 1：达量限速
     * 2：达量释放
     */
    private String controlLogic;

    /**
     * 流量上限，单位byte
     */
    private BigDecimal flowLimitSum;

    /**
     * 上网状态
     * 1：正常
     * 2：限速
     */
    private String surfStatus;

    /**
     * 套餐计费激活方式
     * 1: 预订日期或首话单开始计算套餐
     * 2: 收到IMSI的LU后开始计算套餐
     * 3: 用户IMSI(HIMSI+VIMSI)的流量使用超过指定限额后
     */
    private String activationMode;

    /**
     * 是否支持加油包
     * 1：是
     * 2：否
     */
    private String supportRefuel;

    /**
     * activation_mode=3时不为空
     * 计费激活流量限额，单位byte
     */
    private BigDecimal billFlowLimit;

    private Date activeAt;

    @TableField(exist = false)
    private boolean updateOverdueTime = true;

    private Date packagePurchaseTime;

    private String packageUseStatus;

    private String cooperationMode;



    /**
     * 流量限制类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum FlowLimitTypeEnum {

        /**
         * 1：周期内限量
         */
        CYCLE_LIMIT("1"),

        /**
         * 2：单日限量
         */
        DAY_LIMIT("2");

        @Getter
        private String value;

    }

    /**
     * 达量控制逻辑
     * 1：达量限速
     * 2：达量释放
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum ControlLogicEnum {

        /**
         * 1：达量限速
         */
        SPEED_LIMIT("1"),

        /**
         * 2：达量释放
         */
        REACH_RELEASE("2");

        @Getter
        private String value;

    }

    /**
     * 上网状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum SurfStatusEnum {

        /**
         * 1：正常
         */
        NORMAL("1"),

        /**
         * 2：限速
         */
        LIMIT("2");

        @Getter
        private String value;

    }

    /**
     * 套餐计费激活方式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum ActivationModeEnum {

        /**
         * 1：预订日期或首话单开始计算套餐
         */
        DATE_OR_BILL("1"),

        /**
         * 2：收到IMSI的LU后开始计算套餐
         */
        AFTER_LU("2"),

        /**
         * 3: 用户IMSI(HIMSI+VIMSI)的流量使用超过指定限额后
         */
        AFTER_USING_EXCESS("3");

        @Getter
        private String value;

    }


    /**
     * 套餐激活状态
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum PackageStatusEnum {

        /**
         * 1：待激活
         */
        TO_BE_ACTIVATED("1"),

        /**
         * 2：已激活
         */
        ACTIVATED("2"),

        /**
         * 3：已使用
         */
        USED("3"),

        /**
         * 4：已激活待计费（暂不用）
         */
        ACTIVATED_TO_BE_BILLED("3"),

        /**
         * 5：已过期
         */
        EXPIRED("5"),

        /**
         * 6：激活中
         */
        ACTIVE("6");

        @Getter
        private String value;

    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum ChangeStatusEnum {

        /**
         * VIMSI激活写卡中
         */
        VCARD_ACTIVATED_WRTTING("1"),

        /**
         * VIMSI激活未切换
         */
        VCARD_ACTIVATED_UNSWITCH("2"),

        /**
         * VIMSI激活已切换
         */
        VCARD_ACTIVATED_WSITCHED("3"),

        /**
         * HIMSI激活
         */
        HCARD_ACTIVATED("4");

        @Getter
        private String value;

    }

    /**
     * 套餐类型：
     * 1：套餐
     * 2：终端线下卡池
     * 3：流量池
     * 4：加油包
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum PackageTypeEnum {

        /**
         * 1.套餐
         */
        PACKAGE("1"),

        /**
         * 2.终端线下卡池
         */
        TERMINAL_OFF_CARD_POOL("2"),

        /**
         * 3.流量池
         */
        FLOW_POOL("3"),

        /**
         * 4.加油包
         */
        REFUEL("4");

        @Getter
        private String value;

    }

}
