package com.ebupt.cmi.clientmanagement.controller.channel;

import com.ebupt.cmi.clientmanagement.domain.dto.channel.SubCnlDTO;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.SubChannelVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.SubCnlSearchVO;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/subChannel")
@AllArgsConstructor
@Api(tags = "子渠道商相关接口")
public class SubChannelController {

    private final ChannelService channelService;

    @ApiOperation(value = "子渠道商新增", notes = "新增子渠道商")
    @PostMapping(value = "/add", headers = "content-type=multipart/form-data")
    @OperationLog(operationName = "客户管理-子渠道商管理-新增", operationType = OperationTypeEnum.ADD)
    public Response<Void> addOne(@Validated SubChannelVO vo) {
        channelService.addSubOne(vo);
        return Response.ok();
    }

    @ApiOperation(value = "子渠道商删除")
    @DeleteMapping("/del/{corpId}")
    @OperationLog(operationName = "客户管理-子渠道商管理-删除", operationType = OperationTypeEnum.DELETE)
    public Response<Void> deleteOne(@PathVariable String corpId) {
        channelService.delSubOne(corpId);
        return Response.ok();
    }


    @ApiOperation(value = "子渠道商编辑")
    @PostMapping("/edit")
    @OperationLog(operationName = "客户管理-子渠道商管理-编辑", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> editOne(@Validated SubChannelVO vo) {
        channelService.editSubOne(vo);
        return Response.ok();
    }

    @PostMapping("/queryPage")
    @ApiOperation(value = "分页查询子渠道商信息")
    public Response queryPage(@RequestBody @Validated SubCnlSearchVO searchVO) {
        return channelService.queryPage(searchVO);
    }
}
