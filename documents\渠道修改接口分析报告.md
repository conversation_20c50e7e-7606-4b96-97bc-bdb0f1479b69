# 渠道修改接口分析报告

## 1. 接口功能描述与业务目标

**接口路径**：`PUT /channel/updateChannel`

**控制器方法**：`ChannelController.updateChannel`

**功能描述**：
用于对已存在的渠道商（Channel）信息进行修改，包括基础信息、合作模式、套餐、规则、模板、运营商等多维度的业务数据。该接口支持对渠道商的多表数据进行一致性更新，并具备完善的参数校验、业务约束和异常处理能力。

**业务目标**：
- 支持渠道商信息的全量或部分更新
- 保证渠道商相关联的多表数据一致性
- 严格校验业务规则，防止非法或冲突数据写入
- 支持部分字段变更需走审核流程，保障数据安全

---

## 2. 详细调用流程

```mermaid
flowchart TD
    A[前端/调用方]
    B[ChannelController.updateChannel]
    C[UpdateChannelVO（继承NewChannelVO）]
    D[ChannelService.updateChannel]
    E[ChannelServiceImpl.updateChannel]
    F[ChannelDistributorDetailMapper.selectOne]
    G[allowUpdateCheck 校验]
    H[多表操作: 关联表更新/删除/插入]
    I{是否需审核}
    J[writeAuthTable 写入审核表]
    K[直接更新主表]
    L[合同到期/变更: 解冻账户/更新状态]
    M[异常: BizException/事务回滚]
    N[返回 Response.ok/error]

    A -->|PUT /channel/updateChannel| B
    B -->|参数校验| C
    B -->|调用| D
    D -->|实现| E
    E -->|查库| F
    E -->|校验| G
    E -->|多表操作| H
    E -->|判断| I
    I -- 是 --> J
    I -- 否 --> K
    E -->|合同到期/变更| L
    E -->|异常| M
    E -->|结束| N
```

### 步骤说明
1. **前端/调用方** 通过 PUT 请求提交渠道修改数据。
2. **ChannelController.updateChannel** 接收请求，参数为 `UpdateChannelVO`，自动进行基础校验（如@NotBlank等注解）。
3. **参数结构**：`UpdateChannelVO` 继承自 `NewChannelVO`，包含渠道商所有可维护字段。
4. **调用 Service 层**：controller 调用 `channelService.updateChannel(updateChannelVO)`。
5. **ServiceImpl 业务实现**：
   - 查询渠道商详情（`ChannelDistributorDetailMapper.selectOne`）。
   - 调用 `allowUpdateCheck` 进行业务校验（如名称/EBSCode唯一性、合作模式变更限制、自建套餐组约束等）。
   - 校验通过后，依次对渠道商相关的套餐、规则、模板、运营商等多张表进行更新、删除、插入操作。
   - 判断本次变更是否涉及需审核字段，若是则写入审核表（`writeAuthTable`），否则直接更新主表。
   - 若涉及合同到期/变更，调用解冻账户等后续处理。
   - 全过程采用事务控制，出现异常自动回滚。
6. **异常处理**：如遇业务冲突、唯一性约束、外键依赖等，抛出 `BizException`，controller 捕获后返回错误信息。
7. **返回结果**：成功返回 `Response.ok()`，失败返回 `Response.error(错误信息)`。

---

## 3. 涉及的核心类和方法清单

### Controller 层
- `ChannelController.updateChannel(UpdateChannelVO updateChannelVO)`

### VO/DTO
- `UpdateChannelVO`（继承自 `NewChannelVO`，包含所有渠道商可维护字段及部分必填校验）
- `NewChannelVO`（渠道商新增/修改的通用字段定义）

### Service 层
- `ChannelService.updateChannel(UpdateChannelVO updateChannelVO)`
- `ChannelServiceImpl.updateChannel(UpdateChannelVO updateChannelVO)`
- `allowUpdateCheck(UpdateChannelVO vo, ChannelDistributorDetail detail, boolean isNewChannel)`
- `writeAuthTable(UpdateChannelVO vo, boolean isNewChannel)`
- `buildChannel(UpdateChannelVO vo)`、`buildChannelDistributorDetail(UpdateChannelVO vo)`

### Mapper/DAO
- `ChannelDistributorDetailMapper`
- `ChannelMapper`
- `ChannelA2zOperatorMapper`
- `CmsChannelImsiAmountRelationMapper`
- `CmsChannelA2zruleRelationMapper`
- `CmsChannelResourceruleRelationMapper`
- `CmsChannelSmsTemplateRelationMapper`
- `CmsChannelSelfpackageCountryRelationAuthMapper`
- 其他相关多表 Mapper

### 关联实体
- `Channel`
- `ChannelDistributorDetail`
- `CmsChannelImsiAmountRelation`
- `CmsChannelA2zruleRelation`
- `CmsChannelResourceruleRelation`
- `CmsChannelSmsTemplateRelation`
- `CmsChannelSelfpackageCountryRelationAuth`
- `CmsChannelAuth`（审核表）
- `CmsChannelDistributorsAuth`（审核表）

---

## 4. 数据流转过程

- **参数接收**：前端传入的 JSON 数据被反序列化为 `UpdateChannelVO`，自动校验必填项。
- **主表查询**：根据 `corpId` 查询渠道商主表和详情表，获取当前数据。
- **业务校验**：如名称/EBSCode唯一性、合作模式变更、自建套餐组、套餐模板等多项业务规则校验。
- **多表操作**：
  - 关联套餐、规则、模板、运营商等表的增删改
  - 变更需审核时写入审核表，否则直接更新主表
- **合同到期/变更处理**：如涉及合同到期，自动解冻账户、更新相关状态
- **事务控制**：全流程采用 Spring 事务，任一环节异常均回滚
- **缓存处理**：部分变更涉及 redis 标志位的增删

---

## 5. 异常处理机制

- **参数校验异常**：如必填项缺失、格式错误，直接抛出异常，controller 捕获后返回错误信息
- **业务校验异常**：如名称/EBSCode重复、合作模式非法变更、自建套餐组删除等，抛出 `BizException`，controller 捕获后返回错误信息
- **数据库唯一性冲突**：如主键/唯一索引冲突，抛出 `DuplicateKeyException`，转为业务异常返回
- **外部依赖校验**：如套餐/规则/模板被其他业务引用，不允许删除，抛出业务异常
- **事务回滚**：所有异常均触发事务回滚，保证数据一致性
- **最终清理**：无论成功或失败，均清理 ThreadLocal 线程变量，防止内存泄漏

---

## 6. 总结

该接口实现了渠道商信息的复杂多表一致性修改，具备严格的业务规则校验、灵活的审核机制和完善的异常处理能力，能够有效保障渠道商数据的准确性和安全性。 