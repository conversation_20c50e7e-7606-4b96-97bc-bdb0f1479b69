package com.ebupt.cmi.clientmanagement.config;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.ebupt.cmi.clientmanagement.utils.Sequence;

/**
 * @Desc
 * <AUTHOR> lingsong
 * @Date 2024/3/28 16:32
 */
public class CustonIdentifierGenerator implements IdentifierGenerator {

    private final Sequence sequence;

    public CustonIdentifierGenerator(long datacenterId) {
        this.sequence = new Sequence(datacenterId);
    }

    public CustonIdentifierGenerator(long workerId, long dataCenterId) {
        this.sequence = new Sequence(workerId, dataCenterId);
    }

    @Override
    public Number nextId(Object entity) {
        return this.sequence.nextId();
    }

}
