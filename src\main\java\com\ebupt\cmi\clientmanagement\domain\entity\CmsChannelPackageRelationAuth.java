package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * cms_channel_package_relation_auth
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelPackageRelationAuth implements Serializable {
    private Long id;

    /**
     * 厂商id
     */
    private String corpId;

    /**
     * 套餐组id
     */
    private String groupId;

    /**
     * 套餐组名称
     */
    private String groupName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 套餐组类型
1：代销
2：A2Z

     */
    private String cooperationMode;

    /**
     * 是否是渠道商自建套餐组
1：是 
2：否 
默认为2

     */
    private String isChannelCreate;

    private static final long serialVersionUID = 1L;
}