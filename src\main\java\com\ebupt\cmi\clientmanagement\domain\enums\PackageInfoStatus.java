package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.checkerframework.checker.units.qual.A;

/**
 * 套餐信息状态枚举
 * <AUTHOR>
 * @date 2021-5-25 10:52:33
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PackageInfoStatus {

    /**
     * 待上架
     */
    ON_SHELF("1"),

    /**
     * 正常
     */
    NORMAL("2"),

    /**
     * 下架
     */
    OFF_SHELF("3"),

    /**
     * 删除
     */
    DELETED("4");

    private String value;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

}
