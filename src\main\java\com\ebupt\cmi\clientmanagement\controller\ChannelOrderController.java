package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.net.URLDecoder;
import com.ebupt.cmi.clientmanagement.domain.dto.PersonalOrderDTO;
import com.ebupt.cmi.clientmanagement.domain.req.AuditOrderReq;
import com.ebupt.cmi.clientmanagement.domain.req.SearchOrderDetailReq;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.validation.CommonGroup;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.ChannelOrderDetailVO;
import com.ebupt.cmi.clientmanagement.domain.vo.personalorder.PersonalOrderQueryVO;
import com.ebupt.cmi.clientmanagement.service.IPersonalOrderService;
import com.ebupt.cmi.clientmanagement.service.PersonalOrderService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/channelOrder")
@Api(tags = "渠道商订单管理相关接口")
public class ChannelOrderController {

    private final PersonalOrderService personalOrderService;

    private final IPersonalOrderService orderService;

    @ApiOperation(value = "渠道商订单分页查询接口")
    @GetMapping("/orderPages")
    public Response<PageResult<PersonalOrderDTO>> orderPages(@Validated(value = CommonGroup.class) PersonalOrderQueryVO orderQueryVO) {
        return Response.ok(orderService.orderPages(orderQueryVO));
    }

    @ApiOperation(value = "渠道商订单订单查询导出")
    @GetMapping("/orderPages/export")
    @OperationLog(operationName = "产品运营-渠道商订单管理-导出订单详情", operationType = OperationTypeEnum.EXPORT)
    public void orderPagesDownload(@Validated(value = CommonGroup.class) PersonalOrderQueryVO orderQueryVO, HttpServletResponse response)
            throws IOException {
        orderService.orderPagesDownload(orderQueryVO, response);
    }

    @ApiOperation("根据订单id查询子订单列表")
    @GetMapping("/orderDetailsByOrderId")
    public Response<PageResult<ChannelOrderDetailVO>> getOrderDetailPage(
            @Validated(CommonGroup.class) SearchOrderDetailReq searchOrderDetailReq, @RequestHeader("userName") String name) {
        name = URLDecoder.decode(name, StandardCharsets.UTF_8);
        searchOrderDetailReq.setUsername(name);
        return Response.ok(personalOrderService.getOrderDetailPage(searchOrderDetailReq));
    }

    @ApiOperation("全部退订")
    @PostMapping("/unsubscribe/{id}")
    @OperationLog(operationName = "渠道商订单管理——全部退订",operationType = OperationTypeEnum.DELETE)
    public Response<Void> unsubscribeOrder(@PathVariable String id) {
        personalOrderService.unsubscribeOrder(Long.valueOf(id));
        return Response.ok();
    }

    @ApiOperation("子订单退订")
    @PostMapping("/detail/unsubscribe/{id}")
    @OperationLog(operationName = "渠道商订单管理——子订单退订",operationType = OperationTypeEnum.DELETE)
    public Response<Void> unsubscribeOrderDetail(@PathVariable String id) {
        personalOrderService.unsubscribeOrderDetail(Long.valueOf(id));
        return Response.ok();
    }

    @ApiOperation("审核总订单")
    @PostMapping("/audit")
    @OperationLog(operationName = "渠道商订单管理——总订单审核", operationType = OperationTypeEnum.AUDIT)
    public Response<Void> auditOrder(@RequestBody @Valid AuditOrderReq auditOrderReq) {
        personalOrderService.auditOrder(auditOrderReq);
        return Response.ok();
    }

    @ApiOperation("审核子订单")
    @PostMapping("/detail/audit")
    @OperationLog(operationName = "渠道商订单管理——子订单审核", operationType = OperationTypeEnum.AUDIT)
    public Response<Void> auditOrderDetail(@RequestBody @Valid AuditOrderReq auditOrderReq) {
        personalOrderService.auditOrderDetail(auditOrderReq);
        return Response.ok();
    }

}
