package com.ebupt.cmi.clientmanagement.domain.dto;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/23 16:52
 */

@Data
@Accessors(chain = true)
public class ChannelFlowDetailDTO {
    @Alias("EBSCode")
    private String ebsCode;

    @Alias("Date")
    private String date;

    @Alias("ICCID")
    private String iccid;

    @Alias("MCC")
    private String mcc;

    @Alias("Country or region")
    private String countryOrRegion;

    @Alias("Usage(MB)")
    private String usage;

    private BigDecimal usedTraffic;

    @Alias("Currency")
    private String currency;

    @Alias("unit price(GB)")
    private String unitPrice;

    @Alias("Amount")
    private String amount;

    @Alias("Operator")
    private String operator;

    private String dateBelongTo;

    private String type;

    private String operatorName;

    private String imsi;

    private String countryEn;

    private String plmnlist;

    private String himsi;

    private Long id;

    /**
     * 钱钱入库单位得是分
     */
    public void reSet() {
        this.unitPrice = new BigDecimal(unitPrice).multiply(new BigDecimal("100")).toString();
        this.amount = new BigDecimal(amount).multiply(new BigDecimal("100")).toString();
    }

    public boolean haveEmpty() {
        return isEmpty(this.ebsCode) ||
                isEmpty(this.date) ||
                isEmpty(this.iccid) ||
                isEmpty(this.mcc) ||
                isEmpty(this.countryOrRegion) ||
                isEmpty(this.usage) ||
                isEmpty(this.currency) ||
                isEmpty(this.unitPrice) ||
                isEmpty(this.amount);
    }

    private boolean isEmpty(String col) {
        return col == null || "".equals(col);
    }
}
