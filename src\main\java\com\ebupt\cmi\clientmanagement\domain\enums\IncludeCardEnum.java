package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/7/9 17:15
 */

@AllArgsConstructor
@Getter
public enum IncludeCardEnum {
    /**
     * 非硬卡
     */
    NOT_HARD_CARD(0, "非硬卡"),
    /**
     * 硬卡
     */
    HARD_CARD(1, "硬卡"),
    /**
     * ESIM
     */
    ESIM(2, "ESIM");

    private Integer k;

    private String v;

    public static String getVal(Integer k) {
        for (IncludeCardEnum value : IncludeCardEnum.values()) {
            if (value.k.equals(k)){
                return value.v;
            }
        }
        return "未知类型";
    }
}
