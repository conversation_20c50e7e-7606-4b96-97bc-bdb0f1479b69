package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;


/**
 * <AUTHOR>
 * @description 终端厂商详情DTO
 * @date 2021/5/17 16:25
 */
@Data
@NoArgsConstructor
@ToString
@ApiModel
public class TerminalDTO  {

    @ApiModelProperty(value = "终端厂商id")
    private String id;

    @ApiModelProperty(value = "厂商名称")
    private String corpName;

    @ApiModelProperty(value = "厂商AppKey")
    private String appKey;

    @ApiModelProperty(value = "厂商AppSecret")
    private String appSecret;

    @ApiModelProperty(value = "EBS Code")
    private String ebsCode;

    @ApiModelProperty(value = "通知类型")
    private String notifyType;

    @ApiModelProperty(value = "通知URL")
    private String notifyUrl;

    @ApiModelProperty(value = "终端厂商类型[7线上 8线下]")
    private String type;

    @ApiModelProperty(value = "线下厂商付费模式，厂商为线下厂商时，该字段不能为空 1套餐 2流量方向")
    private String settleType;

    @ApiModelProperty(value = "是否激活通知 1是 2否")
    private String activeNotifyType;

    @ApiModelProperty(value = "是否到期通知 1是 2否")
    private String dueNotifyType;

    @ApiModelProperty(value = "能力信息录入方式，默认系统自动生成。1自动 2手动")
    private String eopCreateType;

    @ApiModelProperty(value = "套餐/流量信息")
    private List<TerminalPackageDTO> extras;
}
