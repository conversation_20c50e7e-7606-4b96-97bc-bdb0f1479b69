package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 流量池信息
 */
@Data
@TableName("cms_flowpool_info")
public class FlowPoolInfo {

    @TableId
    private String flowPoolId;
    private String flowPoolName;

    /**
     * 流量池总量
     */
    private Long flowPoolTotal;
    /**
     * 周期类型：
     * 1：24小时
     * 2：自然日
     * 3：自然月
     * 4：自然年
     */
    private String cycleType;
    /**
     * 周期数
     */
    private Integer cycleNum;
    /**
     * 控制逻辑
     * 1-达量限速
     * 2-达量停用
     */
    private String controlLogic;
    private Date createTime;
    private Date updateTime;;

}
