package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/28 18:58
 */

@AllArgsConstructor
@Getter
public enum RoleEnum {
    /**
     * 102: ICCID
     */
    ICCID("102"),
    /**
     * 104：手机号
     */
    PHONENUMBER("104"),
    /**
     * 105：临时账户
     */
    TEMP_ACOUNT("105"),
    /**
     * 106：渠道商
     */
    CHANNEL("106");

    String role;
}
