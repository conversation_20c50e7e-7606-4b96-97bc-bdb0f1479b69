package com.ebupt.cmi.clientmanagement.controller.realname;

import cn.hutool.core.net.URLDecoder;
import com.alibaba.nacos.client.utils.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebupt.cmi.clientmanagement.domain.entity.realname.ChannelRealNameInfo;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.realname.ChannelRealNameVO;
import com.ebupt.cmi.clientmanagement.feign.sms.domain.Result;
import com.ebupt.cmi.clientmanagement.service.realname.NameAuthService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NameAuthController.java
 * @Description NameAuthController
 * @createTime 2021年12月01日 10:26:00
 */

@Slf4j
@Api(tags = "人工认证分页查询")
@RestController
@RequestMapping("/nameAuth")
public class NameAuthController {

    @Autowired
    NameAuthService nameAuthService;

    @ApiOperation(value = "1、人工认证分页")
    @GetMapping("/human")
    @NormalLog
    public Response humanAuth(@Validated ChannelRealNameVO channelRealNameVO) {
        return Response.ok(nameAuthService.getPages(channelRealNameVO));
    }

    @ApiOperation(value = "2、卡认证信息查询分页")
    @GetMapping("/cardInfo")
    @NormalLog
    public Response cardInfo(@Validated ChannelRealNameVO channelRealNameVO,
                             @RequestHeader("userName") String name) {
        boolean cantGoThrough = StringUtils.isBlank(channelRealNameVO.getIccid()) && StringUtils.isBlank(channelRealNameVO.getMsisdn())
                && StringUtils.isBlank(channelRealNameVO.getCertificatesId())
                && StringUtils.isBlank(channelRealNameVO.getImsi()) && StringUtils.isBlank(channelRealNameVO.getAuthStatus());
        if (cantGoThrough) {
            return Response.error("ICCID、MSISDN、IMSI、认证状态、证件id不能同时为空");
        }
        name = URLDecoder.decode(name, StandardCharsets.UTF_8);
        channelRealNameVO.setName(name);
        return Response.ok(nameAuthService.getCardInfoPages(channelRealNameVO));
    }

    @ApiOperation(value = "3、客服支撑认证信息查询")
    @GetMapping("/customerService")
    @NormalLog
    public Response customerService(@Validated ChannelRealNameVO channelRealNameVO) {
        return Response.ok(nameAuthService.getPagesForCustomerServiceSupport(channelRealNameVO));
    }

    @PostMapping(value = "/human")
    public Response<Page<ChannelRealNameInfo>> humanAuthForPms(@Validated @RequestBody ChannelRealNameVO channelRealNameVO) {
        Response response = new Response();
        response.setCode(ResponseResult.SUCCESS.getCode());
        response.setMsg(ResponseResult.SUCCESS.getMsg());
        response.setData(nameAuthService.getPages(channelRealNameVO));
        return response;
    }

    @PostMapping("/customerService")
    public Response customerServiceForPMS(@RequestBody ChannelRealNameVO channelRealNameVO) {
        Response response = new Response();
        response.setCode(ResponseResult.SUCCESS.getCode());
        response.setMsg(ResponseResult.SUCCESS.getMsg());
        response.setData(nameAuthService.getPagesForCustomerServiceSupport(channelRealNameVO));
        return response;
    }

    @ApiOperation(value = "人工认证删除")
    @DeleteMapping("/deleteHuman")
    @NormalLog
    @OperationLog(operationName = "人工认证删除", operationType = OperationTypeEnum.DELETE)
    public Response deleteHumanAuth(String authID) {
            nameAuthService.deleteHuman(authID);
        return Response.ok();
    }

    @ApiOperation(value = "实名制认证信息取消")
    @OperationLog(operationName = "实名制认证信息取消", operationType = OperationTypeEnum.UPDATE)
    @PostMapping("/cancelAuthentication")
    public Response cancelAuthentication(@RequestParam String authID) {
        nameAuthService.cancelAuthentication(authID);
        return Response.ok();
    }
}
