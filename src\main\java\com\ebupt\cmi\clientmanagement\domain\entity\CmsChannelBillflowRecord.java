package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsChannelBillflowRecord对象", description="")
@Builder
public class CmsChannelBillflowRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "渠道商ID")
    private String corpId;

    /**
     * 类型：1.缴付账单2.增加押金 3.增加预存款4.酬金返还 5. 套餐订购6.加油包订购 7. 套餐退订 8.加油包退订 9.渠道商收入调账 10.营销返利
     */
    @ApiModelProperty(value = "类型：1. 增加押金2. 增加预存款3. 酬金返还4. 缴付账单5. 套餐订购（含加油包）6. 套餐退订（含加油包）, 7.渠道商收入金额调账")
    private String type;

    @ApiModelProperty(value = "币种编码：156 人民币 840 美元 344 港币")
    private String currencyCode;

    @ApiModelProperty(value = "交易金额，单位：分")
    private BigDecimal amount;

    @ApiModelProperty(value = "账户余额（可用额度）单位：分")
    private BigDecimal deposit;

    private String cooperationMode;

    /**
     * 总/子订单id
     */
    private String orderId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


}
