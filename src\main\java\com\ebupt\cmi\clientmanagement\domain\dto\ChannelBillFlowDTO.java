package com.ebupt.cmi.clientmanagement.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ICCID查询返回参数")
public class ChannelBillFlowDTO {

    @JsonIgnore
    @ExcelIgnore
    private Long id;

    @JsonIgnore
    @ExcelIgnore
    private String corpId;

    @ExcelProperty(value = "Type" ,index = 1)
    private String type;

    @ExcelProperty(value = "Currency" ,index = 2)
    private String currencyCode;

    @ExcelProperty(value = "Transaction amount" ,index = 3)
    private String amount;

    @ExcelProperty(value = "Account balance" ,index = 4)
    private String deposit;

    @ExcelProperty(value = "Period" ,index = 0)
    private String createTime;
}
