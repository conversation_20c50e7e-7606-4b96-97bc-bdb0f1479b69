package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageBatchsubRecord.java
 * @Description cms_package_batchsub_record
 * @createTime 2021年07月19日 16:57:00
 */
@Builder
@TableName("cms_package_batchsub_record")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PackageBatchsubRecord extends BaseEntity {
    /**
     *购买时间
     */
    LocalDateTime orderDate;
    /**
     *渠道ID
     */
    String corpId;
    Long id;
    /**
     *原始文件名称
     */
    String fileName;
    /**
     *原始文件路径
     */
    String sourceFilePath;
    /**
     *失败详情文件路径
     */
    String failFilePath;
    /**
     *成功详情文件路径
     */
    String successFilePath;

    /**
     * 任务总数量
     */
    Integer sourceFileCount;

    /**
     * 处理失败数量
     */
    Integer failFileCount;

    /**
     * 处理成功数量
     */
    Integer successFileCount;

    /**
     * 任务状态：
     * 1：处理中
     * 2：完成
     */
    String status;

    String cooperationMode;
}
