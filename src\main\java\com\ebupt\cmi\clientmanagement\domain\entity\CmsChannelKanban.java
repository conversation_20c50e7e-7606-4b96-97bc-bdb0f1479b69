package com.ebupt.cmi.clientmanagement.domain.entity;


import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;

/**
 * (CmsChannelKanban)实体类
 *
 * <AUTHOR>
 * @since 2024-06-19 18:20:27
 */
@Data
@ToString
public class CmsChannelKanban implements Serializable {
    private static final long serialVersionUID = -45131892624534336L;

    private Long id;
/**
     * 厂商id
     */
    private String corpId;
/**
     * 卡总量
     */
    private Long cardCount;
/**
     * 普通卡总量
     */
    private Long classicCardCount;
/**
     * 普通卡使用量
     */
    private Long classicCardUsedCount;
/**
     * ESIM卡总量
     */
    private Long esimCardCount;
/**
     * ESIM卡使用量
     */
    private Long esimCardUsedCount;
/**
     * IMSI卡使用量
     */
    private Long imsiCardUsedCount;
/**
     * IMSI卡总量
     */
    private Long imsiCardCount;
/**
     * 合作模式1：代销2：A2Z
     */
    private String mode;
/**
     * 创建时间
     */
    private LocalDateTime createTime;
/**
     * 修改时间
     */
    private LocalDateTime updateTime;
}

