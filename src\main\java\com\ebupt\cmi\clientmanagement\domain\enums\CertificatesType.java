package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 证件类型
 * @date 2021/11/29 15:01
 */

@Getter
@AllArgsConstructor
public enum CertificatesType {
    /**
     * 1、护照
     * 2、港澳通行证
     * 3、香港身份证
     * 4、澳门身份证
     */
    /**
     * 1：护照
     */
    PASSPORT("1"),
    /**
     * 2：港澳通行证
     */
    HKANDMOPASS("2"),
    /**
     * 3：香港身份证
     */
    HKIDCARD("3"),
    /**
     * 4：澳门身份证
     */
    MOIDCARD("4");

    String type;
}
