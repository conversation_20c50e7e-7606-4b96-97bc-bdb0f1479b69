package com.ebupt.cmi.clientmanagement.consumer.hvshare.context;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.BaseMessageVO;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseContext.java
 * @Description hv共享抽象上下文类
 * @createTime 2022年02月28日 16:27:00
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class BaseContext {

    protected BaseMessageVO messageVO;

    protected QueueEnum queueEnum;

    protected HcardInfo hcardInfo;

    /**
     * 重试
     */
    Integer retryTimes;

    /**
     * 给套餐延期激活用的，实在没办法了
     */
    Date expireTime;
}
