---
description: 
globs: 
alwaysApply: false
---
请分析我选中的代码中的接口方法，追踪其完整的业务逻辑流程并生成详细的需求实现方案文档。具体要求：

1. **代码分析范围**：
   - 从选中的接口方法入口开始，深入分析完整的调用链路
   - 追踪所有相关的服务层、数据访问层、实体类等组件
   - 识别涉及的数据库操作、事务处理、异常处理等关键逻辑
   - 分析输入参数的结构、验证规则和业务含义

2. **输出文档内容要求**：
   - **接口基本信息**：HTTP方法、路径、功能描述、业务目标
   - **输入输出规范**：请求参数结构、响应数据格式，包含所有字段的中文释义
   - **业务流程图**：详细的调用步骤和数据流转过程
   - **核心组件清单**：涉及的Controller、Service、DAO/Repository、Entity等类和方法
   - **数据库设计**：相关数据表结构、字段定义（包含中文释义）、主外键关系
   - **异常处理机制**：错误码定义、异常处理逻辑
   - **业务规则说明**：数据验证规则、业务约束条件

3. **重要约束**：
   - 严格基于当前代码库中已存在的实现进行分析，不添加任何假设或扩展功能
   - 确保所有分析内容的准确性，避免推测性描述
   - 重点关注实际的业务逻辑实现，而非理论设计

4. **输出要求**：
   - 将完整的分析报告写入到 `documents` 目录下
   - 文件名格式：`[接口名称]业务逻辑分析报告.md`
   - 使用Markdown格式，包含清晰的章节结构和代码示例



