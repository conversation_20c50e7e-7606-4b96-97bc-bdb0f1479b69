package com.ebupt.cmi.clientmanagement.domain.entity.realname;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelRealNameOrder.java
 * @Description cms_channel_realname_order
 * @createTime 2021年11月29日 14:52:00
 */

@TableName("cms_channel_realname_order")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelRealNameOrder {
    Long id;
    Long authId;
    String iccid;
    String orderUniqueId;
    String orderId;
}
