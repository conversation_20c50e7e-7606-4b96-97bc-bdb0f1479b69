package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cmsCardQuota对象", description="")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsCardQuota{


    private Long id;

    private String imsi;
    /**
     * 卡已分配配额
     */
    private BigDecimal quota;

    private String flowPoolUniqueId;

}

