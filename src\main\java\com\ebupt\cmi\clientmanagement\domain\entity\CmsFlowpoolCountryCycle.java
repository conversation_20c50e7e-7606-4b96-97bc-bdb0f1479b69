package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CmsFlowpoolCountryCycle对象", description = "")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsFlowpoolCountryCycle implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键	auto_increment comment 'id',	")
    private Long id;

    @ApiModelProperty(value = "流量池id")
    private String flowPoolId;

    @ApiModelProperty(value = "国家id")
    private String mcc;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
