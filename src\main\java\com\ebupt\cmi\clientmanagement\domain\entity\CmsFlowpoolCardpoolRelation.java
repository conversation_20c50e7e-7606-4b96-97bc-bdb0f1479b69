package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsFlowpoolCardpoolRelation对象", description="")
@AllArgsConstructor
@NoArgsConstructor
public class CmsFlowpoolCardpoolRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    private Long id;

    @ApiModelProperty(value = "流量池id")
    private String flowPoolId;

    @ApiModelProperty(value = "卡池id")
    private String poolId;

    @ApiModelProperty(value = "国家码")
    @TableField("Mcc")
    private String mcc;

    @ApiModelProperty(value = "比例 取值范围 0-100")
    private Integer rate;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    public CmsFlowpoolCardpoolRelation(String poolId,String mcc, Integer rate, String flowPoolId){
        this.flowPoolId = flowPoolId;
        this.poolId = poolId;
        this.mcc = mcc;
        this.rate = rate;
    }
}
