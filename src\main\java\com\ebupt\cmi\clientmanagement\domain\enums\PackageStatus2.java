package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 套餐状态
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/5/18 17:39
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum PackageStatus2 {

    NOT_ON_SALE("1", "待上架"),

    NORMAL("2", "正常"),

    OFF_THE_SHELF("3", "已下架"),

    DELETE("4", "已删除");


    private String k;

    private String val;

    public static String getVal(String k) {
        for (PackageStatus2 p: PackageStatus2.values()){
            if (p.getK().equals(k)){
                return p.getVal();
            }
        }
        log.info("输入不符合要求：{}",k);
        return "";
    }
}
