package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 客户与卡关系表实体
 * @date 2021/4/16 14:03
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_card_bak")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelCardBak extends BaseEntity {

    private Long id;

    private String corpId;

    private String imsi;

    private String msisdn;

    private String iccid;

    /**
     * 卡类型: 1:H  2:V
     */
    private String cardType;


    /**
     * 卡池ID 当Card_type=2时不为空
     */
    private String poolId;

    private String lastVimsi;

    private Date lastReportTime;

    /**
     * 卡形态 1普通 2esim 3贴片卡
     */
    private String cardForm;

    /**
     *  套餐唯一ID
     */
    private String packageUniqueId;

    /**
     * 状态，默认值2（DB默认）
     * 1：已分配
     * 2：待分配
     */
    private String status;

    /**
     * 流量池单周期总量，MB
     */
    private BigDecimal dailyTotal;

    /**
     * 流量池总量，MB
     */
    private BigDecimal total;

    /**
     * 流量池限速类型
     * 1：达量继续使用
     * 2：达量限速
     * 3：达量停用
     */
    private String rateType;

    /**
     * 流量池当前限速类型
     * 1：正常
     * 2：单卡周期达量限速
     * 3：单卡周期达量停用
     * 4：单卡总量达量限速
     * 5：单卡总量达量停用
     * 6：流量池上限限速
     * 7：流量池上限达量
     */
    private String currentRateType;

    /**
     * 流量池ID
     */
    private String flowPoolId;

    /**
     * 入流量池时间
     */
    private Date intoPoolTime;

    /**
     * 合作模式
     * 1：代销
     * 2：A2Z
     */
    private String cooperationMode;

    @AllArgsConstructor
    public enum StatusEnum {

        /**
         * 1：已分配
         */
        ASSIGNED("1"),

        /**
         * 2：待分配
         */
        UNASSIGNED("2");

        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }

    @AllArgsConstructor
    public enum rateTypeEnum {

        /**
         * 1：达量继续使用
         */
        CONTINUE_TO_USE("1"),

        /**
         * 2：达量限速
         */
        SPEED_LIMIT("2"),

        /**
         * 3：达量停用
         */
        DEACTIVATION("3");


        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }

}
