package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.reaching;

import com.alibaba.nacos.client.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching.ReachingTreatmentContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.BaseMessageVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.ReachingTreatmentVO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageDirectionRelation;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.UnSubscribeServiceVO;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.mapper.ChannelPackageCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.PackageDirectionRelationMapper;
import com.ebupt.cmi.clientmanagement.service.PackageOverdueService;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReachingReleaseStrategy.java
 * @Description 达量释放逻辑
 * @createTime 2022年03月04日 11:10:00
 */

@Component("reachingReleaseStrategy")
@Slf4j
public class ReachingReleaseStrategy extends AbstractOutsideNetStrategy {

    public final static String SUCCESS = "Success";

    public final static String FAIL = "Fail";

    @Autowired
    PackageOverdueService packageOverdueService;

    @Autowired
    HvShareRepository hvShareRepository;

    @Resource
    PackageDirectionRelationMapper packageDirectionRelationMapper;

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Override
    protected boolean tryOutsideNet(BaseContext context) {
        try {

            log.info("========================这是第{}次进行外部网元交互重试============================",
                    context.getRetryTimes());

            HcardInfo hcardInfo = context.getHcardInfo();

            ReachingTreatmentVO messageVO = (ReachingTreatmentVO) context.getMessageVO();

            ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;

            log.debug("循环处理此套餐所有的V卡");
            for (String vimsi : reachingTreatmentContext.getVImsi()) {

                VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                        .getVcardAccountInfo(vimsi));
                if ("4".equals(vcardInfo.getStatus())) {
                    log.debug("此V卡处于暂停状态，不用处理");
                    continue;
                }

                CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                        .getCardPoolByImsi(vimsi));
                if (org.springframework.util.StringUtils.isEmpty(cardPool.getIsSignUpcc()) || !"1".equals(cardPool.getIsSignUpcc())) {
                    log.debug("此V卡卡池不是UPCC动态签约，不用处理");
                    continue;
                }


                if (!CollectionUtils.isEmpty(reachingTreatmentContext.getApp())) {
                    log.debug("删除此V卡限速应用签约");
                    for (Long appId : reachingTreatmentContext.getApp()) {
                        hvShareRepository.extracted(false, appId, null, vimsi, vcardInfo.getMsisdn());
                        reachingTreatmentContext.setRetryTimes(0);
                    }
                }


                log.debug("处理此V卡通用流量签约");
                coreNetCaller.upccUnSignature(vcardInfo.getMsisdn(), vcardInfo.getUpccSignBizId());

                UpdateOpenStatusReq updateOpenStatusReq = UpdateOpenStatusReq.builder()
                        .imsi(vcardInfo.getImsi())
                        .upccSignBizId(StringPool.EMPTY)
                        .build();
                Response.getAndCheckRemoteData(pmsFeignClient.updateVimsiOpenStatus(updateOpenStatusReq));

                reachingTreatmentContext.setRetryTimes(0);
            }

            if (!CollectionUtils.isEmpty(reachingTreatmentContext.getApp())) {
                log.debug("删除此H卡限速应用签约");
                for (Long appId : reachingTreatmentContext.getApp()) {
                    hvShareRepository.extracted(false, appId, null, hcardInfo.getImsi(), hcardInfo.getMsisdn());
                    reachingTreatmentContext.setRetryTimes(0);
                }
            }


            log.debug("处理H卡通用流量签约");
            coreNetCaller.upccUnSignature(hcardInfo.getMsisdn(), hcardInfo.getUpccSignBizId());

            UpdateOpenStatusReq updateOpenStatusReq = UpdateOpenStatusReq.builder()
                    .imsi(hcardInfo.getImsi())
                    .upccSignBizId(StringPool.EMPTY)
                    .build();
            Response.getAndCheckRemoteData(pmsFeignClient.updateCardOpenStatus(updateOpenStatusReq));

            log.info("=======================非常幸运，通过了，达量释放外部网元交互流程===========================");

            return true;


        } catch (Exception ex) {

            log.error("调用外部网元时发生致命错误，位置：通用达量释放流程");

            log.error("", ex);

            return false;

        }
    }

    @Override
    public <T extends BaseContext> void handle(T context) {

        String packageUniqueId = context.getMessageVO().getPackageUniqueId();

        PackageDirectionRelation packageDirectionRelation = packageDirectionRelationMapper.selectOne(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId)
                .eq(PackageDirectionRelation::getDirectType, PackageDirectionRelation.DirectType.FREE_FLOW.getValue())
                .ne(PackageDirectionRelation::getHasUsed, PackageDirectionRelation.Status.HAS_USED.getValue()));

        if (packageDirectionRelation == null) {
            log.debug("没有其它未使用得免流定向应用，释放套餐");
            ChannelPackageCard channelPackageCard = hvShareRepository
                    .getChannelPackageCardByPackageUniqueId(packageUniqueId);
            hvShareRepository.release(channelPackageCard);
        } else {
            log.debug("还有其它未使用完的定向套餐，仅回收通用流量");
            hvShareRepository.refreshChannelPackageCardPackageStatus(packageUniqueId,
                    ChannelPackageCard.SurfStatusEnum.LIMIT.getValue());
            hvShareRepository.refreshChannelPackageCardSurfStatus(packageUniqueId, ChannelPackageCard.SurfStatusEnum.LIMIT.getValue(), false);
            ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;
            super.setHcardIntoContext(context);
            String packageUniqueIDFromTable = context.getHcardInfo().getUpccSignPackageUniqueId();
            if (vertifyPackageUniqueID(packageUniqueIDFromTable, packageUniqueId)) {
                reachingTreatmentContext.setNeedCallback(true);
            }
        }
    }

    @Override
    public <T extends BaseContext> void beforeCallBack(T context) {
        Set<String> vimsi = hvShareRepository.getSurfCardByPackageUniqueId(context.getMessageVO().getPackageUniqueId());
        ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;
        reachingTreatmentContext.setVImsi(vimsi);
        Set<Long> app = hvShareRepository.getLimitSurfAppByPackageUniqueId(context.getMessageVO().getPackageUniqueId());
        reachingTreatmentContext.setApp(app);
    }

}
