package com.ebupt.cmi.clientmanagement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsUserAccount;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.Trajectories;
import com.ebupt.cmi.clientmanagement.domain.vo.TrajectoriesVO;
import com.ebupt.cmi.clientmanagement.mapper.UserAccountService;
import com.ebupt.cmi.clientmanagement.service.TrajectoriesService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * UsedTrajectoriesController
 * 用户使用轨迹
 * @Author: zhaoqiankun
 * @Date: 2021/5/17 16:19
 */
@RestController
@RequestMapping("/trajectories")
@AllArgsConstructor
public class UsedTrajectoriesController {

    private final TrajectoriesService trajectoriesService;

    @PostMapping("/search/used")
    public Response<List<Trajectories>> usingTrajectories(@RequestBody @Validated TrajectoriesVO trajectoriesVO) {
        return trajectoriesService.usingTrajectories(trajectoriesVO);
    }
}
