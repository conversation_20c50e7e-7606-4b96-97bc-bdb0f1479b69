package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 16:50
 */
@Slf4j
@AllArgsConstructor
@Getter
public enum CardFormEnums {
    /**
     * 1、普通卡
     */
    NORMAL("1","普通卡(实体卡)","Physical SIM"),

    /**
     * 2、Esim
     */
    ESIM("2","Esim卡","ESIM"),

    /**
     * 3、贴片卡
     */
    PATCH_CARD("3","贴片卡","TSIM"),

    /**
     * 4、IMSI号
     */
    IMSI("4","IMSI号","IMSI");

    String type;
    String name;
    String nameEn;

    public static String getName(String type) {
        for (CardFormEnums p : CardFormEnums.values()) {
            if (p.getType().equals(type)) {
                return p.getName();
            }
        }
        log.warn("输入不符合要求：{}", type);
        return "";
    }

    public static String getNameEn(String type) {
        for (CardFormEnums p : CardFormEnums.values()) {
            if (p.getType().equals(type)) {
                return p.getNameEn();
            }
        }
        log.warn("输入不符合要求：{}", type);
        return "";
    }
}
