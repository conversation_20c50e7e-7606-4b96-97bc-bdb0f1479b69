package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.service.ITerminalCorpService;
import com.ebupt.cmi.clientmanagement.service.TerminalOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * TerminalOrderController
 * 终端订单表
 * @Author: zhaoqiankun
 * @Date: 2021/5/14 12:52
 */
@Api(tags = "终端订单相关接口")
@RestController
@RequestMapping("/terminal/order")
@AllArgsConstructor
public class TerminalOrderController {
    private final TerminalOrderService terminalOrderService;

    @ApiOperation(value = "终端订单新增——API套餐购买")
    @PostMapping("/add")
    public Response<String> addTerminalOrder(@RequestBody @Validated OrderPackageVO orderPackageVO) {
        return terminalOrderService.addTerminalOrder(orderPackageVO);
    }
    @ApiOperation(value = "终端订单查询")
    @PostMapping("/get")
    public Response<DataType> getTerminalOrderByID(@RequestParam String orderID,@RequestParam String xwsseHeader ) {
        return Response.ok(terminalOrderService.getTerminalOrderByID(orderID,xwsseHeader));
    }

}
