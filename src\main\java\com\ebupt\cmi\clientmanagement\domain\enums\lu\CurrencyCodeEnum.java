package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 币种编码枚举
 * @date 2021/4/22 15:01
 */
@AllArgsConstructor
@Getter
public enum CurrencyCodeEnum {

    /**
     * 人民币
     */
    CNY("156"),
    /**
     * 美元
     */
    USD("840"),
    /**
     * 港元
     */
    HKD("344")
    ;

    private String currencyCode;

    public static String getCurrencyCodeEnum(String code) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        for (CurrencyCodeEnum currency : CurrencyCodeEnum.values()) {
            if (currency.getCurrencyCode().equals(code)) {
                return currency.name();
            }
        }
        // 如果没有找到对应的编号，返回数字编号本身
        return code;
    }
}
