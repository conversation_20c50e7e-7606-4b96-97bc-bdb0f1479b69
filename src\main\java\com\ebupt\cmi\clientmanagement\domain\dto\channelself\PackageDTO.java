package com.ebupt.cmi.clientmanagement.domain.dto.channelself;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageDTO.java
 * @Description 可购买套餐DTO
 * @createTime 2021年06月17日 17:44:00
 */

@Data
@Builder
@ApiModel
public class PackageDTO {
    @ApiModelProperty(value = "套餐Id")
    String packageId;

    @ApiModelProperty(value = "套餐价格")
    String packagePrice;

    @ApiModelProperty(value = "二次定价价格")
    String groupPrice;

    @ApiModelProperty(value = "套餐组Id")
    String groupId;

    @ApiModelProperty(value = "套餐组名称")
    String groupType;

    @ApiModelProperty(value = "套餐中文名称")
    String nameCn;

    @ApiModelProperty(value = "套餐繁体中文名称")
    String nameTw;

    @ApiModelProperty(value = "套餐英文名称")
    String nameEn;

    @ApiModelProperty(value = "套餐覆盖国家")
    List<MccDTO> mccDtoList;

    @ApiModelProperty(value = "套餐周期类型")
    String periodUnit;

    @ApiModelProperty(value = "套餐持续周期")
    String keepPeriod;

    @ApiModelProperty(value = "UPCC签约业务模板(非限速)")
    String signBizId;

    @ApiModelProperty(value = "UPCC签约业务模板(限速)")
    String limitSignBizId;

    @ApiModelProperty(value = "允许订购开始时间，达到该时间自动上架")
    String startTime;

    @ApiModelProperty(value = "套餐计费激活方式")
    String activationMode;

    String deductionModel;

    List<String> mccList;

    @ApiModelProperty(value = "是否是自建套餐")
    String isChannelCreate;
}
