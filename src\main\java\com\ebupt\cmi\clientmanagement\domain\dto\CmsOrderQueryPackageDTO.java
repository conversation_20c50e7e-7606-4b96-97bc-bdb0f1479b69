package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/27 14:32
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsOrderQueryPackageDTO {

    private String priceCn;

    private String priceTw;

    private String priceEn;

    private String nameCn;

    private String nameEn;

    private String nameTw;

    private String descCn;

    private String descEn;

    private String descTw;

    private String startTime;

    private String endTime;

    private String name;

    private String desc;

    private String price;

    private String supportRefuel;

    private String deductionModel;

    private String flowLimitType;

    public void setInfoByLanguage(String language) {
        if ("CN".equals(language)) {
            name = nameCn;
            desc = descCn;
        } else if ("US".equals(language)) {
            name = nameEn;
            desc = descEn;
        } else {
            name = nameTw;
            desc = descTw;
        }
    }
}
