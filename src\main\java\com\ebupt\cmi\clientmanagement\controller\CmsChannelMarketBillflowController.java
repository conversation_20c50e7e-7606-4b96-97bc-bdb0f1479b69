package com.ebupt.cmi.clientmanagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.ExportVO;
import com.ebupt.cmi.clientmanagement.domain.vo.MarketingAccountFlowVO;
import com.ebupt.cmi.clientmanagement.service.CmsChannelMarketBillflowIService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/cms-channel-market-billflow")
public class CmsChannelMarketBillflowController {

    @Autowired
    private CmsChannelMarketBillflowIService cmsChannelMarketBillflowIService;

    @ApiOperation("查询营销流水明细")
    @GetMapping("/selectMarketingAccountFlow")
    public Response<?> selectMarketingAccountFlow(@RequestParam String corpId
            , @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date beginDate, @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date endDate
            , @RequestParam String cooperationMode
            , @RequestParam Integer pageNum, @RequestParam Integer pageSize) {

        return Response.ok(cmsChannelMarketBillflowIService.selectMarketingAccountFlow(
                MarketingAccountFlowVO
                        .builder()
                        .corpId(corpId)
                        .beginDate(beginDate)
                        .endDate(endDate)
                        .cooperationMode(compatibilityMode(cooperationMode))
                        .pageNum(pageNum)
                        .activityId(null)
                        .pageSize(pageSize).build()));
    }

    @GetMapping("/selectMarketingAccountFlowRebate")
    @ApiOperation("查询营销流水返利明细")
    public Response<?> MarketingAccountFlowBebate(@RequestParam String corpId
            , @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date beginDate, @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date endDate
            , @RequestParam String cooperationMode
            , @RequestParam String activityId
            , @RequestParam Integer pageNum, @RequestParam Integer pageSize) {
        return Response.ok(cmsChannelMarketBillflowIService.selectMarketingAccountFlowRebate(
                MarketingAccountFlowVO
                        .builder()
                        .corpId(corpId)
                        .beginDate(beginDate)
                        .endDate(endDate)
                        .cooperationMode(compatibilityMode(cooperationMode))
                        .pageNum(pageNum)
                        .pageSize(pageSize)
                        .activityId(activityId).build()));
    }


    @GetMapping("/MarketingAccountFlowOut")
    @ApiOperation("营销流水明细导出")
    public Response<ExportVO> MarketingAccountFlowOut(@RequestParam String corpId
            , @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date beginDate, @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date endDate
            , @RequestParam String cooperationMode) {
        return Response.ok(cmsChannelMarketBillflowIService.MarketingAccountFlowOut(corpId, beginDate, endDate, compatibilityMode(cooperationMode),null));
    }

    @GetMapping("/MarketingAccountFlowOutRebate")
    @ApiOperation("营销流水返利明细导出")
    public Response<ExportVO> MarketingAccountFlowOutRebate(@RequestParam String corpId, @RequestParam String activityId
            , @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date beginDate, @RequestParam @DateTimeFormat(pattern = "yyy-MM-dd") Date endDate
            , @RequestParam String cooperationMode) {
        return Response.ok(cmsChannelMarketBillflowIService.MarketingAccountFlowOutRebate(corpId, beginDate, endDate, compatibilityMode(cooperationMode),activityId));
    }

    /**
     *7.4.	单独资源合作模式，可查看营销活动及营销款使用情况（同A2Z）
     * 兼容合作模式
     * @param Mode
     * @return
     */
    private String compatibilityMode(String Mode){
        if ("3".equals(Mode)) {
            return "2";
        }
        return Mode;
    }
}
