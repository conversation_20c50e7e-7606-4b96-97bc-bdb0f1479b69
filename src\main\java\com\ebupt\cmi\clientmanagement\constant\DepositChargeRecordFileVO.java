package com.ebupt.cmi.clientmanagement.constant;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsDepositChargeRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 充值记录文件上传
 * @since 2025/1/9 17:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DepositChargeRecordFileVO {

    @ExcelProperty("CHANNEL_NAME")
    private String channelName;

    @ExcelProperty("CUSTOMER_EBS_CODE")
    private Long customerEbsCode;

    @ExcelProperty("CUST_TRX_TYPE")
    private String custTrxType;

    @ExcelProperty("INVOICE_PERIOD")
    private Long invoicePeriod;

    @ExcelProperty("DEPOSIT_AMOUNT")
    private BigDecimal depositAmount;

    @ExcelProperty("DISCOUNT_AMOUNT")
    private BigDecimal discountAmount;

    @ExcelProperty("NO_TAX_AMOUNT")
    private BigDecimal noTaxAmount;

    @ExcelProperty("TAX_AMOUNT")
    private BigDecimal taxAmount;

    @ExcelProperty("TAX_RATE")
    private BigDecimal taxRate;

    @ExcelProperty("CURRENCY")
    private String currency;

    @ExcelProperty("DEPOSIT_DESC")
    private String depositDesc;

    @ExcelProperty("INVOICE_NUM")
    private String invoiceNum;

    @ExcelProperty("SERVICE_START_DATE")
    private Long serviceStartDate;

    @ExcelProperty("SERVICE_END_DATE")
    private Long serviceEndDate;

    @ExcelProperty("INVOICE_DATE")
    private String invoiceDate;

    @ExcelProperty("PAY_DUE_DATE")
    private String payDueDate;

    @ExcelProperty("ORDER_NUM")
    private String orderNum;

    @ExcelProperty("CUSTOMER_CIRCUIT_ID")
    private String customerCircuitId;

    @ExcelProperty("OU")
    private String ou;

    @ExcelProperty("DEPOSIT_TIME")
    private String depositTime;

    @ExcelProperty("BUSINESS_TYPE")
    private String businessType;

    @ExcelProperty("TEMPLATE_CODE")
    private String templateCode;

    @ExcelProperty("PRODUCT_CODE")
    private String productCode;

    @ExcelProperty("BATCH_ID")
    private String batchId;

    @ExcelProperty("EXTAND_COL1")
    private String extandCol1;

    @ExcelProperty("EXTAND_COL2")
    private String extandCol2;

    /**
     *文件名
     */
    @ExcelProperty("EXTAND_COL3")
    private String extandCol3;

    @ExcelProperty("EXTAND_COL4")
    private String extandCol4;

    @ExcelProperty("EXTAND_COL5")
    private String extandCol5;

}
