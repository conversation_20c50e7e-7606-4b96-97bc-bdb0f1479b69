package com.ebupt.cmi.clientmanagement.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel
@ColumnWidth(23)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT)
public class ChannelOrderExportTemplate {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "订单时间", index = 0)
    private Date orderDate;

    @ExcelProperty(value = "渠道商", index = 1)
    private String user;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "订单编号", index = 2)
    private String orderUniqueId;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "ICCID", index = 3)
    private String iccid;

    @ExcelProperty(value = "购买套餐", index = 4)
    private String nameEn;

    @ExcelProperty(value = "购买渠道", index = 5)
    private String orderChannel;

    @ExcelProperty(value = "订单状态", index = 6)
    private String orderStatus;

    @ExcelProperty(value = "购买份数", index = 7)
    private Integer count;

    @ExcelProperty(value = "金额", index = 8)
    private BigDecimal amount;

    @ExcelProperty(value = "币种", index = 9)
    private String currencyCode;
}
