package com.ebupt.cmi.clientmanagement.domain.dto;

import cn.hutool.core.annotation.Alias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProvinceBillVo {

    @Alias(value = "pro_order_id")
    private String proOrderId;

    @Alias(value = "prod_inst_id")
    private String prodInstId;

    @Alias(value = "msisdn")
    private String msisdn;

    @Alias(value = "user_type")
    private String userType;

    @Alias(value = "prov_cd")
    private String provCd;

    @Alias(value = "cmlink_msisdn")
    private String iccid;

    @Alias(value = "productAttr")
    private String productAttr;

    @Alias(value = "product_id")
    private String productId;

    @Alias(value = "product_status")
    private String productStatus;

    @Alias(value = "validTime")
    private String validTime;

    @Alias(value = "expireTime")
    private String expireTime;

    @Alias(value = "fee_type")
    private String feeType;

    @Alias(value = "fee_cycle")
    private String feeCycle;

    @Alias(value = "currency")
    private String currency;

    @Alias(value = "fee")
    private String fee;

    @Alias(value = "dom_settle_currency")
    private String domSettleCurrency;

    @Alias(value = "convert_exchange_rate")
    private String convertExchangeRate;

    @Alias(value = "dom_prov_pay_chg_rmb")
    private String domProvPayChgRmb;

    @Alias(value = "channel_cd")
    private String channelCd;

    @Alias(value = "order_time")
    private String orderTime;

    @Alias(value = "billing_time")
    private String billingTime;

    @Alias(value = "pay_mode")
    private String payMode;

    @Alias(value =  "product_category")
    private String productCategory;


}
