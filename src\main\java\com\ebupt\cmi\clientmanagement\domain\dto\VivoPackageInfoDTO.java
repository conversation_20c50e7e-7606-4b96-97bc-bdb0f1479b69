package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * VivoPackageInfoVO
 *
 * @Author: zhaoqiankun
 * @Date: 2021/5/20 14:17
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class VivoPackageInfoDTO {
    /**
     * 套餐已使用总量，精确到KB；
     */
    @NotBlank
    private BigDecimal trafficUsed;
    /**
     * 每天的流量使用情况；
     */
    private List<DailyTraffic> dailyTrafficList;
}
