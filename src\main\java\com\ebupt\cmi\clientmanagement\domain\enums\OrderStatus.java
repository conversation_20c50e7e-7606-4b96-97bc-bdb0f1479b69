package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 16:50
 */

@AllArgsConstructor
@Getter
public enum OrderStatus {
    /**
     * 1、待发货
     */
    WAIT_FOR_DELIVERY("1"),

    /**
     * 2、完成
     */
    COMPLETE("2"),

    /**
     * 3、已退订
     */
    SALES_RETURN("3"),

    /**
     * 4、激活退订待审批
     */
    WAIT_APPROVAL("4"),
    /**
     * 5、部分退订
     */
    PART_SALES_RETURN("5"),


    /**
     * 6、部分发货
     */
    PART_DELIVERY_RETURN("6"),

    /**
     * 已回收
     */
    RECYCLED("7"),

    /**
     * 复合状态
     */
    MULTI_STATUS("9");

    String status;
}
