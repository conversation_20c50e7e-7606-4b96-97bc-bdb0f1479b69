package com.ebupt.cmi.clientmanagement.domain.dto.through;

import com.ebupt.cmi.clientmanagement.domain.vo.through.NotifyActivationVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NotifyDTO.java
 * @Description 激活通知接口DTO
 * @createTime 2021年05月18日 10:28:00
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyActivationDTO {

    String imsi;

    String iccid;

    String netTime;

    String mcc;

    public NotifyActivationDTO(NotifyActivationVO notifyVO) {
        this.imsi = notifyVO.getImsi();
        this.iccid = notifyVO.getIccid();
        this.mcc = notifyVO.getMcc();
        this.netTime = notifyVO.getNetTime();
    }
}
