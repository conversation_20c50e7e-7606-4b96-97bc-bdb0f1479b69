package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsTopchannel对象", description="")
public class CmsTopchannel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "厂商ID")
    @TableId
    private String corpId;

    @ApiModelProperty(value = "厂商名称")
    private String corpName;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "是否允许订购1：是2：否")
    private String isSub;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "EBSCode")
    private String ebsCode;

    @ApiModelProperty(value = "联系人邮箱")
    private String email;

    /**
     * 目前没用，杭老板不给钱
     */
    /**
     * 那就林老板给钱
     */
    @ApiModelProperty(value = "审核状态1：审核中2：通过3：不通过")
    private String authStatus;

    @ApiModelProperty(value = "APPkey")
    private String appKey;

    @ApiModelProperty(value = "APPsecret")
    private String appSecret;

    @ApiModelProperty(value = "创建时间默认值：CURRENT_TIMESTAMP")
    private Date createTime;

    @ApiModelProperty(value = "修改时间默认值：CURRENT_TIMESTAMP")
    private Date updateTime;

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum isSubEnum{
        /**
         * 1.允许订购
         */
        YES("1"),
        /**
         * 2.不允许订购
         */
        NO("2");

        @Getter
        private String value;
    }
}
