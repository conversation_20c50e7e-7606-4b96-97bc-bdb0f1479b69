package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KanBanDTO {

    /**
     * 总额度
     */
    private BigDecimal totalDeposit;

    /**
     * 可用额度
     */
    private BigDecimal deposit;

    /**
     * 当月已使用额度
     * 渠道商订单每日累加
     */
    private BigDecimal usedDepositMonth;

    /**
     * A2Z预存款额度
     */
    private BigDecimal a2zPreDeposit;

    /**
     * A2Z总信用额度（A2Z预存款额度+停止使用提醒阈值）
     */
    private BigDecimal a2zTotalDeposit;

    /**
     * 当月已使用额度
     * 物联网账单每日累加
     */
    private BigDecimal a2zUsedDepositMonth;

    /**
     * 卡总量
     */
    private Long cardCount;

    /**
     * 普通卡总量
     */
    private Long classicCardCount;

    /**
     * 普通卡使用量
     */
    private Long classicCardUsedCount;

    /**
     * ESIM卡总量
     */
    private Long EsimCardCount;

    /**
     * ESIM卡使用量
     */
    private Long EsimCardUsedCount;

//    /**
//     * 贴片卡总量
//     */
//    private Long SIMPlusCount;
//
//    /**
//     * 贴片卡使用量
//     */
//    private Long SIMPlusUsedCount;

    /**
     * IMSI卡总量
     */
    private Long ImsiCardUsedCount;

    /**
     * IMSI卡使用量
     */
    private Long ImsiCardCount;

    private List<SaleStatDTO> saleStatList;
}
