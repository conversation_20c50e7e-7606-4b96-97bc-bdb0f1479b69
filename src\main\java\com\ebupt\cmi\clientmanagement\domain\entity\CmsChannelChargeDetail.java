package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CmsChannelChargeDetail对象", description = "")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelChargeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "厂商id")
    private String corpId;

    @ApiModelProperty(value = "充值金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "充值时间默认值：CURRENT_TIMESTAMP")
    private Date chargeTime;

    @ApiModelProperty(value = "付款证明文件地址")
    private String paymentProofAddress;

    @ApiModelProperty(value = "充值类型1.缴付账单2.充值押金")
    private String chargeType;

    @ApiModelProperty(value = "充值状态：1.待确认到账2.已到账3.未到账4.已取消")
    private String chargeStatus;

    @ApiModelProperty(value = "1.待审批2.审批通过3.审批不通过")
    private String authStatus;

    @ApiModelProperty(value = "审批人名字")
    private String authName;

    @ApiModelProperty(value = "审批不通过原因")
    private String noPassReason;

    private long accountId;

    private String toDoItemId;

    @TableField(exist = false)
    private String cropName;

    @TableField(exist = false)
    private String ebsCode;

    @TableField(exist = false)
    private String currency;

    @TableField(exist = false)
    private String invoiceAddress;

    @TableField(exist = false)
    private String channelType;

    @TableField(exist = false)
    private String invoiceNo;

    @ApiModelProperty(value = "确认到账时间")
    @JsonFormat(pattern = "yyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    @ApiModelProperty(value = "银行收款单编号")
    private String receiptNum;
}
