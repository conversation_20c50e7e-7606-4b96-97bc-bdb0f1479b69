package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.*;

import java.math.BigDecimal;

@Builder
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DepositInvoiceTempParam {

    private String companyName;
    private String companyAddress;
    private String invoiceNo;
    private String invoiceDate;
    private String invoiceType;
    private String productName;
    private String currency;
    private String amount;
    private String totalAmount;
    private String paymentInstruction;
}
