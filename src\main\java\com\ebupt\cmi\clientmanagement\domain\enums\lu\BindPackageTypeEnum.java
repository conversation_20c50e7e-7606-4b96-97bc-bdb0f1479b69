package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 卡与套餐绑定表，套餐类型枚举
 * @date 2021/4/19 16:05
 */
@Getter
@AllArgsConstructor
public enum BindPackageTypeEnum {

    /**
     * 套餐
     */
    PACKAGE("1"),

    /**
     * 终端线下卡池套餐
     */
    CARD_POOL("2"),

    /**
     * 流量池
     */
    FLOW_POOL("3");

    private String packageType;
}
