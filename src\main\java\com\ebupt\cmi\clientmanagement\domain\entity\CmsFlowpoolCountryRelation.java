package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsFlowpoolCountryRelation对象", description="")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsFlowpoolCountryRelation implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "自增主键")
    private Long id;

    @ApiModelProperty(value = "流量池id")
    private String flowPoolId;

    @ApiModelProperty(value = "国家id")
    private String mcc;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    public CmsFlowpoolCountryRelation(String flowPoolId, String mcc){
        this.flowPoolId = flowPoolId;
        this.mcc = mcc;
    }


}
