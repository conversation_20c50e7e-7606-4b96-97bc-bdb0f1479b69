package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PmsRefuelPack对象", description="加油包信息表")
public class PmsRefuelPack implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "加油包id")
    @TableId
    private String id;

    @ApiModelProperty(value = "简体中文名称")
    @NotBlank(message = "加油包中文名称不能为空")
    private String nameCn;

    @ApiModelProperty(value = "繁体中文名称")
    @NotBlank(message = "加油包台湾名称不能为空")
    private String nameTw;

    @ApiModelProperty(value = "英文名称")
    @NotBlank(message = "加油包英文名称不能为空")
    private String nameEn;

    @ApiModelProperty(value = "流量值")
    @NotNull(message = "加油包流量值不能为空")
    private Integer flowValue;

    @ApiModelProperty(value = "流量单位 1:MB,2:GB")
    @NotBlank(message = "加油包流量单位不能为空")
    private String flowUnit;

    @ApiModelProperty(value = "港币价格")
    @NotNull(message = "加油包港币价格不能为空")
    private BigDecimal hkd;

    @ApiModelProperty(value = "美元价格")
    @NotNull(message = "加油包美元价格不能为空")
    private BigDecimal usd;

    @ApiModelProperty(value = "人民币价格")
    @NotNull(message = "加油包人民币价格不能为空")
    private BigDecimal cny;

    @ApiModelProperty(value = "是否允许订购 1：是; 2：否")
    @NotBlank(message = "加油包是否允许订购不能为空")
    private String allowSub;

    @ApiModelProperty(value = "审批状态1：新建待审批 2：通过 3：新建审批不通过 4：修改待审批 5：删除待审批")
    private String authStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    private String currencyCode;

    @AllArgsConstructor
    @Getter
    public enum AuthStatusEnum {

        /**
         * 新建待审批
         */
        NEW_NEED_APPROVAL("1"),
        /**
         * 2：通过
         */
        PASS("2"),
        /**
         * 3：不通过
         */
        NOT_PASS("3"),
        /**
         * 4：修改待审批
         */
        ALTER_NEED_APPROVAL("4"),
        /**
         * 5：删除待审批
         */
        DELETE_NEED_APPROVAL("5");

        String status;
    }

    @AllArgsConstructor
    @Getter
    public enum AllowSubEnum {

        /**
         * 允许订购
         */
        YES("1"),

        /**
         * 不允许订购
         */
        NO("2");

        String status;
    }


}
