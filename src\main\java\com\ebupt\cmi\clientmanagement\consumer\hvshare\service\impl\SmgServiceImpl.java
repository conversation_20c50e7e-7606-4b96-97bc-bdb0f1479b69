package com.ebupt.cmi.clientmanagement.consumer.hvshare.service.impl;

import cn.hutool.core.date.DateUtil;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.service.SmgService;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardVO;
import com.ebupt.cmi.clientmanagement.feign.sms.SmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.sms.domain.NoticeSmsVO;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SmgServiceImpl.java
 * @Description 实现类
 * @createTime 2021年07月12日 10:34:00
 */
@Service
@AllArgsConstructor
@Slf4j
public class SmgServiceImpl implements SmgService {

    private final SmsFeignClient smsFeignClient;

    private final static String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public String sendNoticeSms(String phone, Long templateId, Integer sceneId, String sendLang, String packageUniqueId, String mcc) {
        String errorMsg = "下发通知短信失败，请核实数据是否正确。";
        if (templateId == null) {
            throw new BizException(errorMsg + "短信模板id为空");
        }
        if (sceneId == null) {
            throw new BizException(errorMsg + "场景id为空");
        }
        if (!StringUtils.hasText(sendLang)) {
            throw new BizException(errorMsg + "发送语言为空");
        }
        Response<Void> smsResult = smsFeignClient.sendNoticeSms(NoticeSmsVO.builder()
                .phone(phone)
                .language(sendLang)
                .templateId(templateId)
                .sceneId(sceneId)
                .packageUniqueId(packageUniqueId)
                .mcc(mcc)
                .build());
        return smsResult.getCode();
    }

    /**
     * 下发激活短信（用户确认激活触发的短信场景）
     * @param card 主卡信息
     * @param packageUniqueId 套餐唯一id
     * @param packageName 套餐名称
     * @param activeTime 套餐激活时间
     * @param expireTime 套餐到期时间
     */
    @Override
    public void sendActivatedSms(CardVO card, String packageUniqueId, String packageName,
                                 Date activeTime, Date expireTime) {
        Map<String, List<String>> params = ImmutableMap.of(BizConstants.SmsPlaceHolder.PACKAGE_NAME,
                Collections.singletonList(packageName),
                BizConstants.SmsPlaceHolder.ACTIVE_TIME, Collections.singletonList(formatDate(activeTime)),
                BizConstants.SmsPlaceHolder.EXPIRE_TIME, Collections.singletonList(formatDate(expireTime)));
        sendNoticeSms(card, BizConstants.ACTIVATION_SCENE_ID, packageUniqueId, params);
    }

    /**
     * 下发通知短信
     * @param card 主卡信息
     * @param sceneId 场景ID
     * @param packageUniqueId packageUniqueId
     * @param params
     */
    void sendNoticeSms(CardVO card, Integer sceneId, String packageUniqueId,
                       Map<String, List<String>> params) {
        sendNoticeSms(card, sceneId, packageUniqueId, null, params);
    }

    /**
     * 下发通知短信
     * @param card 主卡信息
     * @param sceneId 场景ID
     * @param packageUniqueId packageUniqueId
     * @param mcc mcc
     * @param params
     */
    void sendNoticeSms(CardVO card, Integer sceneId, String packageUniqueId, String mcc,
                       Map<String, List<String>> params) {
        sendNoticeSms(card, sceneId, packageUniqueId, null, mcc, params);
    }

    /**
     * 下发通知短信
     * @param card 主卡信息
     * @param sceneId 场景ID
     * @param packageUniqueId packageUniqueId
     * @param mcc mcc
     * @param params 参数键值对
     */
    public void sendNoticeSms(CardVO card, Integer sceneId, String packageUniqueId, String orderUniqueId, String mcc,
                              Map<String, List<String>> params) {
        try {
            Response<Void> response = smsFeignClient.sendNoticeSms(NoticeSmsVO.builder()
                    .language(card.getSendLang())
                    .phone(card.getMsisdn())
                    .sceneId(sceneId)
                    .mcc(mcc)
                    .templateId(card.getTemplateId())
                    .packageUniqueId(packageUniqueId)
                    .orderUniqueId(orderUniqueId)
                    .params(params)
                    .build());
            log.debug("通知短信下发结果 code: {}, msg: {}", response.getCode(), response.getMsg());
        } catch (Exception e) {
            log.warn("下发通知短信失败 imsi: {}, sceneId: {}", card.getImsi(), sceneId, e);
        }
    }

    /**
     * 格式化日期
     * @param date
     * @return yyyy-MM-dd HH:mm:ss格式
     */
    private String formatDate(Date date) {
        return DateUtil.format(date, DATE_FORMAT);
    }
}
