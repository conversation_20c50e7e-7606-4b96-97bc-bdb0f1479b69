package com.ebupt.cmi.clientmanagement.domain.dto.lu;

import com.ebupt.cmi.clientmanagement.feign.pms.domain.PreActivePackageDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description
 * @date 2021/4/20 11:17
 */
@Data
@ToString
@AllArgsConstructor
public class JudgePackageDTO {

    /**
     * 厂商id
     */
    private String corpId;

    /**
     * 套餐与卡信息表id
     */
    private Long packageCardId;

    /**
     * 是否有已激活套餐
     */
    private Boolean hasActivatedPackage;

    /**
     * v卡相关数据
     */
    private PreActivePackageDTO preActivePackageDTO;

    /**
     * 订单唯一id
     */
    private String orderUniqueId;

    /**
     * 套餐唯一id
     */
    private String packageUniqueId;

    /**
     * H卡非限速签约业务id
     */
    private String signBizId;

    /**
     * H卡限速签约业务id
     */
    private String limitSpeedSignBizId;

    private boolean hasVcard;


    public JudgePackageDTO(Boolean hasActivatedPackage) {
        this.hasActivatedPackage = hasActivatedPackage;
    }
}
