package com.ebupt.cmi.clientmanagement.domain.dto.channelself;


import com.ebupt.excel.annotation.ExcelExport;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
public class SubOrderDTO {

    @ApiModelProperty(value = "订单号，注意，不是订单表的主键，是uuid")
    @ExcelExport(description = "Order Number", index = "0")
    String orderId;

    @ApiModelProperty(value = "卡号")
    @ExcelExport(description = "ICCID", index = "1")
    String iccid;


    @ApiModelProperty(value = "套餐英文名称")
    @JsonProperty("packageNameEn")
    @ExcelExport(description = "Package Name", index = "2")
    String packageNameEn;

    @ApiModelProperty(value = "购买渠道")
    @ExcelExport(description = "Method", index = "3")
    String orderChannel;

    @ApiModelProperty(value = "订单状态")
    @ExcelExport(description = "Order Status", index = "4")
    String orderStatus;

    @ApiModelProperty(value = "数量")
    @ExcelExport(description = "QTY", index = "5")
    String count;

    @ApiModelProperty(value = "订单金额")
    @ExcelExport(description = "Order Amount", index = "6")
    BigDecimal subAmount;

    @ApiModelProperty(value = "创建日期")
    @ExcelExport(description = "Creation Time", index = "7")
    String orderDate;

    @ApiModelProperty(value = "订单类型")
    String orderType;

    @ApiModelProperty(value = "订单id,订单表主键id")
    Long id;

    @ExcelExport(description = "Cancelled Time", index = "8")
    String unsubscribeTime;

    @ExcelExport(description = "Update Time", index = "9")
    String updateTime;


    @ApiModelProperty(value = "归属渠道商")
    @ExcelExport(description = "Channel", index = "10")
    String revertChannelName;
}
