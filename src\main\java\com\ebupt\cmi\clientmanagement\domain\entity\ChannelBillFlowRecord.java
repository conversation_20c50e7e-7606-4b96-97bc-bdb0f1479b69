package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@TableName("cms_channel_billflow_record")
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelBillFlowRecord {

    private Long id;

    private String corpId;

    private String type;

    private String currencyCode;

    private BigDecimal amount;

    private BigDecimal deposit;

    //应对需求 时间不够才加的这个

    @TableField(exist = false)
    private String tDeposit;

    /*
    * 渠道商合作模式
    * */
    private String cooperationMode;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

}
