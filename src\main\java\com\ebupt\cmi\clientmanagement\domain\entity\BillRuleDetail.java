package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description
 * @date 2021/4/27 11:28
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_bill_rule_detail")
@Data
@ToString
@Builder
public class BillRuleDetail extends BaseEntity {

    private Long id;
    private String corpId;

    private String billRule;

    private String packageId;

    private String packageName;

    private String billContent;

}
