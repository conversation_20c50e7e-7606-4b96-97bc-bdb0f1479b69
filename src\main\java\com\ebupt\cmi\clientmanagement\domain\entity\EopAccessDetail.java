package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 能力接入详情表实体
 * @date 2021/4/26 20:34
 */
@Data
@ToString
@TableName("cms_eop_access_detail")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EopAccessDetail {

    /**
     * 主键
     */
    @TableId
    private String corpId;

    private String appKey;

    private String appSecret;

    private String userName;

    private String notifyUrl;

    /**
     * 登网通知类型
     * 1：通知
     * 2:不通知
     */
    private String activeNotifyType;

    /**
     * 到期通知类型
     * 1：通知
     * 2:不通知
     */
    private String dueNotifyType;

    private String eopCreateType;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 到期通知类型枚举
     * <AUTHOR>
     * @date 2021-5-31 15:09:24
     */
    @Getter
    @AllArgsConstructor
    public enum DueNotifyTypeEnum {

        /**
         * 通知
         */
        YES("1"),

        /**
         * 不通知
         */
        NO("2");

        private String value;

    }

}
