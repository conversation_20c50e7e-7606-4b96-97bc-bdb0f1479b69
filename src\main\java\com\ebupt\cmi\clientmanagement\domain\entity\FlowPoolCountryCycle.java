package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 流量池国家关联周期表
 */
@Data
@TableName("cms_flowpool_country_cycle")
public class FlowPoolCountryCycle extends BaseEntity {

    private Long id;

    private String flowPoolId;

    private String mcc;

}
