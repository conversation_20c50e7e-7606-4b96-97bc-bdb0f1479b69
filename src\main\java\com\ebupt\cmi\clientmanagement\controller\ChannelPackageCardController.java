package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.UserQueryOrderDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.UserQueryPackageReDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.service.ChannelPackageCardService;
import com.ebupt.cmi.clientmanagement.service.PackageEndService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.implementation.bind.annotation.Default;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * ChannelPackageCardController
 * 卡+套餐相关接口
 *
 * @Author: zhaoqiankun
 * @Date: 2021/5/12 18:33
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/packageCard")
@Api(tags = "卡+套餐相关接口")
public class ChannelPackageCardController {
    @Autowired
    private PackageEndService packageEndService;

    @Autowired
    private ChannelPackageCardService packageCardService;

    @GetMapping("/packageuuid/{uuid}")
    public Response<ChannelPackageCard> getPackageNameByUUID(@PathVariable("uuid") String uuid) {
        return Response.ok(packageCardService.getPackageNameByUUID(uuid));
    }

    @PostMapping("/editBizId")
    @ApiOperation(value = "更新签约模板ID", notes = "根据packageID更新模板签约ID")
    public Response<Void> editBizId(@RequestBody ChannelPackageCard channelPackageCard) {
        packageCardService.editBizId(channelPackageCard);
        return Response.ok();
    }

    @PostMapping("/editBilling")
    @ApiOperation(value = "更新计费方式", notes = "根据packageID更新计费方式")
    public Response<Void> editBilling(@RequestBody ChannelPackageCard channelPackageCard) {
        packageCardService.editBilling(channelPackageCard);
        return Response.ok();
    }

    @PostMapping("/editSupportRefuel")
    @ApiOperation(value = "更新是否支持加油包", notes = "根据packageID更新是否支持加油包")
    public Response<Void> editSupportRefuel(@RequestBody ChannelPackageCard channelPackageCard) {
        packageCardService.editSupportRefuel(channelPackageCard);
        return Response.ok();
    }


    /**
     * 套餐提前释放
     */
    @PostMapping("/search")
    @ApiOperation(value = "查询渠卡与套餐信息", notes = "根据iccid、packageID查询卡+套餐信息表，状态为“已激活”")
    public Response<List<Parameter>> packageEndHavePID(@RequestBody List<PackageEndVO> voList,@RequestParam Integer role,@RequestParam String userId) {
        packageEndService.channelApiAccessBatchCheck(userId,role.toString(),voList);
        return packageEndService.packageEn(voList, null);
    }

    @GetMapping("/getList/{iccid}")
    @ApiOperation(value = "根据ICCID查询渠道套餐信息", notes = "根据iccid 查询 客户套餐与卡信息表")
    public Response<List<ChannelPackageCard>> selectPackageCardList(@PathVariable("iccid") String iccid) {
        return Response.ok(packageEndService.selectPackageCardList(iccid));
    }

    @GetMapping("/recycleResource/{iccid}")
    @ApiOperation(value = "主卡销户等资源回收", notes = "根据iccid 查询 进行主卡相关销户")
    public Response<Map<String, String>> recycleResource(@PathVariable String iccid) {
        return packageEndService.recycleResource(iccid, true);
    }

    @GetMapping("/recycleResourceCardUpdate/{iccid}")
    @ApiOperation(value = "主卡销户等资源回收", notes = "根据iccid 查询 进行主卡相关销户(主卡不执行hss销户)")
    public Response<Map<String, String>> recycleResourceCardUpdate(@PathVariable String iccid) {
        return packageEndService.recycleResource(iccid, false);
    }

    @GetMapping("/listPackageCard/{packageId}")
    @ApiOperation(value = "根据packageId查询渠卡与套餐信息", notes = "根据packageId 查询 客户套餐与卡信息表")
    public Response<List<ChannelPackageCard>> listPackageCard(@PathVariable("packageId") String packageId) {
        return Response.ok(packageEndService.selectPackageCardListBypackageID(packageId));
    }

    @GetMapping("/packageCard/{packageUniqueId}")
    @ApiOperation(value = "根据packageUniqueId查询渠卡与套餐信息", notes = "根据packageUniqueId查询 客户套餐与卡信息表")
    public Response<ChannelPackageCard> getPackageCard(@PathVariable("packageUniqueId") String packageUniqueId) {
        return Response.ok(packageEndService.getPackageCard(packageUniqueId));
    }

    @DeleteMapping("/cardRecycleAndClear")
    @ApiOperation("主卡回收清理")
    public Response cardRecycleAndClear(@RequestParam String imsi) {
        try {
            packageEndService.cardRecycleAndClear(imsi);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @PutMapping("/InactivePackageOverdue")
    @ApiOperation("套餐过期清理")
    public Response InactivePackageOverdue(@RequestParam String iccid) {
        try {
            packageEndService.InactivePackageOverdue(iccid);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @PostMapping("/userQueryPackage")
    @ApiOperation("外部API——用户套餐查询")
    public Response<UserQueryPackageReDTO> userQueryPackage(@RequestBody UserQueryPackageVO userQueryPackageVO) {
        try {
            return Response.ok(packageEndService.userQueryPackage(userQueryPackageVO));
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @PostMapping("/userQueryOrder")
    @ApiOperation("外部API——用户订单查询")
    public Response<List<UserQueryOrderDTO>> userQueryOrder(@RequestBody UserQueryOrderVO userQueryOrderVO) {
        return Response.ok(packageEndService.userQueryOrder(userQueryOrderVO));
    }

    @PostMapping("/userQueryLocaltion")
    @ApiOperation("外部API——用户位置查询")
    public Response<HimsiStatusAndLocationDTO> getUserLocation(@RequestBody HimsiStatusAndLocationVO himsiStatusAndLocationVO) {
        try {
            HimsiStatusAndLocationDTO userLocation = packageEndService.getUserLocation(himsiStatusAndLocationVO);
            userLocation.setMobileCountryCode(userLocation.getMobileCountryCode().replace("$", ""));
            return Response.ok(userLocation);
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @PostMapping("/countReuse")
    @ApiOperation("统计重复使用")
    public Response<List<ReuseVO>> countReuse(@RequestBody @Valid ReuseForm form) {
        return Response.ok(packageCardService.countReuse(form));
    }

    @PostMapping("/countReuseExport")
    @ApiOperation("统计重复使用")
    public void countReuseExport(@RequestBody @Valid ReuseForm form, HttpServletResponse response) {
        packageCardService.countReuseExport(form, response);
    }

    @GetMapping("/getPackageCardCount/{packageId}")
    public Response<Integer> getPackageCardCount(@PathVariable("packageId") String packageId) {
        return Response.ok(packageEndService.getPackageCardCount(packageId));
    }
    @PostMapping("/getNonExpiredList")
    public Response< Integer> getNonExpiredPackage(@RequestParam String iccid) {
        return Response.ok(packageEndService.getNonExpiredPackage(iccid));
    }

    @PostMapping("/getPackageUniqueId")
    @ApiOperation("获取套餐唯一id集合")
    public Response<List<String>> getPackageUniqueId(@RequestParam String iccid ,@RequestParam Boolean bak) {
        return Response.ok(packageCardService.getPackageUniqueId(iccid,bak));
    }

    @GetMapping("/IsPackageSale")
    @ApiOperation("套餐是否已售卖")
    public Response<Boolean> IsPackageSale(@RequestParam String packageId) {
        return Response.ok(packageCardService.IsPackageSale(packageId));

    }
}
