package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PmsRealnameAttr对象", description="")

public class PmsRealnameAttr implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "实名制id")
    private Long realnameId;

    @ApiModelProperty(value = "国家码")
    private String mcc;

    @ApiModelProperty(value = "国家名称中")
    private String countryCn;

    @ApiModelProperty(value = "国家名称英")
    private String countryEn;

    @ApiModelProperty(value = "国家名称繁")
    private String countryTw;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
