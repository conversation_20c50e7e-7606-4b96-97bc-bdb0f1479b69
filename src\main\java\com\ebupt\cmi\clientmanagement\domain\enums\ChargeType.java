package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 充值类型
 * <AUTHOR> yu qin
 * @Date 2022/6/6 12:16
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum ChargeType {
    /**
     * 1、缴付账单
     */
    PAYBILLS("1", "缴付账单"),
    /**
     * 2、增加押金
     */
    INCREASEDEPOSIT("2", "增加押金"),
    /**
     * 3、增加预存款
     */
    ADDDEPOSIT("3", "增加预存款"),
    /**
     * 4、酬金返还
     */
    REBATE("4", "酬金返还"),

    /**
     * 5、渠道商收入金额调账
     */
    RECONCILIATION("5", "渠道商收入金额调账"),

    /**
     * 6、A2Z押金充值
     */
    A2Z_CHARGE_DEPOSIT("6", "A2Z押金充值"),

    /**
     * 7、A2Z账单缴付
     */
    A2Z_BILL_PAYMENT("7", "A2Z账单缴付"),

    /**
     * 8、营销返利-代销
     */
    MARKETING_REBATE_DISTRIBUTION("8", "营销返利-代销"),

    /**
     * 9、营销返利-A2Z
     */
    MARKETING_REBATE_A2Z("9", "营销返利-A2Z"),

    /**
     * 10、A~Z预存款充值
     */
    A2Z_PREPAID_DEPOSIT_RECHARGE("10", "A~Z预存款充值"),

    /**
     * 11、渠道商A2Z收入金额调账
     */
    A2Z_INCOME_ADJUSTMENT("11", "渠道商A2Z收入金额调账"),

    /**
     * 12、渠道商资源合作收入金额调账
     */
    RESOURCE_INCOME_ADJUSTMENT("12", "渠道商资源合作收入金额调账"),

    /**
     * 13-atz补计费
     */
    A2Z_RE_BILL("13", "A22补计费");

    private final String value;

    private final String name;

    public static String getNameByValue(String value) {
        for (ChargeType chargeType : values()) {
            if (chargeType.value.equals(value)) {
                return chargeType.name;
            }
        }
        throw new IllegalArgumentException("充值类型枚举值错误：" + value);
    }

}
