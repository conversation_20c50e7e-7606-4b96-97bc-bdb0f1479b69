package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/17 10:44
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DirectionalAppSurfDetailDTO {
    private String type;
    private String totleFlow;
    private String appName;
    private String usedFlow;
    private String surfStatus;
    private String groupId;

    @JsonIgnore
    private BigDecimal highSpeedFlow;
}
