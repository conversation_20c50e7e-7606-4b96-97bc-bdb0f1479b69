package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/8 17:01
 */

@Getter
@AllArgsConstructor
public enum PackageType {
    /**
     * 套餐
     */
    PACKAGE("1"),
    /**
     * 终端线下卡池
     */
    TERMINAL_OFFLINE_CAEDPOOL("2"),

    /**
     * 终端线上套餐
     */
    TERMINAL_ONLINE_CAEDPOOL("5"),

    /**
     * 流量池
     */
    FLOW_POOL("3"),
    /**
     *
     */
    REFUEL("4");

    String type;
}
