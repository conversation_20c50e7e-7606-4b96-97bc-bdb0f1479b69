package com.ebupt.cmi.clientmanagement.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel
@ColumnWidth(23)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT)
public class RemunerationDetailTemplate {
    @ExcelProperty(value = "Channel Name", index = 0)
    private String corpName;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "Order ID", index = 1)
    private String orderId;

    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "ICCID", index = 2)
    private String iccid;

    @ExcelProperty(value = "Package Name", index = 3)
    private String nameEn;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "Purchase Date", index = 4)
    private LocalDateTime orderDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "Active Date", index = 5)
    private LocalDateTime activeTime;

    @ExcelProperty(value = "Type:Direct Sales/Indirect Sales", index = 6)
    @ColumnWidth(27)
    private String remunerationType;

    @ExcelProperty(value = "Quantity", index = 7)
    private Integer quantity;

    @ExcelProperty(value = "Currency", index = 8)
    private String currencyCode;

    @ExcelProperty(value = "Commission", index = 9)
    private BigDecimal remunerationAmount;

    @ExcelProperty(value = "Amount", index = 10)
    private BigDecimal amount;

    @ExcelProperty(value = "Channel", index = 11)
    private String orderChannel;
}
