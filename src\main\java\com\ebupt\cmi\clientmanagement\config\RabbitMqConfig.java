package com.ebupt.cmi.clientmanagement.config;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RabbitMqConfig.java
 * @Description RabbitMQ的配置
 * @createTime 2022年01月10日 14:59:00
 */

@Configuration
public class RabbitMqConfig {
    private final int FLOW_CALCULATE_QUEUE_DELAY_TIME = 30 * 60 * 1000;

    @Value("${welcomeMsg-delay-time}")
    private int welcomeMsgDelayTime;

    //直接消费队列 起名：flowPool.directQueue
    @Bean
    public Queue flowPoolQueue() {
        // durable:是否持久化,默认是false,持久化队列：会被存储在磁盘上，当消息代理重启时仍然存在，暂存队列：当前连接有效
        // exclusive:默认也是false，只能被当前创建的连接使用，而且当连接关闭后队列即被删除。此参考优先级高于durable
        // autoDelete:是否自动删除，当没有生产者或者消费者使用此队列，该队列会自动删除。
        //   return new Queue("TestDirectQueue",true,true,false);

        //一般设置一下队列的持久化就好,其余两个就是默认false
        return new Queue("flowPool.directQueue", true);
    }

    /**
     * 直接消费队列  起名：singleCard.reached.queue
     *
     * @return Queue
     */
    @Bean
    public Queue reachingTreatmentQueue() {
        return new Queue(QueueEnum.ReachingTreatment.getName(), true);
    }

    /**
     * 单日恢复延时队列 singleDayResume.delay.queue
     */
    @Bean
    public Queue resumeDelayQueue() {
        return new Queue(QueueEnum.ResumeDelayQueue.getName());
    }

    /**
     * 套餐超时激活延时队列 packageExpire.delay.queue
     */
    @Bean
    public Queue packageExpireDelayQueue() {
        return new Queue(QueueEnum.PackageExpireDelayQueue.getName());
    }

    /**
     * 实名制延时认证通过队列 realNameAuthentication.expire.queue
     */
    @Bean
    public Queue realNameAuthenticationExpireQueue() {
        return new Queue(QueueEnum.RealNameAuthenticationExpireQueue.getName());
    }

    /**
     * Lu订单超时未响应取消队列
     */
    @Bean
    public Queue luOrderCancelQueue() {
        return new Queue(QueueEnum.LuOrderCancelDelayQueue.getName());
    }

    @Bean
    public Queue upccSignQueue() {
        return new Queue(QueueEnum.UpccSignQueue.getName());
    }

    @Bean
    public DirectExchange upccSignExchange() {
        return new DirectExchange(QueueEnum.UpccSignQueue.getExchangeName(), true, false);
    }


    /**
     * CDR延期队列
     */
    @Bean
    Queue cdrProcessDelayQueue() {
        return new Queue(QueueEnum.CDRProcessDelayQueue.getName());
    }

    @Bean
    public Queue packageStopQueue(){
        return new Queue(QueueEnum.PackageStopQueue.getName());
    }

    @Bean
    public Queue packageRecoverQueue(){
        return new Queue(QueueEnum.PackageRecoverQueue.getName());
    }

    @Bean
    public Queue directionalAppTemplateDelQueue(){
        return new Queue(QueueEnum.DIRECTIONALAPPTEMPLATEDEL.getName());
    }

    @Bean
    public Queue malfunctionDealQueue(){
        return new Queue(QueueEnum.MalfunctionDealQueue.getName());
    }

    @Bean
    public DirectExchange malfunctionDealExchange() {
        return new DirectExchange(QueueEnum.MalfunctionDealQueue.getExchangeName(), true, false);
    }

    @Bean
    public Binding bindmalfunctionDeal(){
        return BindingBuilder.bind(malfunctionDealQueue()).to(malfunctionDealExchange())
                .with(QueueEnum.MalfunctionDealQueue.getRoutingKey());
    }

    @Bean
    public DirectExchange directionalAppTemplateDelExchange() {
        return new DirectExchange(QueueEnum.DIRECTIONALAPPTEMPLATEDEL.getExchangeName(), true, false);
    }

    @Bean
    public Binding bindDirectionalAppTemplateDel(){
        return BindingBuilder.bind(directionalAppTemplateDelQueue()).to(directionalAppTemplateDelExchange())
                .with(QueueEnum.DIRECTIONALAPPTEMPLATEDEL.getRoutingKey());
    }


    /**
     * CDR延期队列交换机
     *
     * @return CustomExchange
     */
    @Bean
    CustomExchange cdrProcessDelayExchange() {
        //创建一个自定义交换机，可以发送延迟消息
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(QueueEnum.CDRProcessDelayQueue.getExchangeName(),
                "x-delayed-message", true, false, args);
    }

    @Bean
    Binding cdrProcessDelayBind() {
        return BindingBuilder
                .bind(cdrProcessDelayQueue())
                .to(cdrProcessDelayExchange())
                .with(QueueEnum.CDRProcessDelayQueue.getRoutingKey()).noargs();
    }

    /**
     * LU订单取消交换机
     *
     * @return {@link CustomExchange}
     */
    @Bean
    public CustomExchange luOrderCancelExchange() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(QueueEnum.LuOrderCancelDelayQueue.getExchangeName(), "x-delayed-message", true, false, args);
    }
    @Bean
    public DirectExchange packageStopExchange() {
        return new DirectExchange(QueueEnum.PackageStopQueue.getExchangeName(), true, false);
    }


    @Bean
    public DirectExchange packageRecoverExchange() {
        return new DirectExchange(QueueEnum.PackageRecoverQueue.getExchangeName(), true, false);
    }
    /**
     * lu队列和交换机绑定
     *
     * @return
     */
    @Bean
    Binding luBind() {
        return BindingBuilder
                .bind(luOrderCancelQueue())
                .to(luOrderCancelExchange())
                .with(QueueEnum.LuOrderCancelDelayQueue.getRoutingKey()).noargs();
    }

    @Bean
    public Binding bind(){
        return BindingBuilder.bind(packageStopQueue()).to(packageStopExchange()).with(QueueEnum.PackageStopQueue.getRoutingKey());
    }

    @Bean
    public Binding bindY(){
        return BindingBuilder.bind(upccSignQueue()).to(upccSignExchange()).with(QueueEnum.UpccSignQueue.getRoutingKey());
    }

    @Bean
    public Binding bindZ(){
        return BindingBuilder.bind(packageRecoverQueue()).to(packageRecoverExchange()).with(QueueEnum.PackageRecoverQueue.getRoutingKey());
    }



    @Bean
    DirectExchange reachingTreatmentExchange() {
        return new DirectExchange(QueueEnum.ReachingTreatment.getExchangeName(), true, false);
    }

    /**
     * 单日恢复延时队列交换机
     *
     * @return CustomExchange
     */
    @Bean
    CustomExchange resumeDelayExchange() {
        //创建一个自定义交换机，可以发送延迟消息
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(QueueEnum.ResumeDelayQueue.getExchangeName(),
                "x-delayed-message", true, false, args);
    }

    /**
     * 套餐超时激活延时队列交换机
     *
     * @return CustomExchange
     */
    @Bean
    CustomExchange packageExpireDelayExchange() {
        //创建一个自定义交换机，可以发送延迟消息
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(QueueEnum.PackageExpireDelayQueue.getExchangeName(),
                "x-delayed-message", true, false, args);
    }


    /**
     * 实名制延时认证通过队列交换机
     *
     * @return CustomExchange
     */
    @Bean
    CustomExchange realNameAuthenticationExchange() {
        //创建一个自定义交换机，可以发送延迟消息
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(QueueEnum.RealNameAuthenticationExpireQueue.getExchangeName(),
                "x-delayed-message", true, false, args);
    }


    /**
     * Direct交换机 起名：flowPool.directExchange
     */
    @Bean
    DirectExchange flowPoolDirectExchange() {
        //  return new DirectExchange("TestDirectExchange",true,true);
        return new DirectExchange("flowPool.directExchange", true, false);
    }

    //绑定  将队列和交换机绑定, 并设置用于匹配键：flowPoolDirectRouting
    @Bean
    Binding bindingDirect() {
        return BindingBuilder.bind(flowPoolQueue()).to(flowPoolDirectExchange()).with("flowPoolDirectRouting");
    }

    @Bean
    Binding bindingReachingTreatment() {
        return BindingBuilder
                .bind(reachingTreatmentQueue())
                .to(reachingTreatmentExchange())
                .with(QueueEnum.ReachingTreatment.getRoutingKey());
    }

    /**
     * 将套餐超时激活延时队列绑定到交换机
     */
    @Bean
    Binding packageExpireDelayBinding() {
        return BindingBuilder
                .bind(packageExpireDelayQueue())
                .to(packageExpireDelayExchange())
                .with(QueueEnum.PackageExpireDelayQueue.getRoutingKey()).noargs();
    }

    /**
     * 将实名制延时认证通过队列绑定到交换机
     */
    @Bean
    Binding realNameAuthenticationExpire() {
        return BindingBuilder
                .bind(realNameAuthenticationExpireQueue())
                .to(realNameAuthenticationExchange())
                .with(QueueEnum.RealNameAuthenticationExpireQueue.getRoutingKey()).noargs();
    }

    /**
     * 将单日恢复绑定到交换机
     */
    @Bean
    Binding resumeDelayBinding() {
        return BindingBuilder
                .bind(resumeDelayQueue())
                .to(resumeDelayExchange())
                .with(QueueEnum.ResumeDelayQueue.getRoutingKey()).noargs();
    }

    //流量计算放入队列
    @Bean
    public Queue flowCalculateDelayQueue() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-message-ttl", FLOW_CALCULATE_QUEUE_DELAY_TIME);
        arguments.put("x-dead-letter-exchange", "flowPool.flowCalculateExchange");
        return new Queue("flowPool.FlowCalculateDelayQueue", true, false, false, arguments);
    }

    @Bean
    public Queue flowCalculateQueue() {
        Map<String, Object> arguments = new HashMap<>();
        return new Queue("flowPool.FlowCalculateQueue", true, false, false, arguments);
    }

    @Bean
    DirectExchange flowCalculateExchange() {
        return new DirectExchange("flowPool.flowCalculateExchange", true, false);
    }

    @Bean
    Binding bindFlowCalculateQueue() {
        return BindingBuilder.bind(flowCalculateQueue()).to(flowCalculateExchange()).with("flowPool.FlowCalculateDelayQueue");
    }

    @Bean
    public Queue welcomeMsgDelayQueue() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-message-ttl", welcomeMsgDelayTime * 60 * 1000);
        arguments.put("x-dead-letter-exchange", "sms.welcomeMsgQueueExchange");
        return new Queue("sms.welcomeMsgDelayQueue", true, false, false, arguments);
    }

    @Bean
    public Queue welcomeMsgQueue() {
        Map<String, Object> arguments = new HashMap<>();
        return new Queue("sms.welcomeMsgQueue", true, false, false, arguments);
    }

    @Bean
    public DirectExchange welcomeMsgQueueExchange() {
        return new DirectExchange("sms.welcomeMsgQueueExchange", true, false);
    }

    @Bean
    Binding bindWelcomeMsgQueue() {
        return BindingBuilder.bind(welcomeMsgQueue()).to(welcomeMsgQueueExchange()).with("sms.welcomeMsgDelayQueue");
    }

    /**
     * Lu订单超时未响应取消队列
     */
    @Bean
    public Queue localTestQueue() {
        return new Queue("localTestQueue");
    }

    @Bean
    public CustomExchange localTestExchange() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange("localTestExchange", "x-delayed-message", true, false, args);
    }

    @Bean
    Binding bindTest() {
        return BindingBuilder
                .bind(localTestQueue())
                .to(localTestExchange())
                .with("localTestRouter").noargs();
    }



}
