package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/25 10:34
 */
@Data
@Component
@ConfigurationProperties("record-detail")
public class RecordDetailConfig {
    private List<Sftp> sftp;

    private Sftp uploadPlatformSftp;

    private String localstoragePath;

    private String uploadRemotePath;

    private String decompressPath;

    private Integer doPackage;

    private Integer batchInsertSize;

    private Integer batchQuerySize;

    private Integer batchDeleteSize;

    private Integer maxLineNum;

    private Integer maxThreadNum;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Sftp extends SftpConfig{
        private Sftp sftp;
        private List<String> remotePath;
    }
}