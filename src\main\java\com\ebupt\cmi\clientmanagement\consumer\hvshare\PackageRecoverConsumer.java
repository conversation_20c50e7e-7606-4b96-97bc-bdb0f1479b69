package com.ebupt.cmi.clientmanagement.consumer.hvshare;


import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.ContextUtil;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packageSuspendContext.SuspendAndRecoverContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.factory.HVShareStrategyFactory;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SuspendAndRecoverVO;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

@Component
@RabbitListener(queues = "package.recover.queue")
@Slf4j
public class PackageRecoverConsumer {

    @Resource
    private HVShareStrategyFactory hvShareStrategyFactory;

    @Resource
    private CommonRepository commonRepository;

    @RabbitHandler
    public void process(String messageString, Channel channel, Message message) throws IOException, InterruptedException {

        log.info("rabbitMQ卡恢复队列收到消息： {}", messageString);

        SuspendAndRecoverVO suspendAndRecoverVO = JSON.parseObject(messageString, SuspendAndRecoverVO.class);

        AbstractStrategy strategy = hvShareStrategyFactory.getStrategy("packageRecoverStrategy");

        SuspendAndRecoverContext context = (SuspendAndRecoverContext) ContextUtil.getContext(SuspendAndRecoverContext.class);

        try {

            context.setMessageVO(suspendAndRecoverVO);

            context.setQueueEnum(QueueEnum.PackageRecoverQueue);

            context.setCropId(suspendAndRecoverVO.getCropId());

            try {
                strategy.handle(context);

                strategy.callBack(context);

            } catch (Exception e) {

                log.error("", e);

                commonRepository.insertErrorLog(context,
                        e.getClass().getName() + " " + e.getMessage());
            }
        } finally {

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }


    }


}
