package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelOrder;
import com.ebupt.cmi.clientmanagement.domain.req.CompensationReq;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.CreateOrderResp;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.CreateOrderReq;
import com.ebupt.cmi.clientmanagement.service.ChannelOrderService;
import com.ebupt.cmi.clientmanagement.service.OrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("order")
@AllArgsConstructor
@Api(tags = "订单相关接口")
public class OrderController {

    private final OrderService orderService;

    private final ChannelOrderService channelOrderService;

    @ApiOperation("订单同步[外部API]")
    @PostMapping("create")
    public Response<CreateOrderResp> createOrder(@RequestBody @Valid CreateOrderReq createOrderReq) {
        log.debug("createOrder: {}", createOrderReq);
        CreateOrderResp createOrderResp = orderService.createOrder(createOrderReq);
        if (createOrderResp.getIsRepeat()){
            return Response.of(ApiResponseEnum.REPEAT_CODE,createOrderResp);
        }
        return Response.ok(createOrderResp);
    }

    @ApiOperation("订单同步大单补偿接口")
    @PostMapping("/compensation")
    public Response<Void> compensationOrder(@RequestBody @Valid CompensationReq compensationReq) {
        log.debug("createOrder: {}", compensationReq);
        orderService.compensationOrder(compensationReq);
        return Response.ok();
    }

    @ApiOperation("查询是否有订单记录，供PMS调用")
    @PostMapping("/selectHasOrder")
    public Response<Boolean> selectHasOrder(@RequestParam("id") String id) {
        return Response.ok(channelOrderService.selectHasOrder(id));
    }

    @GetMapping("getOrderIdByUniqueId")
    public Response<Long> getOrderIdByUniqueId(@RequestParam String orderUniqueId) {
        return Response.ok(orderService.getOrderByUniqueId(orderUniqueId));
    }
}
