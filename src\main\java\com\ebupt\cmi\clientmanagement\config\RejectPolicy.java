package com.ebupt.cmi.clientmanagement.config;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2021-5-17 12:13:37
 * @description 线程池任务拒绝策略枚举
 */
public enum RejectPolicy {

    ABORT(new ThreadPoolExecutor.AbortPolicy()),
    DISCARD(new ThreadPoolExecutor.DiscardPolicy()),
    DISCARD_OLDEST(new ThreadPoolExecutor.DiscardOldestPolicy()),
    CALLER_RUNS(new ThreadPoolExecutor.CallerRunsPolicy());

    private final RejectedExecutionHandler value;

    private RejectPolicy(RejectedExecutionHandler handler) {
        this.value = handler;
    }

    public RejectedExecutionHandler getValue() {
        return this.value;
    }
}
