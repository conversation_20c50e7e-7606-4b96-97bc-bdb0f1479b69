package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.net.URLDecoder;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.dto.PersonalOrderDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.NotifyTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.UnsubscribeChannelEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.OrderTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.req.AuditOrderReq;
import com.ebupt.cmi.clientmanagement.domain.req.BigOrderDeliverVO;
import com.ebupt.cmi.clientmanagement.domain.req.ChannelUnsubscribeReq;
import com.ebupt.cmi.clientmanagement.domain.req.SearchOrderDetailReq;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.validation.CommonGroup;
import com.ebupt.cmi.clientmanagement.domain.vo.H5UnsubscribeReq;
import com.ebupt.cmi.clientmanagement.domain.vo.PersonalOrderDeliverVO;
import com.ebupt.cmi.clientmanagement.domain.vo.SysSystemAlerts;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.ChannelOrderDetailVO;
import com.ebupt.cmi.clientmanagement.domain.vo.personalorder.PersonalOrderQueryVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.mapper.ChannelOrderDetailMapper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelOrderMapper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelPackageCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsBigOrderMapper;
import com.ebupt.cmi.clientmanagement.service.IPersonalOrderService;
import com.ebupt.cmi.clientmanagement.service.PersonalOrderService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 个人订单相关接口
 * @date 2021/6/15 11:03
 */
@AllArgsConstructor
@RequestMapping("/api/v1/personalOrder")
@RestController
@Api(tags = "个人订单相关接口")
@Slf4j
public class PersonalOrderController {

    private final PersonalOrderService orderService;
    private final IPersonalOrderService personalOrderService;

    private final ChannelOrderMapper channelOrderMapper;

    private final CmsBigOrderMapper cmsBigOrderMapper;

    private final ChannelOrderDetailMapper channelOrderDetailMapper;

    private final ChannelPackageCardMapper channelPackageCardMapper;

    private final RedissonLock redissonLock;

    @ApiOperation(value = "个人订单分页查询接口")
    @GetMapping("/pages")
    public Response<PageResult<PersonalOrderDTO>> getOrderPages(@Validated(value = CommonGroup.class) PersonalOrderQueryVO orderQueryVO,
                                                                @RequestHeader("userName") String name) {
        name = URLDecoder.decode(name, StandardCharsets.UTF_8);
        orderQueryVO.setName(name);
        return Response.ok(personalOrderService.getOrderPages(orderQueryVO));
    }

    @ApiOperation(value = "个人订单查询导出")
    @GetMapping("/pages/export")
    @OperationLog(operationName = "产品运营-个人订单管理-导出", operationType = OperationTypeEnum.EXPORT)
    public void download(@Validated(value = CommonGroup.class) PersonalOrderQueryVO orderQueryVO, HttpServletResponse response,
                         @RequestHeader("userName") String name) {
        name = URLDecoder.decode(name, StandardCharsets.UTF_8);
        orderQueryVO.setName(name);
        personalOrderService.downloadOrderSearchData(orderQueryVO, response);
    }

    @ApiOperation(value = "查询渠道商套餐购买记录接口")
    @GetMapping("/getPackagePurchaseRecord")
    public Response<PageResult<PersonalOrderDTO>> getPackagePurchaseRecord(@Validated(value = CommonGroup.class) PersonalOrderQueryVO orderQueryVO) {
        return Response.ok(personalOrderService.getPackagePurchaseRecord(orderQueryVO));
    }

    @ApiImplicitParam(name = "id", value = "订单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation(value = "订单发货接口")
    @PostMapping("/deliver/{orderId}")
    @OperationLog(operationName = "个人订单——订单发货", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> deliverOrder(@PathVariable String orderId, @RequestBody @Validated PersonalOrderDeliverVO personalOrderDeliverVO) {
        personalOrderService.deliverOrder(Long.valueOf(orderId), personalOrderDeliverVO);
        return Response.ok();
    }

    @ApiImplicitParam(name = "file", value = "发货文件", paramType = "query", dataType = "file", required = true)
    @ApiOperation(value = "批量发货接口")
    @PostMapping("/deliver/batch")
    @OperationLog(operationName = "个人订单——订单批量发货", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> batchDeliverOrder(@RequestPart MultipartFile file) {
        personalOrderService.batchDeliverOrder(file);
        return Response.ok();
    }

    @ApiOperation(value = "大单发货接口")
    @PostMapping("/bigOrder/deliver")
    @OperationLog(operationName = "个人订单——大单发货", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> deliverOrder(BigOrderDeliverVO vo) {
        MultipartFile file = vo.getFile();
        personalOrderService.deliverBigOrder(vo, file);
        return Response.ok();
    }

    @ApiImplicitParam(name = "id", value = "子单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation(value = "子单发货接口")
    @PostMapping("/deliver/child/{id}")
    @OperationLog(operationName = "个人订单——订单子单发货", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> deliverChildOrder(@PathVariable String id, @RequestBody @Validated PersonalOrderDeliverVO personalOrderDeliverVO) {
        personalOrderService.deliverChildOrder(Long.valueOf(id), personalOrderDeliverVO);
        return Response.ok();
    }

    @ApiImplicitParam(name = "id", value = "订单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation(value = "订单全部解绑接口")
    @PostMapping("/unbind/{orderId}")
    @OperationLog(operationName = "个人订单——订单全部解绑", operationType = OperationTypeEnum.DELETE)
    public Response<Void> unbindOrder(@PathVariable String orderId) {
        personalOrderService.unbindOrder(Long.valueOf(orderId));
        return Response.ok();
    }

    @ApiImplicitParam(name = "id", value = "订单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation(value = "大订单解绑接口")
    @PostMapping("/big/unbind/{orderId}")
    @OperationLog(operationName = "个人订单——大订单解绑", operationType = OperationTypeEnum.DELETE)
    public Response<Void> unbindBigOrder(@PathVariable String orderId) {
        personalOrderService.unbindBigOrder(Long.valueOf(orderId));
        return Response.ok();
    }

    @ApiImplicitParam(name = "id", value = "子单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation(value = "子单解绑接口")
    @PostMapping("/unbind/child/{id}")
    @OperationLog(operationName = "个人订单——子单解绑", operationType = OperationTypeEnum.DELETE)
    public Response<Void> unbindChildOrder(@PathVariable String id) {
        personalOrderService.unbindChildOrder(Long.valueOf(id));
        return Response.ok();
    }


    @ApiOperation("根据订单id查询子订单列表")
    @GetMapping("/orderDetailsByOrderId")
    public Response<PageResult<ChannelOrderDetailVO>> getOrderDetailPage(
            @Validated(CommonGroup.class) SearchOrderDetailReq searchOrderDetailReq, @RequestHeader("userName") String name) {
        name = URLDecoder.decode(name, StandardCharsets.UTF_8);
        searchOrderDetailReq.setUsername(name);
        return Response.ok(orderService.getOrderDetailPage(searchOrderDetailReq));
    }

    @ApiImplicitParam(name = "id", value = "订单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation("订单-全部退订")
    @PostMapping("/unsubscribe/{id}")
    @OperationLog(operationName = "个人订单——全部退订", operationType = OperationTypeEnum.DELETE)
    public Response<Void> unsubscribeOrder(@PathVariable String id) {
        orderService.unsubscribeOrder(Long.valueOf(id));
        return Response.ok();
    }

    @ApiImplicitParam(name = "id", value = "子订单id", paramType = "path", dataType = "long", required = true)
    @ApiOperation("子订单退订")
    @PostMapping("/detail/unsubscribe/{id}")
    @OperationLog(operationName = "个人订单——子订单退订", operationType = OperationTypeEnum.DELETE)
    public Response<Void> unsubscribeOrderDetail(@PathVariable String id) {
        orderService.unsubscribeOrderDetail(Long.valueOf(id));
        return Response.ok();
    }

    @OperationLog(operationName = "个人订单管理-总订单审核", operationType = OperationTypeEnum.AUDIT)
    @ApiOperation("审核总订单")
    @PostMapping("/audit")
    public Response<Void> auditOrder(@RequestBody @Valid AuditOrderReq auditOrderReq) {
        orderService.auditOrder(auditOrderReq);
        return Response.ok();
    }

    @OperationLog(operationName = "个人订单管理-子订单审核", operationType = OperationTypeEnum.AUDIT)
    @ApiOperation("审核子订单")
    @PostMapping("/detail/audit")
    public Response<Void> auditOrderDetail(@RequestBody @Valid AuditOrderReq auditOrderReq) {
        orderService.auditOrderDetail(auditOrderReq);
        return Response.ok();
    }

    @ApiOperation("h5订单详情")
    @GetMapping("/detail/orderDetail")
    public Response orderDetail(@RequestParam String orderId) {
        try {
            return Response.ok(personalOrderService.queryH5OrderDetail(orderId).getObj());
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }


    @ApiOperation("订单-全部退订[外部API]")
    @PostMapping("/unsubscribeForApi")
    public Response<Void> unsubscribeOrderForApi(@RequestBody ChannelUnsubscribeReq channelUnsubscribeReq) {
        return orderService.unsubscribeOrderForApi(channelUnsubscribeReq);
    }

    @ApiOperation("个人订单管理-大单退订")
    @PostMapping("/unsubscribeForBigOrder")
    @OperationLog(operationName = "个人订单管理-大单退订", operationType = OperationTypeEnum.DELETE)
    public Response<Void> unsubscribeForBigOrder(@RequestBody H5UnsubscribeReq h5UnsubscribeReq) {
        ChannelOrder channelOrder = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                .eq(ChannelOrder::getOrderUniqueId, h5UnsubscribeReq.getOrderId()));
        boolean lock = redissonLock.tryLock(channelOrder.getId().toString());
        if (!lock) {
            throw new BizException(ApiResponseEnum.ACQUIRE_LOCK_FAILED);
        }
        try {
            checkOrder(channelOrder, h5UnsubscribeReq);
            checkBigOrder(channelOrder);
//            orderService.updateChannelOrderStatus(channelOrder.getOrderUniqueId());
            orderService.unsubscribeForBigOrder(h5UnsubscribeReq, channelOrder);
            return Response.ok();
        } finally {
            if (redissonLock.isHeldByCurrentThread(channelOrder.getId().toString())) {
                redissonLock.unlock(channelOrder.getId().toString());
            }
        }
    }

    @ApiOperation("订单-部分退订[H5]")
    @PostMapping("/unsubscribeForH5")
    public Response<Void> unsubscribeForH5(@RequestBody H5UnsubscribeReq h5UnsubscribeReq) {
        ChannelOrderDetail orderDetail = Optional.ofNullable(channelOrderDetailMapper.selectOne(Wrappers.lambdaQuery(ChannelOrderDetail.class)
                .eq(ChannelOrderDetail::getId, h5UnsubscribeReq.getChildOrderId()))).orElseThrow(() -> new BizException(ApiResponseEnum.ORDER_NOT_FOUND));
        ChannelOrder channelOrder = Optional.ofNullable(channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                .eq(ChannelOrder::getId, orderDetail.getOrderId()))).orElseThrow(() -> new BizException(ApiResponseEnum.ORDER_NOT_FOUND));
        boolean lock = redissonLock.tryLock(channelOrder.getId().toString());
        if (!lock) {
            throw new BizException(ApiResponseEnum.ACQUIRE_LOCK_FAILED);
        }
        try {
            checkOrder(channelOrder, h5UnsubscribeReq);
            checkOrderDetail(orderDetail);
            if (judgeBigOrder(channelOrder.getOrderUniqueId())) {
                checkBigOrder(channelOrder);
            }
            orderService.unsubscribeForSmallOrder(h5UnsubscribeReq, channelOrder, Collections.singletonList(orderDetail));
        } finally {
            if (redissonLock.isHeldByCurrentThread(channelOrder.getId().toString())) {
                redissonLock.unlock(channelOrder.getId().toString());
            }
        }
        return Response.okForApi("0000000");
    }

    private void checkOrderDetail(ChannelOrderDetail orderDetail) {
        String orderStatus = orderDetail.getOrderStatus();
        final List<String> orderStatusList = Arrays.asList("1", "2");
        if (!orderStatusList.contains(orderStatus)) {
            log.warn("订单状态：{}不能退订", orderStatus);
            throw new BizException(ApiResponseEnum.ORDER_STATUS_ERROR_2);
        }
        if ("2".equals(orderStatus)) {
            ChannelPackageCard channelPackageCard = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                    .eq(ChannelPackageCard::getPackageUniqueId, orderDetail.getPackageUniqueId()));
            if (!PackageStatusEnum.UNACTIVATED.getStatus().equals(channelPackageCard.getPackageStatus())) {
                throw new BizException(ApiResponseEnum.PACKAGE_NOT_WAITACTIVED);
            }
        }
    }

    public boolean judgeBigOrder(String orderUniqueId) {
        return cmsBigOrderMapper.selectOne(Wrappers.lambdaQuery(CmsBigOrder.class)
                .eq(CmsBigOrder::getOrderUniqueId, orderUniqueId)) != null;
    }

    public void checkOrder(ChannelOrder channelOrder, H5UnsubscribeReq h5UnsubscribeReq) {
        final String orderType = channelOrder.getOrderType();
        log.debug("当前订单orderType: {}", orderType);
        final List<String> orderTypes = Arrays.asList("2", "3");
        if (!orderTypes.contains(orderType)) {
            log.warn("订单类型：{}不能退订", orderType);
            throw new BizException(ApiResponseEnum.ORDER_TYPE_ERROR_2);
        }
        String orderStatus = channelOrder.getOrderStatus();
        if (ChannelOrder.OrderStatusEnum.UNSUBSCRIBE.getValue().equals(orderStatus)) {
            log.warn("orderStatus: {}", orderStatus);
            throw new BizException(ApiResponseEnum.ORDER_STATUS_ERROR_2);
        }
        if (StringUtils.isNotBlank(h5UnsubscribeReq.getUserId()) && !channelOrder.getOrderUserId().equals(h5UnsubscribeReq.getUserId())) {
            throw new BizException(ApiResponseEnum.USER_PERMISSION_ERROR);
        }
    }

    public void checkBigOrder(ChannelOrder channelOrder) {
        if (!ChannelOrder.OrderStatusEnum.NOT_DELIVERY.getValue().equals(channelOrder.getOrderStatus())
                && !ChannelOrder.OrderStatusEnum.FINISHED.getValue().equals(channelOrder.getOrderStatus())
                && !ChannelOrder.OrderStatusEnum.PARTIAL_DELIVERED.getValue().equals(channelOrder.getOrderStatus())
                && !ChannelOrder.OrderStatusEnum.UNSUBSCRIBE_NOT_AUDIT.getValue().equals(channelOrder.getOrderStatus())
                && !ChannelOrder.OrderStatusEnum.COMBINATION.getValue().equals(channelOrder.getOrderStatus())
                && !ChannelOrder.OrderStatusEnum.PARTIAL_UNSUBSCRIBE.getValue().equals(channelOrder.getOrderStatus())) {
            throw new BizException(ApiResponseEnum.ORDER_STATUS_ERROR_3);
        }
        if (cmsBigOrderMapper.selectOne(Wrappers.lambdaQuery(CmsBigOrder.class)
                .eq(CmsBigOrder::getOrderUniqueId, channelOrder.getOrderUniqueId())) == null) {
            throw new BizException("大单不存在，退订失败");
        }
    }
}
