package com.ebupt.cmi.clientmanagement.domain.enums;

import com.ebupt.cmi.clientmanagement.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum CurrentRateType {
    /**
     * 正常
     */
    NORMAL("1", "正常"),

    /**
     * 单卡周期达量限速
     */
    CARD_CYCLE_REACH_LIMIT("2", "单卡周期达量限速"),

    /**
     * 单卡周期达量停用
     */
    CARD_CYCLE_REACH_STOP("3", "单卡周期达量停用"),

    /**
     * 单卡总量达量限速
     */
    CARD_TOTAL_REACH_LIMIT("4", "单卡总量达量限速"),

    /**
     * 单卡总量达量停用
     */
    CARD_TOTAL_REACH_STOP("5", "单卡总量达量停用"),

    /**
     * 流量池上限达量限速
     */
    FLOW_POOL_REACH_LIMIT("6", "流量池上限达量限速"),

    /**
     * 流量池上限达量停用
     */
    FLOW_POOL_REACH_STOP("7", "流量池上限达量停用"),

    /**
     * 单卡单周期恢复
     */
    CARD_CYCLE_RESUME("8", "单卡单周期恢复"),

    /**
     * 流量池暂停使用
     */
    FLOW_POOL_PAUSE_USE("9", "流量池暂停使用");

    String type;

    String desc;

    public static String getDesc(String type) {
        for (CurrentRateType c : CurrentRateType.values()) {
            if (c.type.equals(type)) {
                return c.desc;
            }
        }
        throw new BizException("类型错误");
    }

    public static String getStatus(String type) {
        if (CurrentRateType.NORMAL.type.equals(type)) {
            return getDesc(type);
        } else {
            String desc = getDesc(type);
            return desc.substring(desc.length() - 2);
        }
    }


}
