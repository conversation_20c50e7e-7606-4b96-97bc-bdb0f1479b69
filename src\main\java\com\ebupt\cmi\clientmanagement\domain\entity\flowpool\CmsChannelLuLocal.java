package com.ebupt.cmi.clientmanagement.domain.entity.flowpool;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmsChannelLuLocal.java
 * @Description 客户lu上报模拟记录表
 * @createTime 2022年01月17日 16:53:00
 */

@TableName("cms_channel_lu_local")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsChannelLuLocal {
    Long id;

    String imsi;

    String iccid;

    String msisdn;

    /**
     * 上报类型
     * 1：H
     * 2：V
     */
    String reportType;

    /**
     * 卡片形态,当report_type=1时不为空
     * 1：普通卡（实体卡）
     * 2：Esim卡
     * 3：贴片卡
     */
    String cardForm;

    /**
     * 上报时间
     */
    LocalDateTime reportTime;

    String mcc;

    /**
     * 激活类型
     * 1：自动激活
     * 2：手动激活
     */
    String activeType;

    String himsi;

    /**
     * 套餐唯一id，uuid
     * 流量池唯一Id
     */
    String packageUniqueId;
}
