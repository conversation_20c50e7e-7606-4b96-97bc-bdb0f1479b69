package com.ebupt.cmi.clientmanagement.config;

import com.ebupt.elk.feign.FeignLogger;
import feign.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignLoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignConfig {
    @Bean
    public Logger.Level feignLoggerLeavel(){
        return Logger.Level.FULL;

    }
    @Bean
    FeignLoggerFactory MyFeignLoggerFactory() {
        return new MyFeignLoggerFactory();
    }
    /**
     * feign info 日志工厂
     */
    public static class MyFeignLoggerFactory implements FeignLoggerFactory {

        @Override
        public Logger create(Class<?> type) {
            return new FeignLogger(LoggerFactory.getLogger(type));
        }
    }
}
