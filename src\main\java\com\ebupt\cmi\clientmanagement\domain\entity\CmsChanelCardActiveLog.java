package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmsChanelCardActiveLog.java
 * @Description cms_chanel_card_active_log 合作商激活日志表
 * @createTime 2021年06月30日 15:26:00
 */
@TableName("cms_chanel_card_active_log")
@Data
@ToString
@Builder
public class CmsChanelCardActiveLog {
    @TableId
    Long id;

    String imsi;

    String iccid;

    String mcc;

    LocalDateTime netTime;

    LocalDateTime createTime;
}
