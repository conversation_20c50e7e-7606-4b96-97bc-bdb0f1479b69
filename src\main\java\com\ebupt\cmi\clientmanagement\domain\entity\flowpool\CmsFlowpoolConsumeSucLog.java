package com.ebupt.cmi.clientmanagement.domain.entity.flowpool;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmsFlowpoolConsumeSucLog.java
 * @Description cms_flowpool_consume_suc_log 实体
 * @createTime 2022年01月13日 15:00:00
 */

@TableName("cms_flowpool_consume_suc_log")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsFlowpoolConsumeSucLog {
    @TableId
    Long id;

    String imsi;

    String iccid;

    String msisdn;
    /**
     * 上网方式1：H，2：V
     */
    String internetType;
    /**
     * 流量池唯一ID
     */
    String flowPoolUniqueId;
    /**
     * 处理结果
     * 1、成功
     * 2、失败
     */
    String result;

    String errorDesc;
    /**
     * 业务类型
     * 1：正常2：单卡周期达量限速 3：单卡周期达量停用4：单卡总量达量限速5：单卡总量达量停用，6、流量池达量限速 7：流量池达量停用
     */
    String businessType;
}
