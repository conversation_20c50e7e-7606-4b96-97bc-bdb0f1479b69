package com.ebupt.cmi.clientmanagement.domain.dto.channel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@ApiModel
public class SubCnlDTO {

    @ApiModelProperty(value = "子渠道商名称")
    private String subChannelName;

    @ApiModelProperty(value = "联系人邮箱")
    private String email;

    @ApiModelProperty(value = "利润率")
    private Integer profitMargin;

    @ApiModelProperty(value = "利润率")
    private Integer refuelProfitMargin;

    @ApiModelProperty(value = "账号权限")
    private String accountPermissions;

    @ApiModelProperty(value = "可用额度")
    private BigDecimal deposit;

    @ApiModelProperty(value = "总额度")
    private BigDecimal totalDeposit;

    @ApiModelProperty(value = "父渠道商id")
    private String pCorpId;

    @ApiModelProperty(value = "子渠道商id")
    private String cCorpId;

    @ApiModelProperty(value = "appSecret")
    private String appSecret;

    @ApiModelProperty(value = "appKey")
    private String appKey;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
