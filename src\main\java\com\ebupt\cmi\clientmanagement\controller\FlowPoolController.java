package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.util.StrUtil;
import com.ebupt.cmi.clientmanagement.domain.dto.FlowpoolCardListDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.FlowpoolListDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.ImportPackageDto;
import com.ebupt.cmi.clientmanagement.domain.dto.SmsGetFlowPoolDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channel.AuthDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelDistributorDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsFlowpoolIccidimportTask;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsFlowpoolInfo;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.mapper.IccidUsedFlowMapper;
import com.ebupt.cmi.clientmanagement.service.FlowPoolService;
import com.ebupt.cmi.clientmanagement.service.ICmsFlowpoolInfoService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/29 15:19
 */

@Api(tags = "流量池相关接口")
@RestController
@RequestMapping("/flowPool")
public class FlowPoolController {

    @Resource
    private ICmsFlowpoolInfoService cmsFlowpoolInfoService;

    @Resource
    private FlowPoolService flowPoolService;

    @Autowired
    IccidUsedFlowMapper iccidUsedFlowMapper;

    @PostMapping("/addFlowpool")
    @ApiOperation(value = "流量池新增接口", notes = "流量池新增")
    @OperationLog(operationName = "流量池管理——增加流量池", operationType = OperationTypeEnum.ADD)
    public Response addFlowPool(@Valid @RequestBody AddFlowpoolVO addFlowpoolVO) {
        cmsFlowpoolInfoService.addFlowPool(addFlowpoolVO);
        return Response.ok();
    }

    @PostMapping("/flowpoolAuth")
    @ApiOperation(value = "流量池审核接口", notes = "流量池审核")
    @OperationLog(operationName = "流量池管理——流量池审核", operationType = OperationTypeEnum.AUDIT)
    public Response flowPoolAuth(@RequestParam String flowPoolId, @RequestParam boolean authStatus) {
        cmsFlowpoolInfoService.flowPoolAuth(flowPoolId, authStatus);
        return Response.ok();
    }

    @PostMapping("/updateFlowPoolReminder")
    @ApiOperation(value = "流量池提醒阈值修改")
    @OperationLog(operationName = "流量池管理——修改流量池提醒阈值", operationType = OperationTypeEnum.UPDATE)
    public Response updateFlowPollReminder(
            @ApiParam(name = "flowPoolId", value = "流量池ID", required = true) @RequestParam String flowPoolId,
            @ApiParam(name = "alarmThreshold", value = "提醒阈值", required = true) @RequestParam Integer alarmThreshold) {
        cmsFlowpoolInfoService.updateFlowPoolReminder(flowPoolId, alarmThreshold);
        return Response.ok();
    }

    @PostMapping("/updateFlowPool")
    @ApiOperation(value = "流量池修改接口")
    @OperationLog(operationName = "流量池管理——流量池修改", operationType = OperationTypeEnum.UPDATE)
    public Response updateFlowPool(@RequestBody @Valid AddFlowpoolVO updateFlowPoolVO) {
        cmsFlowpoolInfoService.updateFlowPool(updateFlowPoolVO);
        return Response.ok();
    }

    @PostMapping("/deleteFlowPool")
    @ApiOperation(value = "流量池删除接口")
    public Response deleteFlowPool(String[] flowPools) {
        cmsFlowpoolInfoService.deleteFlowPool(flowPools);
        return Response.ok();
    }


    @PostMapping("/getFlowpoolList")
    @ApiOperation("流量池列表查询")
    public Response<List<GetFlowpoolListVO>> getFlowpoolList(@RequestBody @Valid FlowpoolListVO flowListVO) {
        return Response.ok(flowPoolService.getFlowList(flowListVO));
    }


    @PostMapping("/smsGetFlowpool")
    @ApiOperation("流量池列表查询")
    public Response<List<SmsGetFlowPoolDTO>> smsGetFlowpool(@RequestBody @Valid FlowPoolSearchVO flowListVO) {
        return Response.ok(flowPoolService.smsGetFlowpool(flowListVO));
    }

    @PostMapping("/pageList")
    @ApiOperation("流量池列表查询")
    public Response<List<CmsFlowpoolInfo>> pageList(@RequestBody @Valid FlowpoolListVO flowListVO) {
        return Response.ok(flowPoolService.pageList(flowListVO));
    }

    @PostMapping("/getFlowpoolListAPI")
    @ApiOperation("流量池列表查询[外部API]")
    public Response<List<FlowpoolListDTO>> queryFlowPoolList(@RequestBody FlowpoolListVO flowListVO) {
        return Response.ok(flowPoolService.queryFlowPoolList(flowListVO));
    }

    @PostMapping("/getRelateCardPool")
    @ApiOperation("流量池关联卡池查询")
    public Response<Map<String, Object>> getRelateCardPool(@RequestBody @Valid GetRelateCardPoolVO getRelateCardPoolVO) {
        return Response.ok(flowPoolService.getRateTree(getRelateCardPoolVO.getMcc(), getRelateCardPoolVO.getFlowPoolId(), getRelateCardPoolVO.getIsGetAll()));
    }

    @GetMapping("/getFlowpoolUseRecord")
    @ApiOperation("流量池使用记录")
    public Response<List<FlowpoolUseDetailVO>> getFlowpoolUseRecord(@RequestParam String corpId,
                                                                    @RequestParam(required = false) String flowPoolName,
                                                                    @RequestParam(required = false) String nameEn,
                                                                    @RequestParam(required = false) String startTime,
                                                                    @RequestParam(required = false) String endTime,
                                                                    @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.getFlowpoolUseRecord(corpId, flowPoolName, startTime, endTime, pageSize, pageNum, nameEn));
    }


    @GetMapping("/flowpoolBillExport")
    @ApiOperation("流量池账单汇总导出")
    public Response<BillExport> flowpoolBillExport(@RequestParam String corpId,
                                                                    @RequestParam(required = false) String flowPoolName,
                                                                    @RequestParam(required = false) String nameEn,
                                                                    @RequestParam(required = false) String startTime,
                                                                    @RequestParam(required = false) String endTime) {
        return Response.ok(flowPoolService.flowpoolBillExport(corpId, flowPoolName, nameEn,startTime, endTime));
    }

    @GetMapping("/getCardUseDetailRecord")
    @ApiOperation("流量池使用记录详情")
    public Response<List<CardUseDetailVO>> getCardUseDetailRecord(@RequestParam String flowPoolUniqueId,
                                                                  @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.getCardUseDetailRecord(flowPoolUniqueId, pageSize, pageNum));
    }

    @PostMapping("/getChannelFlowList")
    @ApiOperation("获取渠道商流量池")
    public Response<List<GetFlowpoolListVO>> getChanelFlowList(@RequestBody @Valid FlowpoolListVO flowListVO) {
//        if (ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(flowListVO.getCooperationMode())){
//            throw new BizException("Checking of the data pool list is not allowed under A~Z model");
//        }
        return Response.ok(flowPoolService.getChannelFlowList(flowListVO));
    }

    @PutMapping("/rechargeFlow")
    @ApiOperation("流量池充值")
    @OperationLog(operationName = "客户管理——流量池充值",operationType = OperationTypeEnum.UPDATE)
    public Response<String> rechargeFlow(@RequestParam String flowPoolId, @RequestParam String flowValue) {
        flowPoolService.rechargeFlow(flowPoolId, flowValue);
        return Response.ok();
    }

    @GetMapping("/getCorpList")
    @ApiOperation("渠道商查询")
    public Response<List<GetCorpListVo>> getCorpList(@RequestParam(required = false) String corpName,
                                                     @RequestParam(required = false) String type,
                                                     @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.getCorpList(corpName, type, pageSize, pageNum));
    }


    @PostMapping("/flowpoolListOut")
    @ApiOperation("流量池列表导出")

    public Response<ExportVO> FlowpoolListOut(@RequestBody FlowpoolListVO flowListVO) {
        return Response.ok(flowPoolService.FlowPoolOut(flowListVO));
    }

    @PostMapping("/ChannelFlowListOut")
    @ApiOperation("渠道商流量池列表导出")

    public Response<ExportVO> ChannelFlowListOut(@RequestBody FlowpoolListVO flowListVO) {
        return Response.ok(flowPoolService.ChannelFlowPoolOut(flowListVO));
    }

    @PostMapping("/outFlowpoolUseRecord")
    @ApiOperation("流量池使用记录导出")

    public Response<ExportVO> outFlowpoolUseRecord(@RequestParam String corpId,
                                                   @RequestParam String exportType,
                                                   @RequestParam String userId,
                                                   @RequestParam(required = false) String flowpoolName,
                                                   @RequestParam(required = false) String startTime,
                                                   @RequestParam(required = false) String endTime,
                                                   @RequestParam(required = false) String nameEn,
                                                   @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.flowPoolUseRecordOut(corpId, exportType, userId, flowpoolName, startTime, endTime, pageSize, pageNum, nameEn));
    }

    @PostMapping("/outFlowPoolDetailRecord")
    @ApiOperation("流量池使用记录详情导出")
    public Response<ExportVO> outFlowPoolDetailRecord(@RequestParam String flowPoolUniqueId,
                                                      @RequestParam String exportType,
                                                      @RequestParam String userId,
                                                      @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.outFlowPoolDetailRecord(flowPoolUniqueId, exportType, userId, pageSize, pageNum));
    }

    @GetMapping("/getCard")
    @ApiOperation("卡号列表查询")
    public Response<List<CardListVo>> getCardList(@RequestParam String corpId,
                                                  @RequestParam(required = false) String ICCID,
                                                  @RequestParam(required = false) String status,
                                                  @RequestParam String cooperationMode,
                                                  @RequestParam int pageSize, @RequestParam int pageNum) {
//        if (ChannelDistributorDetail.CooperationModeEnum.A2Z.getType().equals(cooperationMode)){
//            throw new BizException("Checking of the data pool card number list is not allowed under A~Z model");
//        }

        String iccid = ICCID;
        if (StrUtil.isNotBlank(iccid)) {
            iccid = ICCID.trim();
        }
        return Response.ok(flowPoolService.getCard(corpId, iccid, status, pageSize, pageNum));
    }

    @GetMapping("/getICCID")
    @ApiOperation("ICCID列表查询")
    public Response<List<GetIccidListVO>> getICCIDList(@RequestParam String flowPoolId,
                                                       @RequestParam(required = false) String sortField,
                                                       @RequestParam(required = false) String sequence,
                                                       @RequestParam(required = false) String ICCID,
                                                       @RequestParam(required = false) String cardRemark,
                                                       @RequestParam(required = false) String currentRateType,
                                                       @RequestParam(required = false) String flowPoolStatus,
                                                       @RequestParam int pageSize, @RequestParam int pageNum) {

        String iccid = ICCID;
        if (StrUtil.isNotBlank(iccid)) {
            iccid = ICCID.trim();
        }
        List<String> list = new ArrayList<>();
        if (StrUtil.isNotBlank(iccid)) {
            list.add(iccid);
        }
        return Response.ok(flowPoolService.getICCIDList(flowPoolId, list,sortField,sequence, cardRemark, currentRateType, flowPoolStatus, pageSize, pageNum));
    }
    @PostMapping("/outICCID")
    @ApiOperation("ICCID列表导出")
    public Response<ExportVO> outICCIDList(@RequestParam String flowPoolId,
                                           @RequestParam String userId,
                                           @RequestParam String exportType,
                                           @RequestParam(required = false) String ICCID,
                                           @RequestParam(required = false) String cardRemark,
                                           @RequestParam(required = false) String currentRateType,
                                           @RequestParam(required = false) String flowPoolStatus,
                                           @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.outICCIDList(flowPoolId, exportType, userId, ICCID, cardRemark, currentRateType, flowPoolStatus, pageSize, pageNum));
    }
    @PostMapping("/getICCIDAPI")
    @ApiOperation("ICCID列表查询[外部API]")
    public Response<FlowpoolCardListDTO> queryFlowPoolCardList(@RequestBody FlowpoolListVO flowListVO) {
        return Response.ok(flowPoolService.queryFlowPoolCardList(flowListVO));
    }

    @GetMapping("/card/pause")
    @ApiOperation("客户管理/渠道自服务流量池卡暂停")
    @OperationLog(operationName = "客户管理/渠道自服务——流量池卡暂停",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> flowPoolCardPause(@RequestParam("iccid") String iccid,
                                            @RequestParam("flowPoolID") String flowPoolID) {
        flowPoolService.flowPoolCardPause(iccid, flowPoolID);
        return Response.ok();
    }


    @GetMapping("/card/resume")
    @ApiOperation("客户管理/渠道自服务流量池卡恢复")
    @OperationLog(operationName = "客户管理/渠道自服务——流量池卡恢复",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> flowPoolCardResume(@RequestParam("iccid") String iccid,
                                             @RequestParam("flowPoolID") String flowPoolID) {

        flowPoolService.flowPoolCardResume(iccid, flowPoolID);
        return Response.ok();
    }

    @GetMapping("/card/pauseAPI")
    @ApiOperation("客户管理/渠道自服务流量池卡暂停[外部API]")
    public Response<Void> flowPoolCardPauseAPI(@RequestParam("iccid") String iccid,
                                            @RequestParam("flowPoolID") String flowPoolID) {
        flowPoolService.flowPoolCardPause(iccid, flowPoolID);
        return Response.ok();
    }


    @GetMapping("/card/resumeAPI")
    @ApiOperation("客户管理/渠道自服务流量池卡恢复[外部API]")
    public Response<Void> flowPoolCardResumeAPI(@RequestParam("iccid") String iccid,
                                             @RequestParam("flowPoolID") String flowPoolID) {

        flowPoolService.flowPoolCardResume(iccid, flowPoolID);
        return Response.ok();
    }
    @PostMapping("/updateICCID")
    @OperationLog(operationName = "流量池ICCID管理——ICCID修改",operationType = OperationTypeEnum.UPDATE)
    @ApiOperation("流量池卡管理")
    public Response updateFlowPoolICCIDList(@RequestBody UpdateICCIDListVO updateICCIDListVO) {
        flowPoolService.updateFlowPoolICCIDList(updateICCIDListVO);
        return Response.ok();
    }

    @PostMapping("/updateICCIDAPI")
    @ApiOperation("流量池卡管理")
    public Response updateFlowPoolICCIDListAPI(@RequestBody UpdateICCIDListVO updateICCIDListVO) {
        flowPoolService.updateFlowPoolICCIDList(updateICCIDListVO);
        return Response.ok();
    }

    @PostMapping("/outCardList")
    @ApiOperation("卡号列表导出")
    public Response<ExportVO> outCardList(@RequestParam String corpId,
                                          @RequestParam String exportType,
                                          @RequestParam String userId,
                                          @RequestParam(required = false) String ICCID,
                                          @RequestParam(required = false) String Status,
                                          @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.outCard(corpId, exportType, userId, ICCID, Status, pageSize, pageNum));
    }




    @GetMapping("/getIccidImportTaskList")
    @ApiOperation("导入任务查询")
    public Response<List<CmsFlowpoolIccidimportTask>> getIccidImportTaskList(@RequestParam String flowPoolId,
                                                                             @RequestParam int pageSize, @RequestParam int pageNum) {
        return Response.ok(flowPoolService.getIccidImportTaskList(flowPoolId, pageSize, pageNum));
    }

    @GetMapping("/getIccidImportTaskFile")
    @ApiOperation("下载导入文件")
    public Response<String> getIccidImportTaskFile(@RequestParam Long id, @RequestParam String type,
                                                   HttpServletResponse response) {
        flowPoolService.getIccidImportTaskFile(id, type, response);
        return Response.ok();
    }

    @PostMapping("/removeCards")
    @ApiOperation("流量池ICCID删除")
    @OperationLog(operationName = "流量池管理——删除ICCID", operationType = OperationTypeEnum.DELETE)
    public Response<Void> removeCards(@RequestBody @Valid RemoveCardsReq removeCardsReq) {
        flowPoolService.removeCards(removeCardsReq);
        return Response.ok();
    }

    @PostMapping("/ChannelRemoveCards")
    @ApiOperation("卡号移除")
    @OperationLog(operationName = "流量池管理——移除卡号", operationType = OperationTypeEnum.DELETE)
    public Response<Void> ChannelRemoveCards(@RequestBody @Valid RemoveCardsReq removeCardsReq) {
        flowPoolService.removeCards(removeCardsReq);
        return Response.ok();
    }

    @PostMapping("/removeCard")
    @ApiOperation("流量池ICCID删除[外部API]")
    public Response<Void> removeCard(@RequestBody @Valid RemoveCardsReq removeCardsReq) {
        flowPoolService.removeCards(removeCardsReq);
        return Response.ok();
    }

    @PostMapping("/smsCheckFlowPool")
    @ApiOperation("短信流量池校验")
    public Response<ImportPackageDto> smsCheckFlowPool(@RequestBody List<String> ids) {
        return Response.ok(flowPoolService.smsCheckFlowPool(ids));
    }

}
