package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流量单位枚举
 * <AUTHOR>
 * @date 2021-6-4 13:14:01
 */
@Getter
@AllArgsConstructor
public enum FlowUnitEnum {

    /**
     * MB
     */
    MB("1"),

    /**
     * GB
     */
    GB("2");

    private String value;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

}
