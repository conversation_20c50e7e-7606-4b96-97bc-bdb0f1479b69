package com.ebupt.cmi.clientmanagement.domain.enums.cooperation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelCheckStatus.java
 * @Description 合作商checkStatus
 * @createTime 2021年05月31日 15:25:00
 */
@AllArgsConstructor
@Getter
public enum ChannelCheckStatus {
    /**
     * 新建待审批
     */
    NEWFORCHECK("1", "新建待审批"),

    /**
     * 通过
     */
    PASS("2", "通过"),

    /**
     * 不通过
     */
    NOPASS("3", "不通过"),

    /**
     * 删除待审批
     */
    DELETEFORCHECK("4", "删除待审批");


    private String code;
    private String description;
}
