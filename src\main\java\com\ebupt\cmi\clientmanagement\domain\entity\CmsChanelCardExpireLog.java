package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmsChanelCardExpireLog.java
 * @Description cms_chanel_card_expire_log 合作商到期日志表
 * @createTime 2021年06月30日 15:51:00
 */
@TableName("cms_chanel_card_expire_log")
@Data
@ToString
@Builder
public class CmsChanelCardExpireLog {
    @TableId
    Long id;

    String imsi;

    String iccid;

    String packageId;

    LocalDateTime activeTime;

    LocalDateTime expireTime;

    LocalDateTime createTime;
}
