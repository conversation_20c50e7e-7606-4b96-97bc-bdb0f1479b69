package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("cms_package_surf_status_log")
@ApiModel(value="PackageSurfStatusLog", description="")
public class PackageSurfStatusLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "套餐唯一ID")
    private String packageUniqueId;

    @ApiModelProperty(value = "上网状态	1：正常	2：限速	")
    private String surfStatus;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @Getter
    @AllArgsConstructor
    public enum SurfStatusEnum {

        /**
         * 正常
         */
        NORMAL("1"),

        /**
         * 限速
         */
        LIMIT("2");

        private String status;

    }


}
