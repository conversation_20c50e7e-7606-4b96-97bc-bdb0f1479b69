package com.ebupt.cmi.clientmanagement.consumer.upcc;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.uitils.RetryUtil;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.RetryContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccSignContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.strategy.AbstractStrategy;
import com.ebupt.cmi.clientmanagement.consumer.upcc.strategy.ActivatedPackageStrategy;
import com.ebupt.cmi.clientmanagement.consumer.upcc.strategy.UnactivatedPackageStrategy;
import com.ebupt.cmi.clientmanagement.consumer.upcc.vo.DirectionUpccVO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsCardUpccRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsPackageCardUpccRelation;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageDirectionRelation;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.mapper.CmsCardUpccRecordMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageCardUpccRelationMapper;
import com.ebupt.cmi.clientmanagement.mapper.PackageDirectionRelationMapper;
import com.ebupt.cmi.clientmanagement.service.ICmsCardUpccRecordService;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
@RabbitListener(queues = "upcc.sign.queue")
@Slf4j
@RequiredArgsConstructor
public class UpccConsumer extends RetryUtil {

    private final ActivatedPackageStrategy activatedPackageStrategy;

    private final UnactivatedPackageStrategy unactivatedPackageStrategy;

    @Resource
    private CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    @Resource
    private Executor upccExecutor;

    @Resource
    public RedisTemplate<String, Object> redisTemplate;

    @Resource
    public CmsCardUpccRecordMapper cmsCardUpccRecordMapper;


    @Resource
    public PackageDirectionRelationMapper packageDirectionRelationMapper;

    @Resource
    public CoreNetCaller coreNetCaller;

    @RabbitHandler
    public void process(String directionUpccVOMessageString, Channel channel, Message message) throws IOException {

        log.debug("upcc签约队列收到消息{}", directionUpccVOMessageString);

        try {
            DirectionUpccVO directionUpccVO =
                    JSON.parseObject(directionUpccVOMessageString, DirectionUpccVO.class);

            String packageUniqueId = directionUpccVO.getPackageUniqueId();

            String packageStatus = directionUpccVO.getPackageStatus();
            //获取需要处理的定向应用
            List<PackageDirectionRelation> packageDirectionRelations;
            if (directionUpccVO.getMessageType().equals("1") || packageStatus.equals("1")) {
                log.debug("需要处理套餐所有定向应用");
                packageDirectionRelations = packageDirectionRelationMapper.selectList(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                        .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId));
            } else {
                log.debug("处理指定定向应用， {}", directionUpccVO.getAppGroupId());
                packageDirectionRelations = packageDirectionRelationMapper.selectList(Wrappers.lambdaQuery(PackageDirectionRelation.class)
                        .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId)
                        .in(PackageDirectionRelation::getAppGroupId, directionUpccVO.getAppGroupId()));
            }

            ArrayList<Long> list = new ArrayList<>();

            boolean hasSetFlow;

            for (PackageDirectionRelation packageDirectionRelation : packageDirectionRelations) {

                String appGroupId = packageDirectionRelation.getAppGroupId();

                AbstractStrategy strategy;

                if (packageStatus.equals("1") || packageDirectionRelation.getDirectType().equals("1")) {
                    if (packageDirectionRelation.getDirectType().equals("1") && directionUpccVO.isFlag()) {
                        log.debug("套餐通用已用完，不处理此应用");
                        continue;
                    }
                    log.debug("套餐待激活或者限速定向应用签约");
                    strategy = unactivatedPackageStrategy;
                } else {
                    log.debug("已激活套餐定向应用签约");
                    strategy = activatedPackageStrategy;
                }

                List<CmsPackageCardUpccRelation> cmsPackageCardUpccRelations = cmsPackageCardUpccRelationMapper.selectList(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                        .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                        .eq(CmsPackageCardUpccRelation::getAppGroupId, appGroupId)).stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(CmsPackageCardUpccRelation::getAppId))), ArrayList::new));
                log.debug("需要处理得APP集合：{}", cmsPackageCardUpccRelations);

                Set<Long> appIdList = cmsPackageCardUpccRelations.stream().map(CmsPackageCardUpccRelation::getAppId).collect(Collectors.toSet());

                list.addAll(appIdList);

                hasSetFlow = false;

                for (CmsPackageCardUpccRelation cmsPackageCardUpccRelation : cmsPackageCardUpccRelations) {

                    UpccConsumerContext upccConsumerContext = new UpccConsumerContext();

                    BeanUtils.copyProperties(directionUpccVO, upccConsumerContext);

                    upccConsumerContext.setAppId(cmsPackageCardUpccRelation.getAppId());

                    upccConsumerContext.setPackageDirectionRelation(packageDirectionRelation);

                    upccConsumerContext.setHasSetFlow(hasSetFlow);

                    upccExecutor.execute(() -> process(upccConsumerContext, strategy));

                    //一个应用组只需设置一次流量
                    hasSetFlow = true;
                }

            }

            if ((directionUpccVO.getMessageType().equals("1") || packageStatus.equals("1")) && !CollectionUtils.isEmpty(list)) {

                RetryContext retryContext = new RetryContext();

                ArrayList<UpccSignContext> upccSignContexts = new ArrayList<>();
                List<CmsCardUpccRecord> cmsCardUpccRecordList = cmsCardUpccRecordMapper.selectList(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getImsi, directionUpccVO.getHImsi())
                        .notIn(CmsCardUpccRecord::getAppId, list));

                for (CmsCardUpccRecord cmsCardUpccRecord : cmsCardUpccRecordList) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(directionUpccVO.getMsisdnToH());
                    upccSignContext.setOldUpccSignBizId(cmsCardUpccRecord.getUpccSignBizId());
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecord);
                    upccSignContexts.add(upccSignContext);
                }

                if (directionUpccVO.getVimsi() != null) {
                    cmsCardUpccRecordList = cmsCardUpccRecordMapper.selectList(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                            .eq(CmsCardUpccRecord::getImsi, directionUpccVO.getVimsi())
                            .notIn(CmsCardUpccRecord::getAppId, list));
                    for (CmsCardUpccRecord cmsCardUpccRecord : cmsCardUpccRecordList) {
                        UpccSignContext upccSignContext = new UpccSignContext();
                        upccSignContext.setMsisdn(directionUpccVO.getMsisdnToV());
                        upccSignContext.setOldUpccSignBizId(cmsCardUpccRecord.getUpccSignBizId());
                        upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecord);
                        upccSignContexts.add(upccSignContext);
                    }
                }

                retryContext.setUpccSignContext(upccSignContexts);

                execute(retryContext);

            }
        } catch (Exception e) {

            log.warn("", e);

            throw e;

        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }


    }

    public void process(UpccConsumerContext upccConsumerContext, AbstractStrategy strategy) {

        strategy.handle(upccConsumerContext);

        strategy.execute(upccConsumerContext.getRetryContext());

    }


    public void doBiz(UpccSignContext context) {
        coreNetCaller.upccUnSignature(context.getMsisdn(), context.getOldUpccSignBizId());
    }


    public void afterFail(RetryContext context) {

    }


    public void afterSuccess(RetryContext context) {
    }

    @Override
    public void afterSuccess(UpccSignContext upccSignContext) {
        cmsCardUpccRecordMapper.deleteById(upccSignContext.getCmsCardUpccRecord());
    }
}
