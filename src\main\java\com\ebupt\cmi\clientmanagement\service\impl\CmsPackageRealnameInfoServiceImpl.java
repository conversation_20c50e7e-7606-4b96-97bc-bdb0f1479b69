package com.ebupt.cmi.clientmanagement.service.impl;

import com.ebupt.cmi.clientmanagement.domain.entity.CmsPackageRealnameInfo;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageRealnameInfoMapper;
import com.ebupt.cmi.clientmanagement.service.ICmsPackageRealnameInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 套餐免实名记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class CmsPackageRealnameInfoServiceImpl extends ServiceImpl<CmsPackageRealnameInfoMapper, CmsPackageRealnameInfo> implements ICmsPackageRealnameInfoService {

}
