package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 是否终端套餐
 *
 * <AUTHOR>
 * @date 2021-5-25 10:45:51
 */
@Slf4j
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum IsTerminal {

    /**
     * 终端套餐
     */
    YES("1", "终端套餐"),

    /**
     * 非终端套餐
     */
    NO("2", "非终端套餐");


    private String value;
    private String desc;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

    public static String getDesc(String value) {
        for (IsTerminal p : IsTerminal.values()) {
            if (p.getValue().equals(value)) {
                return p.getDesc();
            }
        }
        log.info("输入不符合要求：{}", value);
        return "";
    }
}
