package com.ebupt.cmi.clientmanagement.domain.dto.channel;

import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SearchDTO
 * 渠道商分页查询结果
 *
 * @Author: zhaoqiankun
 * @Date: 2021/6/15 9:59
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(description = "分页查询返回响应数据")
public class SearchDTO {

    @ApiModelProperty(value = "ID", required = false)
    private String corpId;

    @ApiModelProperty(value = "名称", required = false)
    private String corpName;

    @ApiModelProperty(value = "创建(购买)时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "ebsCode", required = false)
    private String ebsCode;

    @ApiModelProperty(value = "可用额度", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal deposit;

    @ApiModelProperty(value = "阈值", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal depositeRemindThreshold;

    @ApiModelProperty(value = "币种", required = false)
    private String currencyCode;

    @ApiModelProperty(value = "订购状态", required = false)
    private String isSub;

    @ApiModelProperty(value = "审核状态", required = false)
    private String checkStatus;

    @ApiModelProperty(value = "审核状态(汉字）", required = false)
    private String checkStatusName;

    @ApiModelProperty(value = "已用额度", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal usedAmount;

    @ApiModelProperty(value = "渠道商类型", required = false)
    private String channelType;

    @ApiModelProperty(value = "总额度", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalDeposit;

    @ApiModelProperty(value = "是否允许自建套餐")
    private String allowNewPackage;

    @ApiModelProperty(value = "自建套餐上限")
    private Integer limitPackageNum;

//    @ApiModelProperty(value = "国家卡池id")
//    private Long groupId;

    @JsonIgnore
    private BigDecimal stopUseRemindThreshold;

    private List<String> upccTemplateInfo;

    @ApiModelProperty(value = "渠道合作模式")
    private String channelCoopeRationMode;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal marketingAmount;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal creditAmount;

    @ApiModelProperty(value = "a2z渠道商类型", required = false)
    private String a2zChannelType;

    @JsonIgnore
    private BigDecimal a2zPreDeposit;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zTotalDeposit;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zUsedDeposit;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zMarketingAmount;

    private SearchDTO searchDTO;
}
