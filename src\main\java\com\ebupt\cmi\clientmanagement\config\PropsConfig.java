package com.ebupt.cmi.clientmanagement.config;

import com.ebupt.cmi.clientmanagement.domain.properties.ActiveNotificationProps;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/8 16:58
 */
@Configuration
public class PropsConfig {

    @Bean
//    @RefreshScope
    @ConfigurationProperties("active-notification")
    public ActiveNotificationProps activeNotificationProps() {
        return new ActiveNotificationProps();
    }

}
