package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ description 查询会话历史列表入参
 * @ author yhd
 * @ since 2025/2/27 18:27
 */
@Data
public class HistorySessionListDTO{

    @ApiModelProperty(value = "iccid")
    @NotBlank(message = "iccid is mandatory")
    private String iccid;

    @ApiModelProperty(value = "CMS获取session条数")
    private Integer sessionList;
}
