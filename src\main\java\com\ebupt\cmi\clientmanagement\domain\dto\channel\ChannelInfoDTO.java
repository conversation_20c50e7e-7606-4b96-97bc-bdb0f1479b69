package com.ebupt.cmi.clientmanagement.domain.dto.channel;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageRelation;
import com.ebupt.cmi.clientmanagement.feign.back.vo.User;
import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ChannelInfo
 * 渠道商详情数据
 * @Author: zhaoqiankun
 * @Date: 2021/6/15 19:49
 */

@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@ApiModel(description = "渠道商详情")
public class ChannelInfoDTO extends  SearchDTO{
    @ApiModelProperty(value = "渠道商类型", required = false)
    private String type;

    @ApiModelProperty(value = "可购买套餐组", required = false)
    private Map<String, ChannelPackageRelation> packageGroups ;

    @ApiModelProperty(value = "货币种类", required = false)
    private String currencyCode;

    @ApiModelProperty(value = "联系人邮箱", required = false)
    private String email;

    @ApiModelProperty(value = "套餐购买数量", required = false)
    private Integer accountNum;

    @ApiModelProperty(value = "直接比例", required = false)
    private Integer directRatio;

    @ApiModelProperty(value = "间接比例", required = false)
    private Integer indirectRatio;

    @ApiModelProperty(value = "限制类型", required = false)
    private Integer indirectType ;

    @ApiModelProperty(value = "合约开始时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractStartTime;

    @ApiModelProperty(value = "合约结束时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractEndTime;

    @ApiModelProperty(value = "合约期承诺的金额", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "APP_KEY", required = false)
    private String appKey;

    @ApiModelProperty(value = "APP_Secret", required = false)
    private String appSecret;

    @ApiModelProperty(value = "渠道商编号", required = false)
    private String channelCode;

    @ApiModelProperty(value = "渠道商通知URL", required = false)
    private String channelUrl;

    @ApiModelProperty(value = "购买次数", required = false)
    private Integer indirectCount;

    @ApiModelProperty(value = "创建的账户信息", required = false)
    private List<User> accounts;

    @ApiModelProperty(value = "购买折扣", required = false)
    private Integer discount;

    @ApiModelProperty(value = "押金重置金额", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal resetPrice;
//
//    @ApiModelProperty(value = "押金重置开关", required = false)
//    private String depositeReset;

    @ApiModelProperty(value = "币种名称", required = false)
    private String currencyCodeName;

    @ApiModelProperty(value = "公司名称", required = false)
    private String companyName;

    @ApiModelProperty(value = "内部订单", required = false)
    private String internalOrder;

    @ApiModelProperty(value = "地址", required = false)
    private String address;

    @ApiModelProperty(value = "激活通知开关", required = false)
    private String activateNotification;

    @ApiModelProperty(value = "渠道商类型", required = false)
    private String channelType;

    @ApiModelProperty(value = "退订规则", required = false)
    private String unsubscribeRule;

    @ApiModelProperty(value = "总额度", required = false)
    private BigDecimal totalDeposit;

    @ApiModelProperty(value = "激活通知URL", required = false)
    private String activateNotificationUrl;

    @ApiModelProperty(value = "是否允许自建套餐")
    private String allowNewPackage;

    @ApiModelProperty(value = "自建套餐上限")
    private Integer limitPackageNum;

    @ApiModelProperty(value = "当前自建套餐总量")
    private Integer newPackageNum;

    @ApiModelProperty(value = "国家卡池id")
    private Long groupId;

    private List<String> upccTemplateInfo;

    private BigDecimal runoutofBalanceRemindThreshold;

    private BigDecimal prohibitiveBuyRemindThreshold;

    private BigDecimal stopUseRemindThreshold;

    @JsonIgnore
    private String channelCooperationModelString;

    private List<String> channelCooperationMode;

    @JsonIgnore
    private String packageUsePercentageString;

    private List<ChannelInfoAuthDTO.PackageUsePercentage> packageUsePercentage;

    private List<String> appids;

    /**
     * esim通知开关
     * 1：开
     * 2：关
     * 默认值：2
     */
    private String esimNotification;

    /**
     * esim通知url
     */
    private String esimNotificationUrl;

    private String distributionAccountingPeriodId;

    private String a2zAccountingPeriodId;

    private String a2zChannelType;

    private String resourceAccountingPeriodId;

    private BigDecimal resourceRunoutofBalanceRemindThreshold;

    private BigDecimal resourceProhibitiveBuyRemindThreshold;

    private BigDecimal resourceStopUseRemindThreshold;

    private String resourceChannelType;

}
