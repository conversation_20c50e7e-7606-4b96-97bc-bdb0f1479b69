package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.*;

import java.util.Date;

/**
 * (CmsFlowpoolRemind)实体类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsFlowpoolRemind {
    /**
     * 主键
     */
    private Long id;
    /**
     * 标识类型
     * 1：单卡单周期邮件下发标识 2：单卡总量邮件下发标识
     */
    private String type;
    /**
     * 归属的流量池唯一id
     */
    private String flowUniqueId;
    /**
     * 流量池ID
     */
    private String flowPoolId;
    /**
     * imsi
     */
    private String imsi;
    /**
     * 过期时间
     */
    private String expireTime;

    /**
     * 标识类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum TypeEnum {

        /**
         * 1：单卡单周期剩余流量
         */
        FLOW_POOL_CYCLE_REMAIN("1"),

        /**
         * 2：单卡全周期剩余流量
         */
        FLOW_POOL_CARD_REMAIN("2");

        @Getter
        private String value;

    }
}

