package com.ebupt.cmi.clientmanagement.consumer.strategy;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.callback.StrategyProcessor;
import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.BlockUpStrategy;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.LimitSpeedStrategy;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.ResetStrategyForSingleCycle;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelDistributorDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsFlowpoolInfoCycle;
import com.ebupt.cmi.clientmanagement.domain.entity.flowpool.CmsFlowpoolConsumeErrorLog;
import com.ebupt.cmi.clientmanagement.domain.entity.flowpool.CmsFlowpoolConsumeSucLog;
import com.ebupt.cmi.clientmanagement.domain.enums.CurrentRateType;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.feign.back.vo.MailSendParam;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import com.ebupt.cmi.clientmanagement.mapper.ChannelCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelDistributorDetailMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsFlowpoolInfoCycleMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsFlowpoolInfoMapper;
import com.ebupt.cmi.clientmanagement.mapper.flowpool.CmsFlowpoolConsumeErrorLogMapper;
import com.ebupt.cmi.clientmanagement.mapper.flowpool.CmsFlowpoolConsumeSucLogMapper;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import com.ebupt.cmi.clientmanagement.utils.FlowOperationUtils;
import com.ebupt.cmi.clientmanagement.utils.UpccUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FlowPoolConsumerBaseStrategy.java
 * @Description 流量池消费者抽象策略父类
 * @createTime 2022年01月12日 16:31:00
 */
@RefreshScope
@Slf4j
public abstract class AbstractFlowPoolConsumerStrategy implements StrategyProcessor {

    @Value("${flowPool.rabbit.resetTime}")
    protected Integer resetTime;

    @Value("${flowPool.rabbit.retryInterval}")
    protected Integer retryInterval;

    @Value("${upcc-zero-template-id}")
    protected String zeroSpeedUpccId;

    @Autowired
    protected CmsFlowpoolConsumeErrorLogMapper errorLogMapper;

    @Autowired
    protected CmsFlowpoolConsumeSucLogMapper sucLogMapper;

    @Autowired
    protected ChannelCardMapper channelCardMapper;

    @Autowired
    protected PmsFeignClient pmsFeignClient;

    @Autowired
    protected ControlFeignClient controlFeignClient;


    @Resource
    protected CoreNetCaller coreNetCaller;

    @Autowired
    protected CCRCommonService ccrCommonService;

    @Autowired
    CmsFlowpoolInfoCycleMapper cmsFlowpoolInfoCycleMapper;

    @Autowired
    HvShareRepository hvShareRepository;

    @Autowired
    RedisTemplate<String, String> redisTemplate;

    @Autowired
    private BackFeignClient backFeignClient;

    @Autowired
    private ChannelDistributorDetailMapper channelDistributorDetailMapper;

    @Autowired
    protected UpccUtil upccUtil;

    @Resource
    private CmsFlowpoolInfoMapper cmsFlowpoolInfoMapper;

    @Autowired
    private FlowOperationUtils flowOperationUtils;

    /**
     * 所有逻辑的前置处理,对是否处理做逻辑判断，需要注意，return true，代表不进入逻辑处理|return false代表正常处理
     *
     * @param flowPoolConsumerContext 上下文
     */
    public boolean beforeEveryThing(FlowPoolConsumerContext flowPoolConsumerContext) {

        if (this instanceof BlockUpStrategy || this instanceof LimitSpeedStrategy) {

            setPriority(flowPoolConsumerContext);

            //当消息的控制逻辑优先级大于表中对应控制逻辑优先级时，进行处理
            if (flowPoolConsumerContext.getPriorityFromMessage() > flowPoolConsumerContext.getPriorityFromTable()) {

                return false;

            } else if (flowPoolConsumerContext.getPriorityFromMessage() < flowPoolConsumerContext.getPriorityFromTable()) {

                return true;

            } else if (flowPoolConsumerContext.getPriorityFromTable() == flowPoolConsumerContext.getPriorityFromMessage()) {

                setCurrentRateType(flowPoolConsumerContext);

                //如若是限速
                if ((flowPoolConsumerContext.getPriorityFromTable() + flowPoolConsumerContext.getPriorityFromMessage()) == 2) {

                    return false;

                    //如果是停用，直接改表退出
                } else {

                    updateChannelCard(flowPoolConsumerContext);

                    return true;
                }
            }

        }

        return false;

    }

    /**
     * 为限速、停用流程设置优先级
     *
     * @param flowPoolConsumerContext 上下文
     * @return void
     */
    public void setPriority(FlowPoolConsumerContext flowPoolConsumerContext) {

        FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessageVO = flowPoolConsumerContext.getFlowPoolRabbitMQMessage();

        ChannelCard channelCard = channelCardMapper.selectOne(Wrappers.lambdaUpdate(ChannelCard.class)
                .eq(ChannelCard::getImsi, flowPoolRabbitMQMessageVO.getHimsi())
        );

        flowPoolConsumerContext.setCurrentChannelCard(channelCard);

        String currentRateType = channelCard.getCurrentRateType();

        String currentRateTypeFromMessage = flowPoolRabbitMQMessageVO.getCurrentRateType();

        //停用
        if ("3".equals(currentRateType) || "5".equals(currentRateType) || "7".equals(currentRateType)) {

            flowPoolConsumerContext.setPriorityFromTable(2);

        } else {
            flowPoolConsumerContext.setPriorityFromTable(1);
        }
        log.info("PriorityFromTable ：{}", flowPoolConsumerContext.getPriorityFromTable());

        if ("2".equals(currentRateTypeFromMessage)
                || "4".equals(currentRateTypeFromMessage)
                || "6".equals(currentRateTypeFromMessage)) {

            flowPoolConsumerContext.setPriorityFromMessage(1);

        } else if ("3".equals(currentRateTypeFromMessage)
                || "5".equals(currentRateTypeFromMessage)
                || "7".equals(currentRateTypeFromMessage)) {

            flowPoolConsumerContext.setPriorityFromMessage(2);

        }

    }

    /**
     * 根据类型的优先级，设置控制类型
     *
     * @param flowPoolConsumerContext 上下文
     */
    public void setCurrentRateType(FlowPoolConsumerContext flowPoolConsumerContext) {

        ChannelCard channelCard = flowPoolConsumerContext.getCurrentChannelCard();

        int typeFromTable = Integer.parseInt(channelCard.getCurrentRateType());

        int typeFromMessage = Integer.parseInt(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getCurrentRateType());

        //谁大取谁
        if (typeFromTable > typeFromMessage) {

            flowPoolConsumerContext.getFlowPoolRabbitMQMessage().setCurrentRateType(channelCard.getCurrentRateType());

        }

    }

    /**
     * 总入口，在此处去处理
     *
     * @param flowPoolConsumerContext 上下文
     */
    public void handle(FlowPoolConsumerContext flowPoolConsumerContext) {
        //先判断主卡的签约业务id
        HcardInfo hcardInfo = Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(
                flowPoolConsumerContext
                        .getFlowPoolRabbitMQMessage()
                        .getHimsi()));
        flowPoolConsumerContext.setHcardInfo(hcardInfo);

        if (StringUtils.isBlank(hcardInfo.getUpccSignPackageUniqueId())) {

            log.info("=================================该卡的签约id为空，无需进行外部网元交互=========================================");
        }

        updateChannelCard(flowPoolConsumerContext);

        //(2)入库【cms_flowpool_consume_suc_log】消费日志记录表
        insertCmsFlowpoolConsumeSucLog(flowPoolConsumerContext);
    }

    public void insertIntoError(FlowPoolConsumerContext flowPoolConsumerContext) {

        FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessageVO = flowPoolConsumerContext.getFlowPoolRabbitMQMessage();

        String bussinessType = flowPoolRabbitMQMessageVO.getCurrentRateType();

        CmsFlowpoolConsumeErrorLog errorLog = CmsFlowpoolConsumeErrorLog.builder()
                .businessType(bussinessType)
                .flowPoolUniqueId(flowPoolRabbitMQMessageVO.getFlowPoolUniqueId())
                .iccid(flowPoolRabbitMQMessageVO.getIccid())
                .imsi(flowPoolRabbitMQMessageVO.getImsi())
                .internetType(flowPoolRabbitMQMessageVO.getCardType())
                .msisdn(flowPoolRabbitMQMessageVO.getMsisdn())
                .repeatNum(resetTime)
                .build();

        errorLogMapper.insert(errorLog);

    }

    public boolean useFlowPool(FlowPoolConsumerContext flowPoolConsumerContext) {
        FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                .getFlowPoolRabbitMQMessage();

        HcardInfo hcardInfo = flowPoolConsumerContext.getHcardInfo();

        if (hcardInfo == null) {
            //先判断主卡的签约业务id
            hcardInfo = Response.getAndCheckRemoteData(pmsFeignClient.getCardByImsi(
                    flowPoolConsumerContext
                            .getFlowPoolRabbitMQMessage()
                            .getHimsi()));
            flowPoolConsumerContext.setHcardInfo(hcardInfo);
        }
        if (StrUtil.isBlank(hcardInfo.getUpccSignPackageUniqueId())) {
            return false;
        }

        return hcardInfo.getUpccSignPackageUniqueId().equals(messageVO.getPackageUniqueId());
    }

    /**
     * 更新cms_channel_card对应imsi的状态
     *
     * @param flowPoolConsumerContext 上下文
     */
    protected void updateChannelCard(FlowPoolConsumerContext flowPoolConsumerContext) {

        FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessageVO =
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage();

        String bussinessType = flowPoolRabbitMQMessageVO.getCurrentRateType();
        boolean resumeFlag = false;
        boolean isNeedSendEmail = false;
        if ("8".equals(bussinessType)) {
            resumeFlag = true;
            bussinessType = getFlowPoolCycle(flowPoolConsumerContext);
            log.info("卡状态应更新为：{}", bussinessType);
            if (bussinessType.equals("6")) {
                log.debug("卡更新状态为6，此种情况可能只下发单卡状态变更邮件");
                isNeedSendEmail = true;
            }
        }
        ChannelCard channelCard = channelCardMapper.selectOne(Wrappers.lambdaQuery(ChannelCard.class)
                .select(ChannelCard::getId, ChannelCard::getIccid, ChannelCard::getCorpId,
                        ChannelCard::getCurrentRateType, ChannelCard::getFlowPoolStatus, ChannelCard::getFlowPoolId)
                .eq(ChannelCard::getImsi, flowPoolRabbitMQMessageVO.getHimsi()));
        if ("1".equals(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getCardType())) {


            // 控制逻辑标识为流量池卡暂停不更新卡状态
            // 其他标识正常更新卡状态
            if (!CurrentRateType.FLOW_POOL_PAUSE_USE.getType().equals(bussinessType)) {
                channelCardMapper.update(null, Wrappers.lambdaUpdate(ChannelCard.class)
                        .set(ChannelCard::getCurrentRateType, bussinessType)
                        .eq(ChannelCard::getId, channelCard.getId()));
            } else {
                log.info("流量池卡暂停不更新卡状态：{}", bussinessType);
            }

            ChannelDistributorDetail detail = channelDistributorDetailMapper.selectOne(new LambdaQueryWrapper<ChannelDistributorDetail>()
                    .select(ChannelDistributorDetail::getCorpId, ChannelDistributorDetail::getEmail)
                    .eq(ChannelDistributorDetail::getCorpId, channelCard.getCorpId()));

            String email = Optional.ofNullable(detail).map(ChannelDistributorDetail::getEmail)
                    .filter(StringUtils::isNotBlank)
                    .orElseThrow(() -> new BizException("未找到渠道商邮件地址信息"));

            CmsFlowpoolInfoCycle cmsFlowpoolInfoCycle = cmsFlowpoolInfoCycleMapper.selectOne(Wrappers.lambdaQuery(CmsFlowpoolInfoCycle.class)
                    .eq(CmsFlowpoolInfoCycle::getFlowPoolId, channelCard.getFlowPoolId())
                    .orderByDesc(CmsFlowpoolInfoCycle::getUpdateTime));

            Optional.ofNullable(cmsFlowpoolInfoCycle).orElseThrow(() -> new BizException("未找到流量池信息"));
            // 声明： 以下代码均为了满足需求，代码可读性狗都不关心
            // 需求变更，把下发邮件操作提前至抛异常之前，且自己控制是否下发邮件
            try {
                String triggerType = flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getTriggerType();
                if (triggerType != null && "1234".contains(triggerType)) {
                    log.debug("重置、暂停、恢复、充值等不发送邮件");
                } else if (!ChannelCard.FlowPoolStatusEnum.PAUSE.getValue().equals(channelCard.getFlowPoolStatus()) &&
                        ("12345".contains(bussinessType)) || ("6".equals(bussinessType) && isNeedSendEmail)) {
                    //所有条件均需要满足 卡非暂停状态 才下发 单卡状态变更邮件
                    //单卡重置有可能 3 -> 6 此种情况只下发单卡状态变更邮件
                    boolean isChange = changeStatus(channelCard.getCurrentRateType(), bussinessType);
                    if (isChange) {
                        log.debug("卡状态变更，且非暂停发送单卡状态变更提醒邮件，之前状态： {}， 现在状态： {}",
                                channelCard.getCurrentRateType(), bussinessType);
                        Map<String, List<String>> map = new HashMap<>(4);
                        map.put("{iccid}", Collections.singletonList(channelCard.getIccid()));
                        map.put("{flowPoolName}", Collections.singletonList(cmsFlowpoolInfoCycle.getFlowPoolName()));
                        map.put("{oldStatus}", Collections.singletonList(CurrentRateType.getStatus(channelCard.getCurrentRateType())));
                        map.put("{newStatus}", Collections.singletonList(CurrentRateType.getStatus(bussinessType)));
                        MailSendParam mail = new MailSendParam(6L, Collections.singletonList(email), JSONObject.toJSONString(map));
                        log.debug("mail: {}", JSON.toJSONString(mail));
                        backFeignClient.sendMail(mail);
                    }
                } else if ("67".contains(bussinessType) && isSendEmail(cmsFlowpoolInfoCycle)) {
                    //只要流量池状态变了就下发邮件，不需要管卡状态
                    // 判断流量池状态在正常 -> 限速、正常 -> 停用状态之间发生变更时发送  流量池状态变更邮件
                    Map<String, List<String>> map = new HashMap<>(3);
                    map.put("{flowPoolName}", Collections.singletonList(cmsFlowpoolInfoCycle.getFlowPoolName()));
                    map.put("{oldStatus}", Collections.singletonList("正常"));
                    map.put("{newStatus}", Collections.singletonList(CurrentRateType.getStatus(bussinessType)));
                    MailSendParam mail = new MailSendParam(7L, Collections.singletonList(email), JSONObject.toJSONString(map));
                    log.debug("mail: {}", JSON.toJSONString(mail));
                    backFeignClient.sendMail(mail);
                }
            } catch (Exception e) {
                log.debug("发送邮件出现错误", e);
                log.debug("", e);
            }

            //限速类型
            //限速，标识位=2或4或6
            //恢复类型（单卡单周期恢复[重置、充值、恢复]）
            if (("2".equals(bussinessType) || "4".equals(bussinessType) || "6".equals(bussinessType) || resumeFlag) &&
                    ChannelCard.FlowPoolStatusEnum.PAUSE.getValue().equals(channelCard.getFlowPoolStatus())) {
                log.info("流量池卡为暂停状态，不执行后续流程逻辑，imsi：{}", flowPoolRabbitMQMessageVO.getHimsi());
                throw new GoodException("流量池卡为暂停状态，不执行后续流程逻辑");
            }

        } else {
            //限速类型
            //限速，标识位=2或4或6
            //恢复类型（单卡单周期恢复[重置、充值、恢复]）
            if (("2".equals(bussinessType) || "4".equals(bussinessType) || "6".equals(bussinessType) || resumeFlag) &&
                    ChannelCard.FlowPoolStatusEnum.PAUSE.getValue().equals(channelCard.getFlowPoolStatus())) {
                log.info("流量池卡为暂停状态，不执行后续流程逻辑，imsi：{}", flowPoolRabbitMQMessageVO.getHimsi());
                throw new GoodException("流量池卡为暂停状态，不执行后续流程逻辑");
            }
        }

    }

    /**
     * 是否发送电子邮件
     *
     * @param cmsFlowpoolInfoCycle 周期信息
     * @return boolean
     */
    private boolean isSendEmail(CmsFlowpoolInfoCycle cmsFlowpoolInfoCycle) {
        String key = "flowPool_status_change_" + cmsFlowpoolInfoCycle.getFlowPoolUniqueId();
        String value = redisTemplate.opsForValue().get(key);
        log.debug("value: {}", value);
        long time = DateUtil.between(new Date(), DateUtil.parse(cmsFlowpoolInfoCycle.getPoolExpireTime()), DateUnit.SECOND);
        if (value == null) {
            redisTemplate.opsForValue().set(key, "-1");
            redisTemplate.expire(key, time, TimeUnit.SECONDS);
            return true;
        } else {
            log.debug("已经发送过邮件了");
            return false;
        }
    }

    /**
     * 根据流量池周期状态更新卡状态
     *
     * @param flowPoolConsumerContext 上下文
     * @return String
     */
    protected String getFlowPoolCycle(FlowPoolConsumerContext flowPoolConsumerContext) {

        QueryWrapper<CmsFlowpoolInfoCycle> wrapper = new QueryWrapper<>();

        wrapper.eq("flow_pool_unique_id",
                flowPoolConsumerContext
                        .getFlowPoolRabbitMQMessage()
                        .getFlowPoolUniqueId());

        CmsFlowpoolInfoCycle cmsFlowpoolInfoCycle = cmsFlowpoolInfoCycleMapper.selectOne(wrapper);

        //当前控制逻辑流量池
        //1、达量限速
        //2、达量停用
        //3、正常
        String flowPoolRateStatus = cmsFlowpoolInfoCycle.getCurrentControl();
        log.info("流量池CurrentControl {}", flowPoolRateStatus);

        //单卡-单周期流量 flow_pool_cycle_{flowPollID}_{flowUniqeID}_{imsi} -> cms_flowpool_remain.flow_type  =1
//        String flowPoolCycleKey = "flow_pool_cycle_" + cmsFlowpoolInfoCycle.getFlowPoolId() + "_"
//                + flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getFlowPoolUniqueId() + "_"
//                + flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getHimsi();
//        String flowPoolCycle = redisTemplate.opsForValue().get(flowPoolCycleKey);
        Long flowPoolCycle = flowOperationUtils.getFlowPoolRemainFlow(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getFlowPoolUniqueId(),
                cmsFlowpoolInfoCycle.getFlowPoolId(),
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getHimsi(),
                "1");

        String flowPoolCycleStatus = "3";

        if (ObjectUtils.isNotEmpty(flowPoolCycle) && Double.parseDouble(flowPoolCycle.toString()) <= 0) {
            flowPoolCycleStatus = getStatus(flowPoolConsumerContext.getCurrentChannelCard().getRateType());
        }
        log.info("单卡单周期[{}]: {}", flowPoolCycleStatus, flowPoolCycle);


        //单卡-全周期流量 flow_pool_card_{flowPollID}_{flowUniqeID}_{imsi} -> cms_flowpool_remain.flow_type  =2
//        String flowPoolCardKey = "flow_pool_card_" + cmsFlowpoolInfoCycle.getFlowPoolId() + "_"
//                + flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getFlowPoolUniqueId() + "_"
//                + flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getHimsi();
//        String flowPoolCard = redisTemplate.opsForValue().get(flowPoolCardKey);
        Long flowPoolCard = flowOperationUtils.getFlowPoolRemainFlow(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getFlowPoolUniqueId(),
                cmsFlowpoolInfoCycle.getFlowPoolId(),
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getHimsi(),
                "2");

        String flowPoolCardStatus = "3";
        if (ObjectUtils.isNotEmpty(flowPoolCard) && Double.parseDouble(flowPoolCard.toString()) <= 0) {
            flowPoolCardStatus = getStatus(flowPoolConsumerContext.getCurrentChannelCard().getRateType());
        }
        log.info("单卡全周期[{}]状态: {}", flowPoolCardStatus, flowPoolCard);
        //客户与卡关系表 当前类型：
        //1：正常
        //2：单卡周期达量限速
        //3：单卡周期达量停用
        //4：单卡总量达量限速
        //5：单卡总量达量停用
        //6: 流量池总量达量限速
        //7：流量池总量达量停用

        if ("2".equals(flowPoolRateStatus) || "2".equals(flowPoolCycleStatus) || "2".equals(flowPoolCardStatus)) {
            flowPoolConsumerContext.setUpccSignId("");
            log.info("计划更新UPCC模板为空");
            if ("2".equals(flowPoolRateStatus)) {
                return "7";
            } else if ("2".equals(flowPoolCardStatus)) {
                return "5";
            } else {
                return "3";
            }
        } else if ("1".equals(flowPoolRateStatus) || "1".equals(flowPoolCycleStatus) || "1".equals(flowPoolCardStatus)) {
            flowPoolConsumerContext.setUpccSignId(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getUpccLimitsSignId());
            log.info("计划更新UPCC模板：限速模板");
            if ("1".equals(flowPoolRateStatus)) {
                return "6";
            } else if ("1".equals(flowPoolCardStatus)) {
                return "4";
            } else {
                return "2";
            }

        } else {
            flowPoolConsumerContext.setUpccSignId(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getUpccResumeSignId());
            log.info("计划更新UPCC模板：高速模板");
            return "1";
        }
    }

    boolean changeStatus(String oldStatus, String newStatus) {
        return !CurrentRateType.getStatus(oldStatus).equals(CurrentRateType.getStatus(newStatus));
    }

    String getStatus(String rateType) {
        switch (rateType) {
            case "2":
                return "1";
            case "3":
                return "2";
            default:
                return "3";
        }
    }


    /**
     * 插入cms_flowpool_consume_suc_log
     *
     * @param flowPoolConsumerContext 上下文
     */
    public void insertCmsFlowpoolConsumeSucLog(FlowPoolConsumerContext flowPoolConsumerContext) {

        FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessageVO =
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage();

        String bussinessType = flowPoolRabbitMQMessageVO.getCurrentRateType();

        CmsFlowpoolConsumeSucLog cmsFlowpoolConsumeSucLog =
                CmsFlowpoolConsumeSucLog
                        .builder()
                        .businessType(bussinessType)
                        .flowPoolUniqueId(flowPoolRabbitMQMessageVO.getFlowPoolUniqueId())
                        .iccid(flowPoolRabbitMQMessageVO.getIccid())
                        .imsi(flowPoolRabbitMQMessageVO.getImsi())
                        .internetType(flowPoolRabbitMQMessageVO.getCardType())
                        .msisdn(flowPoolRabbitMQMessageVO.getMsisdn())
                        .result("1")
                        .build();

        sucLogMapper.insert(cmsFlowpoolConsumeSucLog);

        flowPoolConsumerContext.setSucLogId(cmsFlowpoolConsumeSucLog.getId());

    }

    @Override
    public void callBack(FlowPoolConsumerContext flowPoolConsumerContext) throws InterruptedException {

        boolean isOutSideNetSuc;

        isOutSideNetSuc = tryOutsideNet(flowPoolConsumerContext);

        while (!isOutSideNetSuc) {

            int retryTime = flowPoolConsumerContext.getRetryTimes();

            if (retryTime >= resetTime) {

                if (this instanceof ResetStrategyForSingleCycle) {
                    //若是V卡
                    if ("2".equals(flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getCardType())) {

                        hvShareRepository.updateVcardUpccSignStatus(flowPoolConsumerContext
                                .getFlowPoolRabbitMQMessage()
                                .getImsi());

                    }
                    resetSingleCycleVCardRar(flowPoolConsumerContext);
                }

                throw new BizException("与外部网元交互的次数超过限制");
            }

            flowPoolConsumerContext.setRetryTimes(++retryTime);

            if (retryInterval > 0) {
                Thread.sleep(retryInterval);
            }
            isOutSideNetSuc = tryOutsideNet(flowPoolConsumerContext);

        }
        if (this instanceof ResetStrategyForSingleCycle) {
            resetSingleCycleVCardRar(flowPoolConsumerContext);
        }
    }

    /**
     * 单卡单周期重置V卡RAR流程
     *
     * @param flowPoolConsumerContext 上下文信息
     */
    private void resetSingleCycleVCardRar(FlowPoolConsumerContext flowPoolConsumerContext) {
        FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                .getFlowPoolRabbitMQMessage();
        // V卡类型 && 在用套餐一致 && 原始状态为限速 &&  非恢复操作
        if ("2".equals(messageVO.getCardType()) && useFlowPool(flowPoolConsumerContext)) {
            if (1 == flowPoolConsumerContext.getPriorityFromTable() && !"4".equals(messageVO.getTriggerType())) {
                // V卡存在会话校验 存在则下发RAR
                if (ccrCommonService.judgePgwSessionExists(messageVO.getImsi())) {
                    log.info("==================触发V卡RAR====================");
                    controlFeignClient.sendRAR(messageVO.getImsi());
                }
            }
        }
    }

    /**
     * 就是单纯的跟外部网元交互而已
     *
     * @param flowPoolConsumerContext 上下文
     */
    protected abstract boolean tryOutsideNet(FlowPoolConsumerContext flowPoolConsumerContext);

    public static void main(String[] args) {
        long time = DateUtil.between(new Date(), DateUtil.parse("20221115152300"), DateUnit.SECOND);
        System.out.println(time);
    }
}
