package com.ebupt.cmi.clientmanagement.consumer.cdr;


import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelRecordDetail;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.service.ChannelRecordDetailService;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collections;

@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues = "cdrProcess.delay.queue")
public class CDRProcessConsumer {

    @Autowired
    ChannelRecordDetailService channelRecordDetailService;

    @Resource
    CommonRepository commonRepository;

    @RabbitHandler
    public void process(String messageString, Channel channel, Message message) throws IOException {

        log.info("============================rabbitMQ收到消息{}==================================", messageString);
        if (commonRepository.getMessageById(message.getMessageProperties().getMessageId())){
            log.debug("该消息已被处理");
            return;
        }
        ChannelRecordDetail recordDetail =
                JSON.parseObject(messageString, ChannelRecordDetail.class);
        try {
            log.info("收到rabbitMq延迟消息，开始执行CDR话单入库操作");
            channelRecordDetailService.saveRecordDetails(Collections.singletonList(recordDetail));
            log.info("CDR延期处理队列流程结束");
        } catch (Exception e) {
            log.error("CDR延期处理队列处理失败", e);
            throw new BizException(e.getMessage());
        } finally {
            commonRepository.deleteMessage(message.getMessageProperties().getMessageId());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
