package com.ebupt.cmi.clientmanagement.domain.dto.through;

import com.ebupt.cmi.clientmanagement.domain.vo.through.QuotaNoticeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName QuotaNoticeDTO.java
 * @Description 流量通知接口的DTO
 * @createTime 2021年05月18日 14:32:00
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuotaNoticeDTO {

    String imsi;

    String iccid;

    String qtavalue;

    String timestamp;

    public QuotaNoticeDTO(QuotaNoticeVO quotaNoticeVO) {
        this.imsi = quotaNoticeVO.getImsi();
        this.iccid = quotaNoticeVO.getIccid();
        this.qtavalue = quotaNoticeVO.getQtavalue();
        this.timestamp = quotaNoticeVO.getTimestamp();
    }
}
