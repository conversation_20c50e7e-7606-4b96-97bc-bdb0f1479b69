package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.factory;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HVShareStrategyFactory.java
 * @Description HV流量共享策略模式工厂
 * @createTime 2022年02月28日 16:07:00
 */
@Component
public class HVShareStrategyFactory {

    @Autowired
    Map<String, AbstractStrategy> strategys = new ConcurrentHashMap<>(10);

    public AbstractStrategy getStrategy(String component) {
        AbstractStrategy strategy = strategys.get(component);
        if(strategy == null) {
            throw new RuntimeException("no strategy defined");
        }
        return strategy;
    }
}
