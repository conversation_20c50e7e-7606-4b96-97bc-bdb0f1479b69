package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否终端厂商套餐枚举
 * <AUTHOR>
 * @date 2021-5-27 19:50:18
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PackageIsTerminalEnum {

    /**
     * 是
     */
    YES("1"),

    /**
     * 否
     */
    NO("2");

    private String value;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

}
