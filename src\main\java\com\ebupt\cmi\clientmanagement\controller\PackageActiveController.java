package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.domain.dto.ActivePackageDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.InactivePackageDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.StatUnactivatedPackageMonth;
import com.ebupt.cmi.clientmanagement.domain.entity.StatUsedPackageDay;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.service.PackageActiveService;
import com.ebupt.cmi.clientmanagement.utils.CsvExportUtil;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/packageActive")
@Api(tags = "套餐激活记录查询")
@RefreshScope
@RequiredArgsConstructor
public class PackageActiveController {


  public final PackageActiveService packageActiveService;

  private final String format = "yyyy-MM-dd HH:mm:ss";

    @PostMapping("/globalPackage/pageList")
    @ApiOperation(value = "全球卡普通套餐分页查询")
    public Response<List<ActivePackageDTO>> normalPageList(@RequestBody @Valid PackageActiveSearchVO form) {
          Date startDate = null ;
          Date endDate = null ;

          try {

            if(form.getStartDate()!= null  && !"".equals(form.getStartDate().trim())){
              startDate = new SimpleDateFormat(format).parse(form.getStartDate().trim()+" 00:00:00");
            }

            if(form.getEndDate()!= null  && !"".equals(form.getEndDate().trim())){
              endDate = new SimpleDateFormat(format).parse(form.getEndDate().trim()+" 23:59:59");
            }

          } catch (ParseException e) {
            return Response.error("传入时间格式不正确");
          }


      return Response.ok(packageActiveService.getGlobalPackagePageList(startDate,endDate,
                form.getPage(),form.getPageSize()));
    }

    @PostMapping("/offlinePackage/pageList")
    @ApiOperation(value = "终端厂商线下模式套餐分页查询")
    public Response<List<ActivePackageDTO>> offlinePageList(@RequestBody @Valid PackageActiveSearchVO form) {

      Date startDate = null ;
      Date endDate = null ;

      try {

        if(form.getStartDate()!= null  && !"".equals(form.getStartDate().trim())){
          startDate = new SimpleDateFormat(format).parse(form.getStartDate().trim()+" 00:00:00");
        }

        if(form.getEndDate()!= null  && !"".equals(form.getEndDate().trim())){
          endDate = new SimpleDateFormat(format).parse(form.getEndDate().trim()+" 23:59:59");
        }

      } catch (ParseException e) {
        return Response.error("传入时间格式不正确");
      }

      return Response.ok(packageActiveService.getSpecialPackagePageList("8","2",form.getCorpName(),startDate,endDate,
                form.getPage(),form.getPageSize()));
    }

    @PostMapping("/onlinePackage/pageList")
    @ApiOperation(value = "终端厂商线上模式套餐分页查询")
    public Response<List<ActivePackageDTO>> onlinePageList(@RequestBody @Valid PackageActiveSearchVO form) {
      Date startDate = null ;
      Date endDate = null ;
      try {

        if(form.getStartDate()!= null  && !"".equals(form.getStartDate().trim())){
          startDate = new SimpleDateFormat(format).parse(form.getStartDate().trim()+" 00:00:00");
        }

        if(form.getEndDate()!= null  && !"".equals(form.getEndDate().trim())){
          endDate = new SimpleDateFormat(format).parse(form.getEndDate().trim()+" 23:59:59");
        }

      } catch (ParseException e) {
        return Response.error("传入时间格式不正确");
      }
        return Response.ok(packageActiveService.getSpecialPackagePageList("7","5",form.getCorpName(),startDate,endDate,
                form.getPage(),form.getPageSize()));
    }

    @PostMapping("/cooperationPackage/pageList")
    @ApiOperation(value = "合作运营商分页查询")
    public Response<List<ActivePackageDTO>> cooperatePageList(@RequestBody @Valid PackageActiveSearchVO form) {
      Date startDate = null ;
      Date endDate = null ;
      try {

        if(form.getStartDate()!= null  && !"".equals(form.getStartDate().trim())){
          startDate = new SimpleDateFormat(format).parse(form.getStartDate().trim()+" 00:00:00");
        }

        if(form.getEndDate()!= null  && !"".equals(form.getEndDate().trim())){
          endDate = new SimpleDateFormat(format).parse(form.getEndDate().trim()+" 23:59:59");
        }

      } catch (ParseException e) {
        return Response.error("传入时间格式不正确");
      }
        return Response.ok(packageActiveService.getSpecialPackagePageList("3","1",form.getCorpName(),startDate,endDate,
                form.getPage(),form.getPageSize()));
    }

    @PostMapping("/usedPackageStat")
    @ApiOperation(value = "可用套餐统计查询接口")
    public Response<List<UsedPackageStatVO>> avilablePackageList(@ApiParam("日期 :yyyy-MM-dd") @RequestParam ("statDate") String statDate  ) {
        statDate = statDate.replace("-","");
        return Response.ok(packageActiveService.getusedPackageStat( statDate ));

    }

  @ApiOperation(value = "使用过有/无可用套餐列表导出")

  @PostMapping("/usedPackageStat/export")
  @NormalLog
  public void exportUsedPackageStat(@ApiParam("日期 :yyyy-MM-dd") @RequestParam String statDate,@ApiParam("1：有可用套餐，2：无可用套餐") @RequestParam String type, HttpServletResponse response) {
    try {
      statDate = statDate.replace("-","");
      packageActiveService.ExportAvailablePackage(statDate,type,response);
    } catch (Exception e) {
      log.warn("导出失败" + e.getMessage(), e);
    }
  }

  @PostMapping("/UnactivatedPackage")
  @ApiOperation(value = "未激活套餐统计查询接口")
  public Response<List<UnactivatedPackageStatVO>> getUnactivatedPackageList(@ApiParam("日期 ：:yyyy-MM") @RequestParam String statDate  ) {
    statDate = statDate.replace("-","");
    return Response.ok(packageActiveService.getcountUnactivatedPackage(statDate ));

  }

  @ApiOperation(value = "未激活套餐统计导出")

  @PostMapping("/unactivatedPackageStat/export")
  @NormalLog
  public void exportUsedPackageStat(@ApiParam("日期： yyyy-MM") @RequestParam String statDate,  HttpServletResponse response) {
    try {
      statDate = statDate.replace("-","");
      //套餐分页查询
      List<StatUnactivatedPackageMonth> result = packageActiveService.getUnactivatedPackage(statDate);
      // 构造导出数据结构
      // 设置表头
      String titles = "统计时间,imsi,msisdn,iccid,创建时间 ,更新时间";
      // 设置每列字段
      String keys = "statTime,imsi,msisdn,iccid,createTime,updateTime";

      // 构造导出数据
      List<Map<String, Object>> datas = new ArrayList<>();
      Map<String, Object> map;
      if (result != null) {
        for (StatUnactivatedPackageMonth data : result) {
          map = new HashMap<>();
          map.put("statTime", data.getStatTime());
          map.put("imsi", data.getImsi()+"\t");
          map.put("msisdn", data.getMsisdn()+"\t");
          map.put("iccid", data.getIccid()+"\t");
          map.put("createTime", DateUtil.format(data.getCreateTime(), "yyyy-MM-dd HH:mm:ss")+"\t");
          map.put("updateTime", DateUtil.format(data.getUpdateTime(), "yyyy-MM-dd HH:mm:ss")+"\t");
          datas.add(map);
        }
      }
      // 设置导出文件前缀
      String fName = "未激活套餐统计列表";
      // 文件导出
      OutputStream os = response.getOutputStream();
      CsvExportUtil.responseSetProperties(fName, response);
      CsvExportUtil.doExportForUTF8(datas, titles, keys, os);
      os.close();
    } catch (Exception e) {
      log.warn("导出失败" + e.getMessage(), e);
    }

  }

  @PostMapping("/activatedPackageStat")
  @ApiOperation(value = "激活统计查询接口")
  public Response<List<ActiveMccStatVO>> getactivatedPackageList(@RequestBody @Valid ActiveStatSearchVO form) {
      try {
        return Response.ok( packageActiveService.getActiveMccStat(form));
      }catch (Exception e){
        e.printStackTrace();
        return  Response.error(e.getMessage());
      }
  }

  @PostMapping("/activatedPackageStatExport")
  @ApiOperation(value = "激活统计查询导出接口")
  public void doActivatedPackageListExport(@RequestBody @Valid ActiveStatSearchVO form, HttpServletResponse response) {
    packageActiveService.doActivatedPackageListExport(form,response);
  }

  @PostMapping("/globalPackageSearchExport")
  @ApiOperation(value = "全球卡普通套餐查询导出接口")
  public void globalPackageSearchExport(@RequestBody @Valid PackageActiveSearchVO form, HttpServletResponse response) {
    packageActiveService.globalPackageSearchExport(form,response);
  }

  @PostMapping("/offlinePackageSearchExport")
  @ApiOperation(value = "终端厂商线下模式套餐查询导出接口")
  public void offlinePackageSearchExport(@RequestBody @Valid PackageActiveSearchVO form, HttpServletResponse response) {
    packageActiveService.offlinePackageSearchExport(form,response);
  }

  @PostMapping("/onlinePackageSearchExport")
  @ApiOperation(value = "终端厂商线上模式套餐导出接口")
  public void onlinePackageSearchExport(@RequestBody @Valid PackageActiveSearchVO form, HttpServletResponse response) {
    packageActiveService.onlinePackageSearchExport(form,response);
  }

  @PostMapping("/cooperationPackageSearchExport")
  @ApiOperation(value = "合作运营商查询导出接口")
  public void cooperationPackageSearchExport(@RequestBody @Valid PackageActiveSearchVO form, HttpServletResponse response) {
    packageActiveService.cooperationPackageSearchExport(form,response);
  }

}
