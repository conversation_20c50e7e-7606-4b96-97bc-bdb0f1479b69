package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 通知URL后缀配置
 * <AUTHOR>
 * @date 2021-6-3 15:24:33
 */
@Data
@Component
@ConfigurationProperties("notice-config")
public class NoticeConfigProperties {

    /**
     * 通知URL后缀
     */
    private Suffix urlSuffix;

    /**
     * H5的corpId
     */
    private String corpId;

    @Data
    public static class Suffix {

        /**
         * 到期
         */
        private String overdue;

        /**
         * 退订
         */
        private String unsubscribe;

    }

}
