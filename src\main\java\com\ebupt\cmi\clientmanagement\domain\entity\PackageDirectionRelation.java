package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * @Desc
 * <AUTHOR> lingsong
 * @Date 2023/12/27 15:59
 */
@Data
@Builder
@TableName("cms_package_direction_relation")
@AllArgsConstructor
@NoArgsConstructor
public class PackageDirectionRelation {

    private Long id;
    private String packageId;
    private String packageUniqueId;
    private String appGroupId;
    private String directType;
    private String isUsePackage;
    private Long flowLimitSum;
    //定向应用的状态
    private String hasUsed;
    private Date createTime;
    private Date updateTime;



    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum DirectType {

        LIMIT_SPEED("1"),

        FREE_FLOW("2");

        @Getter
        private final String value;

    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum Status {

        NOT_USED("1"),

        USING("2"),

        HAS_USED("3");

        @Getter
        private final String value;
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum IsUsePackage {

        YES("1"),

        NO("2");


        @Getter
        private final String value;
    }


}
