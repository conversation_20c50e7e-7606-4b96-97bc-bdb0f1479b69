package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Date;

/**
 * @Desc
 * <AUTHOR> lingsong
 * @Date 2024/06/26 16:25
 */
@Data
@Accessors(chain = true)
@TableName("jms_atz_charging")
public class AtzCharging {

	@TableId
	private Long id;

	private String name;

	private String flowUnit;

	private LocalDate effectiveDate;

	private String status;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
