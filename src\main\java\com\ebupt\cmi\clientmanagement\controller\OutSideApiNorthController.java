package com.ebupt.cmi.clientmanagement.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.ebupt.cmi.clientmanagement.domain.response.through.BaseResult;
import com.ebupt.cmi.clientmanagement.domain.response.through.ImsiResult;
import com.ebupt.cmi.clientmanagement.domain.response.through.QuotaRes;
import com.ebupt.cmi.clientmanagement.domain.vo.through.*;
import com.ebupt.cmi.clientmanagement.service.OutSideApiService;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OutSideApiNorthController.java
 * @Description 对外提供北向接口
 * @createTime 2021年05月19日 14:33:00
 */
@RestController
@RequestMapping("/through")
@Api(tags = "透传接口,北向")
@Slf4j
public class OutSideApiNorthController {

    @Autowired
    OutSideApiService outSideApiService;

    @ApiOperation(value = "激活通知接口，北向接口，提供给别人", notes = "激活通知接口")
    @PostMapping("/SBO_notify_activation/v1")
    @NormalLog
    public BaseResult notifyActivationNorth(@RequestBody NotifyActivationNorthVO notifyVO) {
        log.info("-----------激活通知接口参数打印" + notifyVO.toString() + "---------------------");
        if (StringUtil.isBlank(notifyVO.getIccid()) && StringUtil.isBlank(notifyVO.getImsi())) {
            return BaseResult.error("iccid与imsi不能同时为空");
        }

        return outSideApiService.notifyActivationNorth(notifyVO) ? BaseResult.ok("成功") : BaseResult.error("操作失败，请过会儿重试");
    }

    @ApiOperation(value = "到期通知接口，北向接口，提供给别人", notes = "到期通知接口")
    @PostMapping("/SBO_notify_expire/v1")
    @NormalLog
    public BaseResult notifyExpireNorth(@RequestBody NotifyExpireNorthVO notifyVO) {

        log.info("-----------到期通知接口参数打印" + notifyVO.toString() + "---------------------");

        return outSideApiService.notifyExpireNorth(notifyVO) ? BaseResult.ok("成功") : BaseResult.error("操作失败，请过会儿重试");

    }

    @ApiOperation(value = "IMSI状态修改接口", notes = "IMSI状态修改接口")
    @PostMapping("/SBO_imsistatu_update/v1")
    @NormalLog
    public ImsiResult imsistatuUpdate(@RequestBody ImsistatuUpdateVO imsistatuUpdateVO) {
        try {
            return outSideApiService.imsistatuUpdate(imsistatuUpdateVO);
        } catch (Exception e) {
            log.error("", e);
            ImsiResult imsiResult = new ImsiResult();
            imsiResult.setCode("9999999");
            imsiResult.setDescription("系统异常，请求失败");
            return imsiResult;
        }
    }

    @ApiOperation(value = "UPCC查询流量接口", notes = "透传查询流量接口")
    @PostMapping("/APP_getSubscriberAllQuota_SBO/v1")
    @NormalLog
    public QuotaRes getSubscriberAllQuota(@RequestBody QuotaVO quotaVO) {
        return outSideApiService.getSubscriberAllQuotaAll(quotaVO);
    }
}
