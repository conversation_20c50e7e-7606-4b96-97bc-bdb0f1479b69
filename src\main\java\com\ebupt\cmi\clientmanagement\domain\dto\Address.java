package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Address
 *
 * @Author: zhaoqiankun
 * @Date: 2021/5/8 16:33
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class Address {

    private String id;

    private String phoneNumber;

    private String mccCode;

    private String postCode;

    private String countryName;

    private String email;

    private String province;

    private String cityName;

    @JsonProperty("mAddress")
    private String mAddress;

    private String addressee;

    @JsonProperty("mAddress")
    public String getmAddress() {
        return mAddress;
    }
}
