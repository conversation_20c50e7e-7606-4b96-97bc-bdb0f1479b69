package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * (CmsFlowpoolRemain)实体类
 *
 * <AUTHOR>
 * @since 2024-04-08 15:11:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("cms_flowpool_remain_temp")
public class CmsFlowpoolRemainTemp {
/**
     * 主键
     */
    private Long id;
/**
     * 剩余流量类型1：单卡单周期剩余流量2：单卡全周期剩余流量3：流量池剩余流量
     */
    private String flowType;
/**
     * 归属的流量池唯一id
     */
    private String flowUniqueId;
/**
     * 流量池ID
     */
    private String flowPoolId;
/**
     * imsi
     */
    private String imsi;
/**
     * 剩余流量
     */
    private Long remainFlow;
/**
     * 过期时间
     */
    private Long expireTime;

}

