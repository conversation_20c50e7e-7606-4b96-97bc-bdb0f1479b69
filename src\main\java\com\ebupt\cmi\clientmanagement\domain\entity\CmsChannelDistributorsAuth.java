package com.ebupt.cmi.clientmanagement.domain.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsChannelDistributorsAuth对象", description="")
public class CmsChannelDistributorsAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    private String corpId;

    @ApiModelProperty(value = "是否允许订购")
    private String isSub;

    @ApiModelProperty(value = "渠道商编码")
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String channelCode;

    @ApiModelProperty(value = "渠道商通知url前缀")
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String channelUrl;

    @ApiModelProperty(value = "app_key")
    private String appKey;

    @ApiModelProperty(value = "app_secret")
    private String appSecret;

    @ApiModelProperty(value = "押金重置开关	1：开 	2：关")
    private String depositeReset;

    @ApiModelProperty(value = "押金重置金额，单位分")
    private BigDecimal resetPrice;

    private BigDecimal deposit;

//    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal depositeRemindThreshold;

    @ApiModelProperty(value = "币种编码")
    private String currencyCode;

    @ApiModelProperty(value = "折扣0-100，type=4时不为空")
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer discount;

    @ApiModelProperty(value = "合约开始时间")
    private Date contractStartTime;

    @ApiModelProperty(value = "合约结束时间")
    private Date contractEndTime;

    @ApiModelProperty(value = "合约期承诺金额")
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "直接比例0-100")
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer directRatio;

    @ApiModelProperty(value = "间接类型 	1:按次数 	2：按年限")
    private Integer indirectType;

    @ApiModelProperty(value = "简介比例")
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer indirectRatio;

    private Integer indirectCount;

    private String email;

    private Integer accountNum;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "渠道类型 1.押金模式 2.预存模式")
    private String channelType;

    @ApiModelProperty(value = "总额度")
    private BigDecimal totalDeposit;

    @ApiModelProperty(value = "激活通知开关 1:开  2:关")
    private String activateNotification;

    @ApiModelProperty(value = "退订规则 1:自然月内退订  2:有效期内退订")
    private String unsubscribeRule;

    @ApiModelProperty(value = "激活通知URL")
    private String activateNotificationUrl;

    @ApiModelProperty(value = "是否允许自建套餐")
    private String allowNewPackage;

    @ApiModelProperty(value = "自建套餐上限")
    private Integer limitPackageNum;

    @ApiModelProperty(value = "国家关联卡池组id")
    private Long groupId;

    @ApiModelProperty(value = "套餐使用率提醒阈值")
    private String packageUsePercentage;

    /**
     * 渠道商合作模式
     * 1  代销
     * 2  A2Z
     * */
    private String channelCooperationMode;

    /**
     * 额度用尽提醒阈值
     * */
    private BigDecimal runoutofBalanceRemindThreshold;

    /**
     * 禁止购买提醒阈值
     * */
    private BigDecimal prohibitiveBuyRemindThreshold;

    /**
     * 停止使用提醒阈值
     * */
    private BigDecimal stopUseRemindThreshold;

    /**
     * esim通知开关
     * 1：开
     * 2：关
     * 默认值：2
     */
    private String esimNotification;

    /**
     * esim通知url
     */
    private String esimNotificationUrl;

    private String approvalPackage;

    private Long distributionAccountingPeriodId;

    private Long a2zAccountingPeriodId;

    private Long resourceAccountingPeriodId;

    private BigDecimal resourceRunoutofBalanceRemindThreshold;

    private BigDecimal resourceProhibitiveBuyRemindThreshold;

    private BigDecimal resourceStopUseRemindThreshold;

    private String a2zChannelType;

    private String resourceChannelType;

    private BigDecimal marketingAmount;

    private BigDecimal creditAmount;

    private BigDecimal a2zMarketingAmount;
}
