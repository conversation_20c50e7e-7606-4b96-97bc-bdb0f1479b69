package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsBigOrder对象", description="")
@NoArgsConstructor
@AllArgsConstructor
public class CmsBigOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "订单唯一id")
    private String orderUniqueId;

    @ApiModelProperty(value = "订单数量")
    private Long count;

    public CmsBigOrder(String orderUniqueId, Long count) {
        this.orderUniqueId = orderUniqueId;
        this.count = count;
    }
}
