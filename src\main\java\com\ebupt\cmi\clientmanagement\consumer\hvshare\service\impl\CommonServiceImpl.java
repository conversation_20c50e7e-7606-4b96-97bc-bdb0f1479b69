package com.ebupt.cmi.clientmanagement.consumer.hvshare.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.service.CommonService;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.service.SmgService;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.EopAccessDetail;
import com.ebupt.cmi.clientmanagement.domain.properties.ActiveNotificationProps;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.domain.response.through.BaseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.AccessNotify;
import com.ebupt.cmi.clientmanagement.domain.vo.MappingImsiQueryReturnVO;
import com.ebupt.cmi.clientmanagement.domain.vo.through.NotifyActivationVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardVO;
import com.ebupt.cmi.clientmanagement.feign.rms.RmsFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.EopAccessDetailMapper;
import com.ebupt.cmi.clientmanagement.service.HssService;
import com.ebupt.cmi.clientmanagement.service.OutSideApiService;
import com.ebupt.cmi.clientmanagement.utils.DateUtilWrapper;
import feign.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonServiceImpl.java
 * @Description
 * @createTime 2022年03月09日 16:41:00
 */

@Component
@Slf4j
public class CommonServiceImpl implements CommonService {
    @Autowired
    SmgService smgService;

    @Autowired
    HvShareRepository hvShareRepository;

    @Autowired
    EopAccessDetailMapper eopAccessDetailMapper;

    @Autowired
    OutSideApiService outSideApiService;

    @Autowired
    RmsFeignClient rmsFeignClient;

    @Autowired
    ActiveNotificationProps activeNotificationProps;

    @Autowired
    HssService hssService;

    /**
     * 是否激活通知：1是
     */
    private final String ACTIVE_NOTIFY_TYPE = "1";

    @Override
    public void sendSmg(ChannelPackageCard channelPackageCard) {
        CardVO cardVO = hvShareRepository.getOneCard(channelPackageCard.getImsi());

        String packageName;

        //1：中文-繁体
        //2：英文
        //3：简体
        switch (cardVO.getSendLang()) {
            case "1":
                packageName = channelPackageCard.getNameTw();
                break;
            case "2":
                packageName = channelPackageCard.getNameEn();
                break;
            case "3":
                packageName = channelPackageCard.getPackageName();
                break;
            default:
                packageName = channelPackageCard.getPackageName();
                break;
        }

        smgService.sendActivatedSms(cardVO, channelPackageCard.getPackageUniqueId(),
                packageName, new Date(), channelPackageCard.getExpireTime());

    }

    @Override
    public void loginNotification(String vimsi, String corpId, boolean spec, String mcc) {
        try {

            EopAccessDetail eopAccessDetail = eopAccessDetailMapper.selectOne(Wrappers.<EopAccessDetail>lambdaQuery()
                    .eq(EopAccessDetail::getCorpId, corpId));
            if (eopAccessDetail == null) {
                log.warn("[签约后置处理] [登网通知] 失败，未查询到能力接入数据,corpId={}", corpId);
                return;
            }
            log.debug("[签约后置处理] eopAccessDetail={}", eopAccessDetail);
            // 通知url前缀
            String notifyUrl = eopAccessDetail.getNotifyUrl();
            String activeNotifyType = eopAccessDetail.getActiveNotifyType();
            // activeNotifyType='1'通知
            log.debug("activeNotifyType === > {}", activeNotifyType);
            if (ACTIVE_NOTIFY_TYPE.equals(activeNotifyType)) {
                log.debug("[签约后置处理] 开始进行登网通知");
                List<Map<String, String>> specConfigs = activeNotificationProps.getSpec();
                // 特殊厂家通知
                String imsi = vimsi;
//                String mappingImsi = rmsFeignClient.getMappingImsi(imsi).getData().toString();

                Response<List<MappingImsiQueryReturnVO>> response4QueryMappingImsi = rmsFeignClient.query(imsi, null, null, -1, -1);
                if (!ResponseResult.SUCCESS.getCode().equals(response4QueryMappingImsi.getCode())) {
                    //查询三元组失败
                    throw new BizException("查询三元组失败");
                }
                String mappingImsi = null;
                if (response4QueryMappingImsi.getData().size() > 0) {
                    MappingImsiQueryReturnVO returnVO = response4QueryMappingImsi.getData().get(0);
                    mappingImsi = returnVO.getMappingImsi();
                }
                if (!Util.isBlank(mappingImsi)) {
                    log.debug("========调用RMS资源管理服务，替换imsi为mappingImsi，imsi：{} mappingImsi：{}================", imsi, mappingImsi);
                    imsi = mappingImsi;
                }

                if (spec) {
                    // 获取url后缀
                    log.debug("获取通知URL");
                    String urlSuffix = specConfigs.stream().filter(item -> item.get("corp-id").equals(corpId))
                            .map(item -> item.get("url-suffix")).findFirst().orElse(null);
                    log.debug("通知URL后缀：{},拼接结果===》URL：{}", urlSuffix, notifyUrl + urlSuffix);
                    if (StringUtils.hasText(urlSuffix)) {
                        log.debug("[签约后置处理] [登网通知] 开始进行特殊厂家通知，厂家数量为:{}", specConfigs.size());
                        String url = (notifyUrl + urlSuffix).replace(" ", "");
                        Boolean result = hssService.accessNotify(AccessNotify.builder()
                                .param(AccessNotify.AccessNotifyParam.builder()
                                        .accessTime(System.currentTimeMillis())
                                        .imsi(imsi)
                                        .accessPosition(mcc)
                                        .build())
                                .notifyUrl(url)
                                .build(), url);
                        log.debug("[签约后置处理] [登网通知] 特殊厂商通知{}", result ? "成功" : "失败");
                    }
                    // 一般厂家通知
                    else {
                        log.debug("一般厂家通知流程——1");
                        doCommonActiveNotification(imsi, mcc, eopAccessDetail);
                    }
                }
                // 一般厂家通知
                else {
                    log.debug("一般厂家通知流程——2");
                    doCommonActiveNotification(imsi, mcc, eopAccessDetail);
                }
            } else {
                log.debug("[签约后置处理] [登网通知] 通知类型不为1，不通知. activeNotifyType={}", activeNotifyType);
            }
        } catch (Exception e) {
            log.debug("[登网通知流程] 捕获异常，不影响整体流程", e);
        }
    }

    private void doCommonActiveNotification(String imsi, String mcc, EopAccessDetail eopAccessDetail) {
        log.debug("[普通流程通知URL前缀:{}]", eopAccessDetail.getNotifyUrl());
        String url = (eopAccessDetail.getNotifyUrl() + activeNotificationProps.getCommon().get("url-suffix")).replace(" ", "");
        NotifyActivationVO notifyActivationVO = NotifyActivationVO.builder()
                .imsi(imsi)
                .mcc(mcc)
                .netTime(DateUtilWrapper.getFormatNowTime())
                .url(url)
                .build();
        try {
            log.debug("[签约后置处理] [登网通知] 通知请求参数:{}", notifyActivationVO);
            log.debug("[签约后置处理] [登网通知] 调用接口进行通知");
            BaseResult baseResult = outSideApiService.notifyActivation(notifyActivationVO);
            log.debug("[签约后置处理] [登网通知] 通知结果:{}", baseResult);
        } catch (Exception e) {
            log.warn("[签约后置处理] [登网通知] 通知失败", e);
        }
    }
}
