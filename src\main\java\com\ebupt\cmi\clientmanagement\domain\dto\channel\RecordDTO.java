package com.ebupt.cmi.clientmanagement.domain.dto.channel;
import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * RecordDTO
 *
 * @Author: zhaoqiankun
 * @Date: 2021/6/16 16:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RecordDTO {

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订购日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String orderDate;

    @ApiModelProperty(value = "ICCID")
    private String iccid;

    @ApiModelProperty(value = "购买套餐")
    private String packageName;

    @ApiModelProperty(value = "购买渠道")
    private String orderChannel;

    @ApiModelProperty(value = "购买主体")
    private String type;

    @ApiModelProperty(value = "套餐激活时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String activeTime;

    @ApiModelProperty(value = "金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private String amount;

    @ApiModelProperty(value = "币种")
    private String currencyCode;


}
