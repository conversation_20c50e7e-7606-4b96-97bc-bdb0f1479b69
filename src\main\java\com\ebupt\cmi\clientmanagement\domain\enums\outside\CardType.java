package com.ebupt.cmi.clientmanagement.domain.enums.outside;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 卡片类型
 * @<PERSON> h<PERSON> l<PERSON>ong
 * @Date 2021/4/21 16:54
 */
@Getter
@AllArgsConstructor
public enum CardType {

    /**
     * 普通卡
     */
    ORDINARY("1"),

    /**
     * 省移动
     */
    PROVINCIAL_MOBILE("2"),

    /**
     * 后付费卡
     */
    POSTPAID("3");

    private String type;
}
