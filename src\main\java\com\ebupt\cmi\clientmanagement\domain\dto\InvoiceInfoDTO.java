package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/15 10:22
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceInfoDTO {
    private String companyName;
    private String companyAddress;
    private Integer quantity;
    private String invoiceNo;
    private String currency;

    public void setCurrency(String currency) {
        switch (currency) {
            case "156":
                this.currency = "CNY";
                break;
            case "344":
                this.currency = "HKD";
                break;
            case "840":
                this.currency = "USD";
                break;
            default:
                this.currency = "";
        }
    }
}
