package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.enums.IncludeCardEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/7/9 10:33
 */



@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("H5订单查询返回详情")
public class H5OrderDetailDTO {

    private String status;

    private String msg;

    private Obj obj;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Obj{
        private String total;

        private List<Orders> orders;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Orders{
        /**
         * 订单ID
         */
        @ApiModelProperty("订单ID")
        private String uuid;

        /**
         * 订单编号
         */
        @ApiModelProperty("订单编号")
        private String orderNumber;

        /**
         * 订单号的交易币种
         */
        @ApiModelProperty("订单号的交易币种")
        private String orderCurrency;

        /**
         * 订单状态
         */
        @ApiModelProperty("订单状态")
        private String orderType;

        /**
         * 支付时间
         */
        @ApiModelProperty("支付时间")
        private Date payTime;

        /**
         * 订单生成时间
         */
        @ApiModelProperty("订单生成时间")
        private Date orderTime;


        private Integer includeCard;

        /**
         * 是否包含硬卡
         * 0：不包含 是否包含硬卡
         * 1：包含
         * 2: esim
         */
        @ApiModelProperty("是否包含硬卡")
        private String cardType;

        /**
         * 支付方式，OceanPay网页支付 WeChatPay_Web Asipa
         */
        @ApiModelProperty("支付方式")
        private String methods;

        /**
         * 银行卡号（Asipa支付）
         */
        @ApiModelProperty("银行卡号")
        private String cardNo;

        /**
         * 购买渠道
         */
        @ApiModelProperty("购买渠道")
        private String buyType;

        /**
         * 套餐ID
         */
        @ApiModelProperty("套餐ID")
        private String productId;

        /**
         * 套餐名字
         */
        @ApiModelProperty("套餐名字")
        private String productName;

        /**
         * 套餐价格
         */
        @ApiModelProperty("套餐价格")
        private Double productAmount;

        /**
         * 套餐图片
         */
        @ApiModelProperty("套餐图片")
        private String productPic;

        /**
         * 套餐介绍
         */
        @ApiModelProperty("套餐介绍")
        private String productDetial;

        /**
         * eSim
         */
        @ApiModelProperty("eSim")
        private String iccd;

        /**
         * 物流编号
         */
        @ApiModelProperty("物流编号")
        private String logisticsNo;

        /**
         * 物流公司
         */
        @ApiModelProperty("物流公司")
        private String logisticsCom;

        /**
         * 套餐数量
         */
        @ApiModelProperty("套餐数量")
        private Integer productNum;

        /**
         * 收货人ID
         */
        @ApiModelProperty("收货人ID")
        private String personId;

        /**
         * 支付订单ID
         */
        @ApiModelProperty("支付订单ID")
        private String payId;

        /**
         * 订单状态描述
         */
        @ApiModelProperty("订单状态描述")
        private String payResult;

        private Consignee consignee;

        /**
         * 支付方式
         */
        private String type;

        private Date synDate;

        private String orderCountry;

        /**
         * 登录帐号
         */
        @ApiModelProperty("登录帐号")
        public String loginUser;
        /**
         * 登录信息
         */
        @ApiModelProperty("登录信息")
        public int isTemporary;

        /**
         * 是否激活
         */
        @ApiModelProperty("是否激活")
        public int isActivate;
        /**
         * 同步错误信息
         */
        @ApiModelProperty("同步错误信息")
        public String snyErro;
        /**
         * 同步成功回传订单号
         */
        @ApiModelProperty("同步成功回传订单号")
        public String synOrderid;
        /**
         * 退款失败原因
         */
        @ApiModelProperty("退款失败原因")
        public String backPayreason;

        /**
         * 订单提交语言
         */
        @ApiModelProperty("订单提交语言")
        public String orderLanguage;

        /**
         * 是否激活
         * @return
         */
        @ApiModelProperty("是否激活")
        public String isBind;

        public  String oldorderid;

        /**
         * 退款时间
         */
        @ApiModelProperty("退款时间")
        public Date backDate;

        /**
         * 结算币种
         */
        @ApiModelProperty("结算币种")
        public String orderPayType;

        /**
         * 商品原价
         */
        @ApiModelProperty("商品原价")
        public String oldPrice;

        /**
         * 商品折扣
         */
        @ApiModelProperty("商品折扣")
        public String acount;

        /**
         * 电话头
         */
        @ApiModelProperty("电话头")
        public String billingPhoneTel;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Consignee{
        @ApiModelProperty("")
        private String uuid;

        /**
         * 是否设置为注册账号
         */
        @ApiModelProperty("是否设置为注册账号")
        private int lsLogonNo;

        /**
         * 消费者的账单国家，采用国际标准ISO 3166代码，如：美国 — US，如果没有该值必须传： N/A
         */
        @ApiModelProperty("消费者的账单国家")
        private String billingCountry;

        /**
         * 消费者的电话如果没有该值必须传：N/A
         */
        @ApiModelProperty("消费者的电话")
        private String billingPhone;

        /**
         * 省份
         */
        @ApiModelProperty("省份")
        private String billingProvince;

        /**
         * 消费者的城市，如果没有该值可传：N/A
         */
        @ApiModelProperty("消费者的城市")
        private String billingCity;

        /**
         * 消费者的详细地址，billing_address
         */
        @ApiModelProperty("消费者的详细地址")
        private String billingAddress;

        /**
         * 消费者的姓如果没有该值必须传：消费者id或N/A传值时须去首尾空格，对特殊字符(‘“ < >)进行转义，否则会影响signValue校验
         */
        @ApiModelProperty("消费者的姓")
        private String billingFirstName;

        /**
         *
         */
        @ApiModelProperty("消费者名")
        private String billingLastName;

        /**
         * 消费者的邮箱，如果没有该值可默认传：消费者id@域名或简称.com传值时须去首尾空格，对特殊字符(‘“ < >)进行转义，否则会影响signValue校验
         */
        @ApiModelProperty("消费者的邮箱")
        private String billingEmail;

        private String billingZip;
    }
}
