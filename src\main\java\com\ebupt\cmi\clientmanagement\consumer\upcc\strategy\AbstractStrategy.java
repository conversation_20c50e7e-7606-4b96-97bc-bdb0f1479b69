package com.ebupt.cmi.clientmanagement.consumer.upcc.strategy;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.uitils.RetryUtil;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccConsumerContext;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsCardUpccRecord;
import com.ebupt.cmi.clientmanagement.mapper.CmsCardUpccRecordMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageCardUpccRelationMapper;
import com.ebupt.cmi.clientmanagement.mapper.PackageDirectionRelationMapper;
import com.ebupt.cmi.clientmanagement.service.ICmsCardUpccRecordService;
import com.ebupt.cmi.clientmanagement.service.lu.corenet.CoreNetCaller;
import com.ebupt.cmi.clientmanagement.utils.FlowOperationUtils;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

public abstract class AbstractStrategy extends RetryUtil {

    @Resource
    public CmsPackageCardUpccRelationMapper cmsPackageCardUpccRelationMapper;

    @Resource
    public ICmsCardUpccRecordService cmsCardUpccRecordService;

    @Resource
    public RedisTemplate<String, Number> redisTemplate;

    @Resource
    public CmsCardUpccRecordMapper cmsCardUpccRecordMapper;


    @Resource
    public PackageDirectionRelationMapper packageDirectionRelationMapper;

    @Resource
    public CoreNetCaller coreNetCaller;

    @Resource
    public HvShareRepository hvShareRepository;

    @Resource
    public FlowOperationUtils flowOperationUtils;


    public abstract <T extends UpccConsumerContext> void handle(T context);


}
