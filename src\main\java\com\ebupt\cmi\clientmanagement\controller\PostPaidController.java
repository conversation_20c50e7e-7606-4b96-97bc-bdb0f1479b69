package com.ebupt.cmi.clientmanagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.domain.dto.FlowInfoDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.PackageInfoDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.PostPaidQueryChannelDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.Channel;
import com.ebupt.cmi.clientmanagement.domain.entity.SettleRuleDetail;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.postPaidVO.*;
import com.ebupt.cmi.clientmanagement.service.PostPaidService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/7 10:48
 */

@Api(tags = "后付费渠道接口")
@RestController
@RequestMapping("/api/v2/postpaid")
public class PostPaidController {
    @Autowired
    private PostPaidService postPaidService;

    @ApiOperation("查询渠道接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize",value = "每页条数",required = true,paramType = "String"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "String"),
            @ApiImplicitParam(name = "channelName",value = "渠道名称",required = false,paramType = "String")
    })
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @GetMapping("/queryChannel")
    public Response<List<PostPaidQueryChannelDTO>> queryChannel(@RequestParam int pageSize, @RequestParam int pageNum,
                                                                @RequestParam(required = false) String channelName){
        try {
            IPage<PostPaidQueryChannelDTO> page = postPaidService.queryChannel(pageSize, pageNum, channelName);
            return Response.ok(page);
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("查询渠道可用套餐接口")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @GetMapping("/queryPackageListForChannel")
    public Response<List<PostPaidPackageListVO>> queryPackageListForChannel(){
        try {
            return Response.ok(postPaidService.queryPackageListForChannel());
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("查询订单可用套餐接口")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @ApiImplicitParam(name = "channelId",value = "渠道id",required = true,paramType = "String")
    @GetMapping("/queryPackageListForOrder")
    public Response<List<SettleRuleDetail>> queryPackageListForChannel(@RequestParam String channelId){
        try {
            return Response.ok(postPaidService.queryPackageListForOrder(channelId));
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("套餐本月账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize",value = "每页条数",required = true,paramType = "String"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "String"),
            @ApiImplicitParam(name = "channelId",value = "渠道id",required = false,paramType = "String"),
            @ApiImplicitParam(name = "month",value = "月份",required = false,paramType = "String")
    })
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @GetMapping("/queryPackageBills")
    public Response<List<PackageInfoDTO>> queryPackageBills(@RequestParam String channelId,
                                                            @RequestParam int pageSize,@RequestParam int pageNum){
        try {
            IPage<PackageInfoDTO> page = postPaidService.queryPackageBills(channelId,pageSize, pageNum);
            return Response.ok(page);
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("套餐付费详情")
    @ApiImplicitParam(name = "channelId",value = "渠道id",required = true,paramType = "String")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @GetMapping("/queryPackageDetails")
    public Response queryPackageDetails(@RequestParam String channelId){
        try {
            return Response.ok(postPaidService.queryPackageDetails(channelId));
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("流量本月账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize",value = "每页条数",required = true,paramType = "String"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "String"),
            @ApiImplicitParam(name = "channelId",value = "渠道id",required = false,paramType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @GetMapping("/queryFlowBills")
    public Response<List<FlowInfoDTO>> queryFlowBills(@RequestParam String channelId,
                                                      @RequestParam int pageSize, @RequestParam int pageNum){
        try {
            IPage<FlowInfoDTO> page = postPaidService.queryFlowBills(channelId,pageSize, pageNum);
            return Response.ok(page);
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("流量详情")
    @ApiImplicitParam(name = "channelId",value = "渠道id",required = true,paramType = "String")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @GetMapping("/queryFlowDetails")
    public Response queryFlowDetails(@RequestParam String channelId){
        try {
            return Response.ok(postPaidService.queryFlowDetails(channelId));
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道审核")
    @PutMapping("/channelCheck")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status",value = "状态",required = true,paramType = "String"),
            @ApiImplicitParam(name = "corpId",value = "渠道id",required = true,paramType = "String"),
            @ApiImplicitParam(name = "frostCrop",value = "企业是否可用",required = false,paramType = "Boolean")
    })
    @OperationLog(operationName = "后付费渠道——渠道审核",operationType = OperationTypeEnum.AUDIT)
    public Response channelCheck(@RequestParam String status,@RequestParam String corpId,
                                 @RequestParam(required = false) Boolean frostCrop){
        try {
            postPaidService.channelCheck(status,corpId,frostCrop);
            return Response.ok();
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道新增")
    @PostMapping("/newChannel")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @OperationLog(operationName = "后付费渠道——渠道新增",operationType = OperationTypeEnum.ADD)
    public Response newChannel(@RequestBody @Valid NewChannelVO newChannel){
        try {
            postPaidService.newChannel(newChannel);
            return Response.ok();
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道修改")
    @PostMapping("/updateChannel")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @OperationLog(operationName = "后付费渠道——渠道修改",operationType = OperationTypeEnum.UPDATE)
    public Response updateChannel(@RequestBody @Valid UpdateChannelVO updateChannelVO){
        try {
            postPaidService.updateChannel(updateChannelVO);
            return Response.ok();
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("订单新增")
    @PostMapping("/order")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @OperationLog(operationName = "后付费渠道——订单新增",operationType = OperationTypeEnum.ADD)
    public Response order(OrderVO orderVO){
        try {
            postPaidService.order(orderVO);
            return Response.ok();
        }catch (Exception e){
            e.printStackTrace();
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道单个删除")
    @DeleteMapping("/deleteChannel")
    @ApiImplicitParam(name = "channelId",value = "渠道id",required = true,paramType = "String")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @OperationLog(operationName = "后付费渠道——渠道单个删除",operationType = OperationTypeEnum.DELETE)
    public Response deleteChannel(@RequestParam String channelId){
        try {
            postPaidService.deleteChannel(channelId);
            return Response.ok();
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道批量删除")
    @DeleteMapping("/batchDeleteChannel")
    @ApiResponses({
            @ApiResponse(code = 0000,message = "成功"),
            @ApiResponse(code = 1000,message = "失败")
    })
    @OperationLog(operationName = "后付费渠道——渠道批量删除",operationType = OperationTypeEnum.DELETE)
    public Response batchDeleteChannel(@RequestBody BatchDeleteChannelVO batchDeleteChannelVO){
        try {
            postPaidService.batchDeleteChannel(batchDeleteChannelVO.getChannelIdList());
            return Response.ok();
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("账单导出")
    @GetMapping(value = "/detailDownload")
    @ApiImplicitParam(name = "channelId",value = "渠道id",required = true,paramType = "String")
    public void detailOut(@RequestParam String channelId, HttpServletResponse response){
        postPaidService.detailOut(channelId,response);
    }

    @ApiOperation("渠道查询")
    @GetMapping(value = "/queryChannelList")
    public Response queryChannelList(){
        try {
            return Response.ok(postPaidService.queryChannelList());
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("结算表查询")
    @GetMapping(value = "/queryDetailForUpdate")
    public Response queryDetailForUpdate(@RequestParam String channelId){
        try {
            return Response.ok(postPaidService.queryDetailForUpdate(channelId));
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }

    /********************************************************************************
     *                          非业务接口                                           *
     ********************************************************************************/

    @ApiOperation("结算表查询")
    @GetMapping(value = "/queryPackageForChannelOrder")
    public Response<List<SettleRuleDetail>> queryPackageForChannelOrder(@RequestParam String corpId){
        try {
            return Response.ok(postPaidService.queryPackageForChannelOrder(corpId));
        }catch (Exception e){
            return Response.error(e.getMessage());
        }
    }
}
