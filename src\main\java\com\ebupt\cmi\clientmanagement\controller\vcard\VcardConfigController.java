package com.ebupt.cmi.clientmanagement.controller.vcard;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.cooperation.VcardConfigVO;
import com.ebupt.cmi.clientmanagement.service.CooperationService;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName VcardConfigController.java
 * @Description V卡配置绑定接口-给产品管理服务V卡导入用的
 * @createTime 2021年06月08日 11:12:00
 */

@RestController
@RequestMapping("/vcardconfig")
@Api(tags = "合作商接口")
@Slf4j
public class VcardConfigController {
    @Autowired
    CooperationService cooperationService;

    @PostMapping
    @NormalLog
    public Response VcardConfig(@RequestBody VcardConfigVO vcardConfigVO) {
        cooperationService.vcardConfig(vcardConfigVO);
        return Response.ok();
    }

    @DeleteMapping("/{imsi}")
    public Response vCardConfigDelete(@PathVariable("imsi") String imsi) {
        cooperationService.vCardConfigDelete(imsi);
        return Response.ok();
    }
}


