package com.ebupt.cmi.clientmanagement.domain.dto.Context;

import cn.hutool.core.thread.RejectPolicy;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.vo.CreateOrderReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 14:48
 */
@Data
public class CreateOrderContext {
    private CreateOrderReq createOrderReq;

    private List<ChannelCard> channelCards;

    private Date orderDate;

    /**
     * 卡+套餐判定
     */
    private boolean isEsimCard = false;

    /**
     * 购买类型
     */
    private PurchaseType purchaseType;

    public CreateOrderContext(CreateOrderReq createOrderReq) {
        this.createOrderReq = createOrderReq;
    }

    @Getter
    @AllArgsConstructor
    public enum PurchaseType {
        CARD_AND_PACKAGE,

        REFUEL_PACKAGE,

        BIND_PACKAGE,

        COMMON_PACKAGE
    }

}