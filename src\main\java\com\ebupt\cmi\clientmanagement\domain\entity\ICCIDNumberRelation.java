package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 */
@TableName("cms_iccid_number_relation")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ICCIDNumberRelation extends BaseEntity {
    private Long id;

    private String iccid;

    private String cmccNumber;

    private String cmccNumberProvince;

    private String cmccNumberCity;

    private String orderUniqueId;
}
