package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import com.ebupt.cmi.clientmanagement.domain.entity.BillRuleDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.Channel;
import com.ebupt.cmi.clientmanagement.domain.entity.EopAccessDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.SettleRuleDetail;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CooperationDTO.java
 * @Description 查询需要的DTO
 * @createTime 2021年05月08日 14:40:00
 */

@Data
public class CooperationDTO {
    /**
     * 客户信息
     */
    Channel channel;

    /**
     * 能力接入详情
     */
    EopAccessDetail eopAccessDetail;

}
