package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Desc 实名制表实体
 * @<PERSON> <PERSON><PERSON> l<PERSON>
 * @Date 2021/4/26 14:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Realname extends BaseEntity {

    /**
     * 实名制名称
     */
    private String name;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 订单认证URL
     */
    private String orderUrl;

    /**
     * 卡认证URL
     */
    private String cardUrl;

    /**
     * 一证绑定卡数量限制
     */
    private Integer bindNum;

}
