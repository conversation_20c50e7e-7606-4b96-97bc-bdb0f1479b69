package com.ebupt.cmi.clientmanagement.domain.dto.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 语言信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LangInfo {

    /**
     * 语言类型，参见ISO-639定义，例如
     * zh：中文,
     * en：英文
     */
    private String language;

    /**
     * 地区代码，参见ISO-3166定义，例如
     * CN：中国
     * US：美国
     * HK: 香港(繁体)
     */
    private String country;

}
