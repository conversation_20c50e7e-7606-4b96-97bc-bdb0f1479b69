package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.service.lu.context.h.UpccContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PackageUpcctemplateRedisDTO {
    private String appGroupId;
    List<UpccContext> packageCardUpccRedisDTOs;
}
