package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @description 套餐与卡绑定表
 * @date 2021/4/19 11:14
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_bind")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelBind extends BaseEntity {

    private Long id;
    private String imsi;

    private String msisdn;

    private String iccid;

    /**
     * 套餐类型：
     * 1：套餐
     * 2：终端线下卡池套餐
     * 3: 流量池
     */
    private String packageType;

    private String packageId;

    private String corpId;

    private String orderChannel;

    private String poolId;

    private String thirdOrderId;

    private String orderUniqueId;

    private String goodsId;

}

