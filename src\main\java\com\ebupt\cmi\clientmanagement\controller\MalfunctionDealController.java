package com.ebupt.cmi.clientmanagement.controller;


import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.MalfunctionDealService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/malfunctionDeal")
public class MalfunctionDealController {
    private final MalfunctionDealService malfunctionDealService;

    @GetMapping("/do")
    public void malfunctionDeal(@RequestParam String mcc,@RequestParam String supplierId,@RequestParam String malfunctionId) {
        malfunctionDealService.malfunctionDeal(mcc, supplierId, malfunctionId);
    }
}
