package com.ebupt.cmi.clientmanagement.aspect;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.nio.charset.Charset;

/**
 * HttpLoggingInterceptor
 *
 * @Author: zhaoqiankun
 * @Date: 2021/5/14 9:42
 */
@Slf4j
public class HttpLoggingInterceptor implements ClientHttpRequestInterceptor {
    private static final String REQ_BEG = "=========================== req beg >>>>>>>>>>>>>>>>>>>>>>>>>>";
    private static final String REQ_END = "=========================== req end <<<<<<<<<<<<<<<<<<<<<<<<<<";
    private static final String RES_BEG = "=========================== res beg >>>>>>>>>>>>>>>>>>>>>>>>>>";
    private static final String RES_END = "=========================== res end <<<<<<<<<<<<<<<<<<<<<<<<<<";

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

        logRequest(request, body);
        ClientHttpResponse response = null;
        try {
            response = execution.execute(request, body);
        } catch (SocketTimeoutException e) {
            // throw new BusinessException(EnumResult.CODE_800004, "数据请求超时");
        }

        logResponse(response);

        return response;
    }

    private void logRequest(HttpRequest request, byte[] body) throws IOException {

        if (log.isInfoEnabled()) {
            log.debug(REQ_BEG);
            log.debug("URI         : {}", request.getURI());
            log.debug("Method      : {}", request.getMethod());
            log.debug("Headers     : {}", request.getHeaders());
            log.debug("Request body: {}", new String(body, "UTF-8"));
            log.debug(REQ_END);
        }
    }

    private void logResponse(ClientHttpResponse response) throws IOException {

        if (log.isInfoEnabled()) {
            log.debug(RES_BEG);
            log.debug("Status code  : {}", response.getStatusCode());
            log.debug("Status text  : {}", response.getStatusText());
            log.debug("Headers      : {}", response.getHeaders());
            log.debug("Response body: {}", StreamUtils.copyToString(response.getBody(), Charset.defaultCharset()));
            log.debug(RES_END);
        }
    }
}
