package com.ebupt.cmi.clientmanagement.controller.scheduled;

import com.ebupt.cmi.clientmanagement.service.PackageService;
import com.ebupt.cmi.clientmanagement.service.scheduled.ScheduledService;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;

/**
 * ScheduledController
 *
 * @Author: zhaoqiankun
 * @Date: 2021/6/14 22:29
 */
@Api(tags = "定时任务")
@RestController
@AllArgsConstructor
@RequestMapping("/scheduled")
public class ScheduledController {

    private final ScheduledService scheduledService;
    private final PackageService packageService;

    @PostMapping("/v/hss/cancellation")
    @ApiOperation(value = "终端线下V卡HSS销户")
    @NormalLog
    public void getDetail() {
        scheduledService.vHssCancellation();
    }

    @PostMapping("/h/overdue/files")
    @ApiOperation(value = "主卡过期文件处理")
    @NormalLog
    public void handExpiredDocuments() throws IOException {
        scheduledService.handExpiredDocuments();
    }

    @PostMapping("/threshold/reminder")
    @ApiOperation(value = "渠道商押金阈值过期提醒")
    @NormalLog
    public void thresholdReminder() {
        scheduledService.thresholdReminder();
    }


    @PostMapping("/h/screen")
    @ApiOperation(value = "主卡筛选定时程序")
    @NormalLog
    public void hCardScreen() {
        scheduledService.hCardScreen();
    }


    @PostMapping("/threshold/messageReminder")
    @ApiOperation(value = "套餐即将过期下发短信提醒")
    @NormalLog
    public void packageOverReminder() {
        packageService.sendPackageOutReminderMessage();
    }

    @PostMapping("/threshold/emailReminder")
    @ApiOperation(value = "渠道商流量池达到提醒阈值发送邮件提醒")
    @NormalLog
    public void channelReminder() {
        scheduledService.sendOverNumReminderEmail();
    }

    @PostMapping("/threshold/updatePackageStatus")
    @ApiOperation(value = "定时修改套餐状态")
    @NormalLog
    public void updatePackageStatus() {
        scheduledService.updatePackageStatus();
    }


    @PostMapping("/threshold/provinceBill")
    @ApiOperation(value = "定时上传省移动账单")
    @NormalLog
    public void dealProvinceBill() {
        scheduledService.dealProvinceBill();
    }

    @PostMapping("/threshold/updateRealNameStatus")
    @ApiOperation(value = "定时清理实名制数据")
    @NormalLog
    public void updateRealNameStatus() {
        scheduledService.updateRealNameStatus();
    }

    @DeleteMapping("/threshold/clearExpirePackageDate")
    @ApiOperation(value = "定时清理过期套餐附属数据")
    @NormalLog
    public void clearExpirePackageDate() {
        scheduledService.clearExpirePackageDate();
    }

    @GetMapping("/threshold/dealDelayMessage")
    @ApiOperation(value = "节点切换处理延迟消息数据")
    @NormalLog
    public void dealDelayMessage(String str) {
        scheduledService.dealDelayMessage(str);
    }

    @GetMapping("/threshold/packageRecover")
    @ApiOperation(value = "节点切换处理延迟消息数据")
    @NormalLog
    public void dealDelayMessage() {
        scheduledService.dealDelayMessageCompensation();
    }


    @GetMapping("/threshold/reconciliationDateStatistics")
    @ApiOperation(value = "对账日统计")
    @NormalLog
    public void reconciliationDateStatistics(@RequestParam String startTime, @RequestParam String endTime) {
        scheduledService.reconciliationDateStatistics(startTime, endTime, null);
    }

    @GetMapping("/threshold/reconciliationDateStatisticsX")
    @ApiOperation(value = "对账日统计-指定渠道商")
    @NormalLog
    public void reconciliationDateStatistics(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String cropId) {
        scheduledService.reconciliationDateStatistics(startTime, endTime, cropId);
    }

    @GetMapping("/threshold/reconciliationDateStatisticsW")
    @ApiOperation(value = "对账日统计-数据割接")
    @NormalLog
    public void reconciliationDateStatisticsW(@RequestParam String startTime, @RequestParam String endTime) {
        scheduledService.reconciliationDateStatisticsW(startTime, endTime);
    }

    @GetMapping("/threshold/generateChannelBill/normal")
    @ApiOperation(value = "渠道商出账定时")
    @NormalLog
    public void generateChannelBill(@RequestParam Long accountId, @RequestParam String billType) {
        scheduledService.channelAccountDelivery(accountId, billType, null, null, null);
    }

    @GetMapping("/threshold/generateChannelBill/make")
    @ApiOperation(value = "渠道商定制账期出账定时")
    @NormalLog
    public void generateChannelBill(@RequestParam Long accountId, @RequestParam String billType, @RequestParam String batchId, @RequestParam
    @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate start, @RequestParam
                                    @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate end) {
        scheduledService.channelAccountDelivery(accountId, billType, start, end, batchId);
    }


    @DeleteMapping("/threshold/testSdc2")
    @ApiOperation(value = "定时清理过期套餐附属数据")
    @NormalLog
    public void testSdc2() {
        scheduledService.testSdc();
    }
}
