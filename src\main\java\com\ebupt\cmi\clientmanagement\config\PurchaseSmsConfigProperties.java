package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 购买短信配置
 * <AUTHOR>
 * @date 2021-8-6 15:22:59
 */
@Data
@Component
@ConfigurationProperties("purchase-sms-config")
public class PurchaseSmsConfigProperties {

    /**
     * 短信模板id
     */
    private long templateId;

    /**
     * 下发语言
     */
    private String sendLang;

}
