package com.ebupt.cmi.clientmanagement.consumer.hvshare.service;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.service.lu.context.v1.LocationUpdateV1Context;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonService.java
 * @Description 一些公共的流程，抽取出来
 * @createTime 2022年03月09日 16:34:00
 */

public interface CommonService {

    /**
     * 下发激活短信
     *
     * @param channelPackageCard 最新的channelPackageCard
     */
    void sendSmg(ChannelPackageCard channelPackageCard);

    /**
     * 登网通知封装
     *
     * @param vimsi  vimsi
     * @param corpId corpId
     * @param spec   是否是特殊
     */
    void loginNotification(String vimsi, String corpId, boolean spec, String mcc);

}
