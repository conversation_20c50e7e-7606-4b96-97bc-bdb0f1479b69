package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 销售渠道
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/7/26 10:40
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum SalesChannel {
    /**
     * 批量售卖
     */
    BATCH_SELL("1", "批量售卖", OrderChannel.BATCH_SELL),


    /**
     * 推广活动
     */
    PROMOTION("2", "推广活动", OrderChannel.PROMOTION),

    /**
     * 测试渠道
     */
    TEST_CHANNEL("3", "测试渠道", OrderChannel.TEST_CHANNEL);


    private String k;

    private String val;

    private OrderChannel orderChannel;

    public static String getK(String val) {
        for (SalesChannel p : SalesChannel.values()) {
            if (p.getVal().equals(val)) {
                return p.getK();
            }
        }
        log.warn("输入不符合要求：{}", val);
        return "";
    }

    public static String getOrderChannel(String k) {
        for (SalesChannel p : SalesChannel.values()) {
            if (p.getK().equals(k)) {
                return p.getOrderChannel().getType();
            }
        }
        log.warn("输入不符合要求：{}", k);
        return "";
    }
}
