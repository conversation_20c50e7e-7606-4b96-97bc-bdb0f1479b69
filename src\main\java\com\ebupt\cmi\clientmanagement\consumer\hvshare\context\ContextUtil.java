package com.ebupt.cmi.clientmanagement.consumer.hvshare.context;

import com.ebupt.cmi.clientmanagement.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ContextUtil.java
 * @Description 获取上下文
 * @createTime 2022年02月28日 16:29:00
 */

@Slf4j
public class ContextUtil {

    public static BaseContext getContext(Class<? extends BaseContext> clazz) {
        try {
            Constructor<? extends BaseContext> constructor = clazz.getDeclaredConstructor();
            constructor.setAccessible(true);
            BaseContext context = constructor.newInstance();
            context.setRetryTimes(0);
            return context;
        } catch (Exception e) {
            log.warn("创建上下文实例失败:", e);
            throw new BizException(e);
        }
    }
}
