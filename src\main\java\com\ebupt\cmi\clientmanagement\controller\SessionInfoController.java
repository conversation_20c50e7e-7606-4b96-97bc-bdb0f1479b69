package com.ebupt.cmi.clientmanagement.controller;

import com.alibaba.excel.EasyExcel;
import com.ebupt.cmi.clientmanagement.domain.dto.HistorySessionListDTO;
import com.ebupt.cmi.clientmanagement.domain.response.QueryCurrentSessionResp;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.HistorySessionInfo;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.service.SessionInfoService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/session")
@Api(tags = "网站获取session相关")
public class SessionInfoController {
    @Autowired
    private SessionInfoService sessionInfoService;
    /**
     * 查询当前会话状态
     * @param imsi
     * @return 当前会话信息
     */
    @GetMapping("/getCurrent")
    public Response<QueryCurrentSessionResp> queryCurrentSession(
            @RequestParam("imsi") @NotBlank(message = "imsi不能为空") String imsi) {
        return Response.ok(sessionInfoService.queryCurrentSession(imsi));
    }

    /**
     * @param historySessionListDTO 查询条件
     * @return 分页的历史会话信息
     */
    @PostMapping("/getHistory")
    public Response<List<HistorySessionInfo>> getPageHistorySessionInfoByIccid(
            @Valid @RequestBody HistorySessionListDTO historySessionListDTO) {
        return Response.ok(sessionInfoService.getPageHistorySessionInfoByIccid(historySessionListDTO));
    }

    /**
     * 导出Excel报表
     *
     * @param response HTTP响应对象，用于设置响应头和输出流
     * @param historySessionListDTO 包含会话历史记录查询条件的数据传输对象
     */
    @PostMapping("/exportHistory")
    public void exportExcel(HttpServletResponse response, @Valid @RequestBody HistorySessionListDTO historySessionListDTO){
        List<HistorySessionInfo> sessionInfos = sessionInfoService.getPageHistorySessionInfoByIccid(historySessionListDTO);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String fileName = "history" + "_"+historySessionListDTO.getIccid()+ "_"+timestamp +".xlsx";
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("使用URLEncoder对文件名进行编码失败", e);
            throw new BizException("导出历史会话报表失败");
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        try {
            EasyExcel.write(response.getOutputStream(), HistorySessionInfo.class)
                    .autoCloseStream(true)
                    .sheet("历史会话")
                    .doWrite(sessionInfos);
        } catch (IOException e) {
            log.error("导出历史会话报表失败", e);
            throw new BizException("导出历史会话报表失败");
        }
    }
}
