package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Builder
@TableName("cms_deposit_charge_record")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsDepositChargeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String billId;
    private Date chargeTime;
    private String corpId;
    private String cooperationMode;
    private String chargeStatus;
    private BigDecimal chargeAmount;
    private String invoiceAddress;
    private String invoiceNo;
    private String proUniqueId;
    private Date updateTime;
    private String noPassReason;
    private String channelType;
    private String isDisplay;

}
