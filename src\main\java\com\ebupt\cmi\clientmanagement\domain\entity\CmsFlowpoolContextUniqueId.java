package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@TableName("cms_flowpool_context_unique_id")
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsFlowpoolContextUniqueId implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "老套餐唯一ID")
    private String oldPackageUniqueId;

    @ApiModelProperty(value = "新套餐唯一ID")
    private String newPackageUniqueId;

    @ApiModelProperty(value = "旧周期流量池唯一ID")
    private String oldFlowpoolUniqueId;
}
