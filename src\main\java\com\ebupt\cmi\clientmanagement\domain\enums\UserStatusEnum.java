package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * UserStatus
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/5/8 11:22
 */
@Getter
@AllArgsConstructor
public enum UserStatusEnum {
    /**
     * 正常
     */
    OK("1"),
    /**
     *  暂停
     */
    PAUSE("2"),
    /**
     *  删除
     */
    DELETE("3");

    private String status;
}
