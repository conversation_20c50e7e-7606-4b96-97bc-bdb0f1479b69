package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * UPCC签约业务状态枚举
 * <AUTHOR>
 * @date 2021-6-7 16:09:33
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum UpccSignStatusEnum {

    /**
     * 成功
     */
    SUCCESS("1"),

    /**
     * 失败
     */
    FAILED("2");

    private String value;

}
