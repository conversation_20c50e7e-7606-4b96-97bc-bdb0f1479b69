package com.ebupt.cmi.clientmanagement.controller.flowpool;

import com.alibaba.fastjson.JSONObject;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.PackageDelayMessageVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.ReachingTreatmentVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.consumer.uitils.SendMessageWrapper;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TestConsumerController.java
 * @Description TODO
 * @createTime 2022年01月20日 10:29:00
 */

@Slf4j
@RestController
@RequestMapping("/testConsumerController")
@Api(tags = "测试Consumer")
public class TestConsumerController {
    @Autowired
    protected RabbitTemplate rabbitTemplate;

    @Autowired
    SendMessageWrapper sendMessageWrapper;

    @PostMapping("/test")
    public Response test(@RequestBody SingleDayDelayVO flowPoolRabbitMQMessage) {

//        //将消息携带绑定键值：TestDirectRouting 发送到交换机ota.directExchange
//        rabbitTemplate.convertAndSend("flowPool.directExchange111",
//                "flowPoolDirectRouting111", JSONObject.toJSONString(flowPoolRabbitMQMessage));

        sendMessageWrapper.throwMessageToQueue(JSONObject.toJSONString(flowPoolRabbitMQMessage),
                QueueEnum.ResumeDelayQueue,6000L);

        return Response.ok();

    }
}
