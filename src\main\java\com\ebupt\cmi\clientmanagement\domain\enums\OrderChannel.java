package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/8 17:05
 */

@AllArgsConstructor
@Getter
public enum OrderChannel {
    /**
     * 102：API
     */
    API("102"),
    /**
     * 103：官网（H5）
     */
    OFFICIAL("103"),
    /**
     * 104:  北京移动
     */
    BJ_MOBILE("104"),
    /**
     * 105：批量售卖
     */
    BATCH_SELL("105"),
    /**
     * 106：推广活动
     */
    PROMOTION("106"),
    /**
     * 110: 测试渠道
     */
    TEST_CHANNEL("110"),
    /**
     * 111：合作发卡
     */
    COOP_CARD("111"),
    /**
     * 后付费发卡
     */
    POST_PAID("112"),
    /**
     * WEB
     */
    WEB("113"),
    /**
     * 流量池WEB
     */
    WEB_FLOW_POOL("114");

    String type;
}
