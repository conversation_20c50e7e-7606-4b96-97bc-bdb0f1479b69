package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

/**
 * CmsUserAccount
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/5/7 19:48
 */
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cms_user_account")
@AllArgsConstructor
@NoArgsConstructor
public class CmsUserAccount extends BaseEntityAccount{
    @TableId
    private String id;

    //这个应该是用户手机号吧
    private String msisdn;

    private String directRatio;

    private String secretSalt;

    private Integer iterations;

    private Integer outLength;

    private String email;

}
