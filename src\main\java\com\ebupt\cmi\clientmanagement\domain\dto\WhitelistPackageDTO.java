package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 白名单套餐DTO
 * @date 2023-03-13
 */
@Data
@ApiModel(value = "白名单套餐DTO")
public class WhitelistPackageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "渠道商id")
    @NotBlank(message = "渠道商id不能为空")
    private String corpId;

    @ApiModelProperty(value = "号段列表")
    private List<NumberSegmentDTO> numberSegmentDTO;

    @Data
    public static class NumberSegmentDTO {
        @ApiModelProperty(value = "起始号段")
        @NotBlank(message = "起始号段不能为空")
        private String beginIccid;

        @ApiModelProperty(value = "结束号段")
        @NotBlank(message = "结束号段不能为空")
        private String endIccid;

        @ApiModelProperty(value = "套餐id")
        @NotBlank(message = "套餐id不能为空")
        private String packageId;
    }
} 