package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@TableName("cms_channel_market_billflow_a2z")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsChannelMarketBillflowA2z {

    @TableId
    private Long id;

    //'渠道商ID'
    private String corpId;

    //'订单id'
    private String orderId;

    //类型：1.增加影响返利款
    //2.流量结算 3.营销额过期清零
    private String type;

    //币种编码：156 人民币 840 美元 344 港币
    private String currencyCode;

    //交易金额，单位：分
    private BigDecimal amount;

    //账户余额（可用额度）单位：分
    private BigDecimal deposit;

    //未过期活动总余额 单位：分
    private BigDecimal totalAmount;

    //营销活动id
    private Long activityId;

    //已用流量
    private BigDecimal usedFlow;

    //'创建时间'
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    //批价日期
    private Date approveDate;

    private String date;

    private String mcc;

    private String recordType;
}

