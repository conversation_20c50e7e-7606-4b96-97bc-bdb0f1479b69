package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.through.BaseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.through.*;
import com.ebupt.cmi.clientmanagement.service.OutSideApiService;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OutSideApiController.java
 * @Description 对外透传
 * @createTime 2021年05月14日 10:40:00
 */

@RestController
@RequestMapping("/through")
@Api(tags = "透传接口,南向")
@Slf4j
public class OutSideApiSouthController {
    @Autowired
    OutSideApiService outSideApiService;

    @ApiOperation(value = "激活通知接口，内部服务调用本接口，透传别人，南向接口", notes = "激活通知接口")
    @PostMapping("/notify_activation/v1")
    @NormalLog
    public Response notifyActivationSouth(@RequestBody NotifyActivationVO notifyVO) {

        try {
            BaseResult baseResult = outSideApiService.notifyActivation(notifyVO);
            if ("0".equals(baseResult.getCode())) {
                return Response.ok();
            } else {
                return Response.error(baseResult.getDescription());
            }
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation(value = "到期通知接口", notes = "到期通知接口")
    @PostMapping("/notify_expire/v1")
    @NormalLog
    public Response notifyExpireSouth(@RequestBody NotifyExpireVO notifyExpireVO) {
        try {
            BaseResult baseResult = outSideApiService.notifyExpire(notifyExpireVO);
            if ("0".equals(baseResult.getCode())) {
                return Response.ok();
            } else {
                return Response.error(baseResult.getDescription());
            }
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation(value = "流量通知接口", notes = "流量通知接口")
    @PostMapping("/quota_notice/v1")
    @NormalLog
    public Response quotaNotice(@RequestBody QuotaNoticeVO quotaNoticeVO) {
        try {
            BaseResult baseResult = outSideApiService.quotaNotice(quotaNoticeVO);
            if ("0".equals(baseResult.getCode())) {
                return Response.ok();
            } else {
                return Response.error(baseResult.getDescription());
            }
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

}
