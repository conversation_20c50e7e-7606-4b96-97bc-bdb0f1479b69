package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.UsedVivoVO;
import com.ebupt.cmi.clientmanagement.domain.dto.VivoPackageInfoDTO;
import com.ebupt.cmi.clientmanagement.service.VivoService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * VivoPackageController
 * vivo相关
 * @Author: zhaoqiankun
 * @Date: 2021/5/20 14:50
 */
@RestController
@RequestMapping("/vivo")
@AllArgsConstructor
public class VivoPackageController {
    private final VivoService vivoService;

    @PostMapping("/package/info")
    public Response<VivoPackageInfoDTO> getUsedVivo(@RequestBody @Validated UsedVivoVO usedVivoVO) {
        return vivoService.getUsedVivo(usedVivoVO);
    }
}
