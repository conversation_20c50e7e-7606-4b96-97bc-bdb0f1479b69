package com.ebupt.cmi.clientmanagement.consumer.hvshare.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName QueueEnum.java
 * @Description 队列枚举
 * @createTime 2022年02月28日 14:32:00
 */

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum QueueEnum {

    /**
     * 1：达量处理消费队列
     */
    ReachingTreatment("singleCard.reached.queue", "reached", "reachedExchange"),
    LocalTest("localTestQueue", "localTestExchange", "localTestRouter"),

    /**
     * 2：单日恢复延时队列(延时消息)
     */
    ResumeDelayQueue("singleDayResume.delay.queue", "resume", "resumeExchange"),

    /**
     * 3: 套餐超时激活延时队列
     */
    PackageExpireDelayQueue("packageExpire.delay.queue", "packageExpire", "packageExpireExchange"),


    /**
     * 4. lu绑定订单扣费失败取消延迟队列
     */
    LuOrderCancelDelayQueue("luOrderCancel.delay.queue", "luOrderCancel", "luOrderCancelExchange"),

    /**
     * 5. CDR流程延迟队列
     */
    CDRProcessDelayQueue("cdrProcess.delay.queue", "cdrProcess", "cdrProcessExchange"),

    /**
     * 6. 套餐停用队列
     */
    PackageStopQueue("package.stop.queue", "packageStop", "packageStopExchange"),

    /**
     * 7. 套餐恢复队列
     */
    PackageRecoverQueue("package.recover.queue", "packageRecover", "packageRecoverExchange"),

    /**
     * 8. upcc签约队列，目前只处理定向应用的签约
     */
    UpccSignQueue("upcc.sign.queue", "upccSign", "upccSignExchange"),

    DIRECTIONALAPPTEMPLATEDEL("directionalAppTemplate.del.queue", "delDirectionalAppTemplate", "directionalAppTemplateDelExchange"),

    /**
     * 10: 实名制延时认证通过队列
     */
    RealNameAuthenticationExpireQueue("realNameAuth.expire.queue", "realNameAuthentication", "realNameAuthenticationExchange"),

    MalfunctionDealQueue("malfunctionDeal.expire.queue", "malfunctionDeal", "malfunctionDealExchange");

    @Getter
    private String name;

    @Getter
    private String routingKey;

    @Getter
    private String exchangeName;

}
