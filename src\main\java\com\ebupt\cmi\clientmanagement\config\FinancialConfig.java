package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @since 2025/2/11 15:58
 */
@Data
@Component
@ConfigurationProperties("financial")
public class FinancialConfig extends SftpConfig{

    private Sftp sftp;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Sftp extends SftpConfig.Sftp {
        private String remotePath;
        private String localstoragePath;
        private String remotePathExtra;
    }
}
