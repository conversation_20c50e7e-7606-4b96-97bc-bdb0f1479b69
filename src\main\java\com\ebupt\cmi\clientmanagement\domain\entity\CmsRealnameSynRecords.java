package com.ebupt.cmi.clientmanagement.domain.entity;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cms_realname_syn_records对象", description="")
@Builder
@AllArgsConstructor
@NoArgsConstructor
/**
 * 
 * @TableName cms_realname_syn_records
 */
public class CmsRealnameSynRecords implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 组id
     */
    private Long groupId;

    /**
     * 认证记录规则code
     */
    private String authRuleCode;
    /**
     * 被认证规则code
     */
    private String synRuleCode;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    private String iccid;

    private static final long serialVersionUID = 1L;
}