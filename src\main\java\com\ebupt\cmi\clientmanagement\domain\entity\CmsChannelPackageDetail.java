package com.ebupt.cmi.clientmanagement.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * (CmsChannelPackageDetail)表实体类
 *
 * <AUTHOR>
 * @since 2023-06-21 17:34:43
 */
@Data
@Builder
@TableName("cms_channel_package_detail")
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelPackageDetail {

    private Long id;
    //厂商id
    private String corpId;
    //套餐id
    private String packageId;
    //套餐价格
    private BigDecimal packagePrice;
    //创建时间默认值：CURRENT_TIMESTAMP
    private Date createTime;
    //修改时间
    private Date updateTime;

}

