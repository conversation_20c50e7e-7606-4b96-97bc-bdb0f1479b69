package com.ebupt.cmi.clientmanagement.domain.enums.cooperation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChargeType.java
 * @Description 资费编码类型
 * @createTime 2021年06月04日 14:18:00
 */
@AllArgsConstructor
@Getter
public enum ChargeType {


    /**
     * 资费编码
     */
    BILLCODE("1", "资费编码"),

    /**
     * 流量方向
     */
    FLUSHWAY("2", "流量方向");

    private String code;
    private String description;
}
