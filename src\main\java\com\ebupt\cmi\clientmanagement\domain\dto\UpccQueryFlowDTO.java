package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.enums.PackageFlowLimitEnums;
import com.ebupt.cmi.clientmanagement.domain.vo.through.HistoryQuota;
import com.ebupt.cmi.clientmanagement.domain.vo.through.SubscriberQuota;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/12 15:21
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpccQueryFlowDTO {
    PackageFlowLimitEnums type;

    SubscriberQuota subscriberQuotaList;

    Map<String, String> appNameMap;
}
