package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 订购类型枚举
 * @date 2021/4/22 14:03
 */
@AllArgsConstructor
@Getter
public enum OrderTypeEnum {

    /**
     * 卡
     */
    CARD("1"),

    /**
     * 套餐
     */
    PACKAGE("2"),

    /**
     * 卡 + 套餐
     */
    CARD_PACKAGE("3"),

    /**
     * 终端线下卡池套餐
     */
    CARD_POOL("4"),

    /**
     * 流量池套餐
     */
    FLOW_POOL("5"),

    /**
     * 加油包
     */
    REFUEL_PACKAGE("7");

    private final String orderType;
}
