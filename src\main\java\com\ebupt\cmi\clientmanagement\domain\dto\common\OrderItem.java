package com.ebupt.cmi.clientmanagement.domain.dto.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 18:49
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderItem {

    private String orderID;

    private String orderItemID;

    private String price;

    @JsonProperty("Order_status")
    private String orderStatus;

    private ProductOffering productOffering;

    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private Date createTime;

}
