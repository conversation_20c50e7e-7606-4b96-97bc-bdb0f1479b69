package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/15 16:02
 */
@Data
@ApiModel
public class PersonalOrderDTO {

    @ApiModelProperty(value = "用户")
    private String user;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDate;

    private String orderName;

    /**
     * 订购类型：
     * 1：卡
     * 2：套餐
     * 3：卡+套餐
     * 4：终端线下卡池
     * 5：流量池
     */
    private String orderType;

    /**
     * 对应订购类型：
     * order_type = 2、3 为套餐ID
     * order_type = 4 为卡池ID
     * order_type = 5 为流量池流程ID
     */
    private String packageId;

    private String iccid;
    private String packageName;

    private String nameEn;

    private String nameTw;

    private String periodUnit;

    private Integer keepPeriod;

    /**
     * 套餐订购有效期，套餐有效期（天） + 当前时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveDay;

    private String signBizId;

    private String limitSpeedSignBizId;

    /**
     * 订购渠道
     * 102：API
     * 103：官网（H5）
     * 104:  北京移动
     * 105：批量售卖
     * 106：推广活动
     * 110: 测试渠道
     */
    private String orderChannel;

    private String logistic;

    private String address;

    /**
     * 收件人
     */
    private String addressee;

    /**
     * 联系号码
     */
    private String phoneNumber;

    /**
     * 邮编
     */
    private String postCode;

    private Integer count;

    private String currencyCode;

    /**
     * 价格，单位分
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal amount;

    private String thirdOrderId;

    /**
     * 订单类型归属
     * 1：个人订单
     * 2：渠道订单
     * 3：绑定订单
     */
    private String orderUserType;

    /**
     * 订单状态
     * 1、待发货
     * 2、完成
     * 3、已退订
     * 4、激活退订待审批
     * 	5：部分退订
     * 	6：部分发货
     * 	7：已回收
     * 	8：部分回收
     * 9：复合状态
     */
    private String orderStatus;

    /**
     * 退订类型
     * 1-过期未激活
     * 2-用户退款
     */
    private String unsubscribeType;

    /**
     * 支付方式（当前为1）
     * 1：直接支付
     * 3：页面支付
     */
    private String payType;

    /**
     * 购买用户的corpid
     */
    private String orderUserId;

    private String orderUserName;

    private String logisticCompany;
    /**
     * 订购唯一ID
     */
    private String orderUniqueId;

    /**
     * 卡片形态
     * 1：普通卡（实体卡）
     * 2：Esim卡
     * 3：贴片卡
     */
    private String cardForm;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private boolean isBigOrder;

}
