package com.ebupt.cmi.clientmanagement.consumer.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.utils.StringUtils;
import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.strategy.AbstractFlowPoolConsumerStrategy;
import com.ebupt.cmi.clientmanagement.domain.enums.UpccOpenStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import com.ebupt.cmi.clientmanagement.feign.control.domain.hss.RmvSubVO;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.AddSubscriber;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BlockUpStrategy.java
 * @Description 停用策略
 * @createTime 2022年01月12日 16:45:00
 */

@Component("blockup")
@Slf4j
public class BlockUpStrategy extends AbstractFlowPoolConsumerStrategy {
    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = GoodException.class)
    public void handle(FlowPoolConsumerContext flowPoolConsumerContext) {

        log.info("==============停用流程开始，卡{}================", flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());

        super.handle(flowPoolConsumerContext);

        //根据HIMSI查询PMS_CARD表，判断流量池唯一ID与签约唯一ID是否一致
        //2022/02/21 现在不这么玩儿了，不在事务里面
        // callBack(flowPoolConsumerContext);

        log.info("==============停用流程结束，卡{}================", flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());


    }

    /**
     * Upcc/HSS做交互
     *
     * @param flowPoolConsumerContext
     */
    @Override
    public boolean tryOutsideNet(FlowPoolConsumerContext flowPoolConsumerContext) {
        try {

            log.info("========================这是第{}次进行外部网元交互重试============================",
                    flowPoolConsumerContext.getRetryTimes());

            FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                    .getFlowPoolRabbitMQMessage();

            HcardInfo hcardInfo = flowPoolConsumerContext.getHcardInfo();

            boolean useFlowPool = useFlowPool(flowPoolConsumerContext);

            //若不相等，什么操作都不做
            if (useFlowPool) {

                log.info("==========================该卡使用的是流量池=============================");

                /**
                 * 卡类型: 1:H  2:V，若卡为V卡，销户HSS
                 */
                if ("2".equals(messageVO.getCardType())) {

                    VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                            .getVcardAccountInfo(messageVO.getImsi()));
                    log.info("查询v卡信息：{}", JSONObject.toJSONString(vcardInfo));

                    CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                            .getCardPoolByImsi(messageVO.getImsi()));
                    log.info("查询卡池信息：{}", JSONObject.toJSONString(cardPool));

                    RmvSubVO rmvSubVO = new RmvSubVO(messageVO.getImsi());

                    if (vcardInfo.getRouteId() != null){
                        log.info("删除路由");
                        coreNetCaller.deleteRoute(vcardInfo.getImsi(), vcardInfo.getRouteId());
                        pmsFeignClient.routeSetNull(vcardInfo.getImsi());
                    }

                    log.info("HSS 销户:{}", JSONObject.toJSONString(rmvSubVO));
                    Response.getAndCheckRemoteData(controlFeignClient.delHssSubscriber(rmvSubVO));


                    //is_sign_upcc 是否去UPCC动态签约;1 true|0 false,注意该字段可能为空
                    //UPCC进行销户（V卡需要判断是动态签约才调用）
                    log.info("cardPool.getIsSignUpcc:{}", cardPool.getIsSignUpcc());
                    if (StringUtils.isNotBlank(cardPool.getIsSignUpcc()) &&
                            "1".equals(cardPool.getIsSignUpcc())) {

                        log.info("UPCC销户:{}", vcardInfo.getMsisdn());
                        Response.getAndCheckRemoteData(controlFeignClient.delUpccSubscriber(vcardInfo.getMsisdn(), null));

                    }

                    //H卡直接upcc签约0K模板
                } else {

                    if (!UpccOpenStatusEnum.SUCCESS.getVal().equals(hcardInfo.getUpccOpenStatus())) {
                        log.debug("imsi: {} 对应主卡UPCC开户状态不为1，需要重新开户", hcardInfo.getImsi());
                        controlFeignClient.addUpccSubscriber(AddSubscriber.builder()
                                .usrIdentifier(hcardInfo.getMsisdn())
                                .usrMSISDN(hcardInfo.getMsisdn())
                                .build());
                    }

                    Response response;
                    String signUpccid = zeroSpeedUpccId;
                    if (StrUtil.isNotBlank(hcardInfo.getExpireUpccSignId())){
                        log.info("签约主卡套餐过期模板");
                        response = upccUtil.UpccSignExprieId(hcardInfo.getMsisdn(),hcardInfo.getExpireUpccSignId());
                        signUpccid = hcardInfo.getExpireUpccSignId();
                    }else {
                        log.info("签约0k模板");
                        response = upccUtil.UpccSignZeroId(hcardInfo.getMsisdn());
                    }

                    if (response.isOk()){
                        log.debug("UPCC签约0k/套餐过期模板成功，修改H卡状态 imsi: {}", hcardInfo.getImsi());
                        // H卡开户状态更新
                        pmsFeignClient.updateCardOpenStatus(UpdateOpenStatusReq.builder()
                                .imsi(hcardInfo.getImsi())
                                .upccSignStatus("1")
                                .upccSignId("3")
                                .upccSignBizId(signUpccid)
                                .build());
                    }

                }

            } else {
                log.info("==========================该卡使用的不是流量池，不做处理=============================");

            }

            log.info("=======================非常幸运，通过了，停用外部网元交互流程===========================");

            return true;
        } catch (Exception ex) {

            log.error("调用外部网元时发生致命错误，位置：停用流程");

            log.error("", ex);

            return false;
        }

    }
}
