package com.ebupt.cmi.clientmanagement.domain.dto.channelself;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskBatchDetailDTO.java
 * @Description 详情1 dto
 * @createTime 2021年06月17日 15:50:00
 */

@Data
public class TaskBatchDetailDTO {
    String iccid;
    String msisdn;
    String imsi;
    String cardStatus;
    String cardForm;
    String createTime;
    String corpName;
    //imsi, msisdn, iccid, card_form, create_time

    public TaskBatchDetailDTO(String imsi,
                              String msisdn,
                              String iccid,
                              String cardForm,
                              String createTime) {
        this.imsi = imsi;
        this.msisdn = msisdn;
        this.iccid = iccid;
        this.cardForm = cardForm;
        this.createTime = createTime;
    }

}
