package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 主卡出入库状态
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/4/16 15:57
 */
@Getter
@AllArgsConstructor
public enum UnsubscribeChanne {

    ROLLBACK("205", "回滚"),

    CUSTOMER_WEBSITE("206", "客服网站"),

    RECYCLING_RESOURCES("207", "终端回收资源");

    private String k;

    private String val;
}
