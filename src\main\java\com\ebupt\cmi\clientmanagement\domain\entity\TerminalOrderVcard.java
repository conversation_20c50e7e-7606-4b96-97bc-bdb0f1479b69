package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * TerminalOrderVcard
 *
 * @Author: wey
 * @Date: 2022/3/23 16:25
 */
@TableName("cms_terminal_order_vcard")
@Data
@ToString
@Builder
public class TerminalOrderVcard {

    @TableId
    private String orderTerminalId;

    private String orderUniqueId;

    private String imsi;

    private String packageId;

    private String mcc;


}
