package com.ebupt.cmi.clientmanagement.domain.dto.channelself;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.ebupt.excel.annotation.ExcelExport;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OrderDTO.java
 * @Description 订单DTO
 * @createTime 2021年06月18日 16:03:00
 */

@Data
@ApiModel
public class OrderDTO {
    @ApiModelProperty(value = "订单号，注意，不是订单表的主键，是uuid")
    @ExcelExport(description = "Order Number", index = "0")
    String orderId;

    @ApiModelProperty(value = "卡号")
    @ExcelExport(description = "ICCID", index = "1")
    String iccid;

    @ApiModelProperty(value = "套餐名称")
    String packageName;

    @ApiModelProperty(value = "套餐英文名称")
    @JsonProperty("packageNameEn")
    @ExcelExport(description = "Package Name", index = "2")
    String packageNameEn;

    @ApiModelProperty(value = "购买渠道")
    @ExcelExport(description = "Method", index = "3")
    String orderChannel;

    @ApiModelProperty(value = "订单状态")
    @ExcelExport(description = "Order Status", index = "4")
    String orderStatus;

    @ApiModelProperty(value = "数量")
    @ExcelExport(description = "QTY", index = "5")
    String count;

    @ApiModelProperty(value = "订单金额")
    @ExcelExport(description = "Order Amount", index = "6")
    BigDecimal amount;

    @ApiModelProperty(value = "创建日期")
    @ExcelExport(description = "Creation Time", index = "8")
    String orderDate;

    @ApiModelProperty(value = "套餐是否已使用")
    Boolean isUsed;

    @ApiModelProperty(value = "币种")
    String currencyCode;

    String packageUniqueId;

    String packageStatus;

    String effectiveDay;

    @ApiModelProperty(value = "订单类型")
    String orderType;

    @ApiModelProperty(value = "订单id,订单表主键id")
    Long id;

    @ExcelExport(description = "Cancelled Time", index = "9")
    String unsubscribeTime;

    @ExcelExport(description = "Update Time", index = "10")
    String updateTime;

    @ExcelIgnore
    String createTime;

    @ExcelIgnore
    String corpId;

    @ExcelIgnore
    String subCorpId;

    @ExcelExport(description = "Channel Order Amount", index = "7")
    BigDecimal subAmount;

    @ApiModelProperty(value = "归属渠道商")
    @ExcelExport(description = "Channel", index = "11")
    String revertChannelName;

}
