package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PmsRealname对象", description="")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsRealname implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "实名制id")
      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "实名制名称")
    private String name;

    @ApiModelProperty(value = "规则编码")
    private String ruleCode;

    @ApiModelProperty(value = "订单认证URL")
    private String orderUrl;

    @ApiModelProperty(value = "卡认证URL")
    private String cardUrl;

    @ApiModelProperty(value = "一证绑定卡数量")
    private Integer bindNum;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

}
