package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 上网明细表
 * @date 2021/4/20 15:57
 */

@TableName("cms_channel_surf_info")
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
@Builder
public class ChannelSurfInfo     {

    @TableId
    private Long surfId;
    private String packageUniqueId;

    @TableField("order_unique_id")
    private String order_unique_id;

    private String madeImsi;
    private String imsi;
    private String internet_type;
    private String internetType;
    private String pool_id;
    private String mcc;
    private String Himsi;
    private LocalDateTime start_time;
    private LocalDateTime end_time;
    private String corp_id;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Getter
    @AllArgsConstructor
    public enum InternetType {

        /**
         * 成功
         */
        H("1"),

        /**
         * 失败
         */
        V("2");

        private String type;

    }
}
