package com.ebupt.cmi.clientmanagement.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

@Configuration()
@ConfigurationProperties("malfunction-mail")
@Data
public class MalfunctionConfig {
    private String title;

    private String content;

    private List<String> mailAddressList;

}
