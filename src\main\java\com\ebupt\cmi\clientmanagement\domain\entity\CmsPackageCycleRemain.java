package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

/**
 * (CmsPackageCycleRemain)实体类
 *
 * <AUTHOR>
 * @since 2024-04-03 14:45:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsPackageCycleRemain{

    private Long id;
/**
     * 剩余流量类型1：套餐周期限量剩余流量2：加油包周期限量剩余流量3：待激活剩余流量4：定向应用剩余流量
     */
    private String flowType;
/**
     * 套餐唯一id
     */
    private String packageUniqueId;
/**
     * 应用组id
     */
    private String appGroupId;
/**
     * 剩余流量
     */
    private Long remainFlow;
/**
     * 过期时间
     */
    private String expireTime;

    public CmsPackageCycleRemain(Long id, String flowType, String packageUniqueId, String appGroupId, Long remainFlow) {
        this.id = id;
        this.flowType = flowType;
        this.packageUniqueId = packageUniqueId;
        this.appGroupId = appGroupId;
        this.remainFlow = remainFlow;
    }

    /**
     * 剩余流量类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum FlowTypeEnum {

        /**
         * 1：套餐周期限量剩余流量
         */
        PACKAGE_CYCLE_REMAIN("1"),

        /**
         * 2：加油包周期限量剩余流量
         */
        ADD_CYCLE_REMAIN("2"),

        /**
         * 3：待激活剩余流量
         */
        REACH_ACTIVE_REMAIN("3"),

        /**
         * 4：定向应用剩余流量
         */
        APP_REMAIN("4");

        @Getter
        private String value;

    }
}

