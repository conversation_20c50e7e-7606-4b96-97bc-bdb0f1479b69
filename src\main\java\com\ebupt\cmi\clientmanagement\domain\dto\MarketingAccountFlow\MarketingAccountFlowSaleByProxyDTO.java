package com.ebupt.cmi.clientmanagement.domain.dto.MarketingAccountFlow;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MarketingAccountFlowSaleByProxyDTO{

    @ExcelProperty(value = "Consumption date", index = 0)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    //消费日期
    private Date consumptionDate;

    @ExcelProperty(value = "Type", index = 1)
    @ContentStyle(dataFormat = 49)
    //类型
    private String type;

    @ExcelProperty(value = "Related activities", index = 2)
    @ContentStyle(dataFormat = 49)
    //活动
    private String activity;

    @ExcelProperty(value = "Currency", index = 3)
    @ContentStyle(dataFormat = 49)
    //币种
    private String currency;


    @ExcelProperty(value = "Consumption amount", index = 4)
    //交易金额，单位：分
    private BigDecimal amount;

    @ExcelProperty(value = "Total balance of marketing account", index = 5)
    //未过期活动总余额 单位：分
    private BigDecimal totalAmount;

    @ExcelIgnore
    //订单id
    private String orderId;

    @ExcelProperty(value = "Order Number", index = 6)
    @ContentStyle(dataFormat = 49)
    private String totalOrderId;

    @ExcelIgnore
    //账户余额（可用额度）单位：分
    private BigDecimal deposit;





}
