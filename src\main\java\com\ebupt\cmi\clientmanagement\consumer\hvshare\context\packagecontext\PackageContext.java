package com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packagecontext;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageContext.java
 * @Description 套餐延时激活上下文
 * @createTime 2022年03月14日 11:20:00
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class PackageContext extends BaseContext {

    private Set<String> vimsi;

    private String imsi;
}
