package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.checkerframework.checker.units.qual.A;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value="CmsChannelRelation对象", description="")
public class CmsChannelRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "厂商ID")
    private String corpId;

    @ApiModelProperty(value = "0级渠道商id")
    private String parentCorpId;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "创建时间默认值：CURRENT_TIMESTAMP")
    private Date createTime;

    @ApiModelProperty(value = "修改时间默认值：CURRENT_TIMESTAMP")
    private Date updateTime;
}
