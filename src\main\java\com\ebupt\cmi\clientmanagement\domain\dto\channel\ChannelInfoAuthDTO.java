package com.ebupt.cmi.clientmanagement.domain.dto.channel;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageRelation;
import com.ebupt.cmi.clientmanagement.feign.back.vo.User;
import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 渠道商详情表展示待审批
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(description = "渠道商详情")
public class ChannelInfoAuthDTO extends SearchDTO {

    @ApiModelProperty(value = "渠道商类型", required = false)
    private String type;

    @ApiModelProperty(value = "可购买套餐组", required = false)
    private Map<String, ChannelPackageRelation> packageGroups;

    @ApiModelProperty(value = "货币种类", required = false)
    private String currencyCode;

    @ApiModelProperty(value = "联系人邮箱", required = false)
    private String email;

    @ApiModelProperty(value = "套餐购买数量", required = false)
    private Integer accountNum;

    @ApiModelProperty(value = "直接比例", required = false)
    private Integer directRatio;

    @ApiModelProperty(value = "间接比例", required = false)
    private Integer indirectRatio;

    @ApiModelProperty(value = "限制类型", required = false)
    private Integer indirectType;

    @ApiModelProperty(value = "合约开始时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractStartTime;

    @ApiModelProperty(value = "合约结束时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractEndTime;

    @ApiModelProperty(value = "合约期承诺的金额", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal depositAmount;

    @ApiModelProperty(value = "APP_KEY", required = false)
    private String appKey;

    @ApiModelProperty(value = "APP_Secret", required = false)
    private String appSecret;

    @ApiModelProperty(value = "渠道商编号", required = false)
    private String channelCode;

    @ApiModelProperty(value = "渠道商通知URL", required = false)
    private String channelUrl;

    @ApiModelProperty(value = "购买次数", required = false)
    private Integer indirectCount;

    @ApiModelProperty(value = "创建的账户信息", required = false)
    private List<User> accounts;

    @ApiModelProperty(value = "购买折扣", required = false)
    private Integer discount;

    @ApiModelProperty(value = "押金重置金额", required = false)
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal resetPrice;
//
//    @ApiModelProperty(value = "押金重置开关", required = false)
//    private String depositeReset;

    @ApiModelProperty(value = "币种名称", required = false)
    private String currencyCodeName;

    @ApiModelProperty(value = "公司名称", required = false)
    private String companyName;

    @ApiModelProperty(value = "内部订单", required = false)
    private String internalOrder;

    @ApiModelProperty(value = "地址", required = false)
    private String address;

    @ApiModelProperty(value = "激活通知开关", required = false)
    private String activateNotification;

    @ApiModelProperty(value = "渠道商类型", required = false)
    private String channelType;

    @ApiModelProperty(value = "退订规则", required = false)
    private String unsubscribeRule;

    @ApiModelProperty(value = "总额度", required = false)
    private BigDecimal totalDeposit;

    @ApiModelProperty(value = "激活通知URL", required = false)
    private String activateNotificationUrl;

    private int newPackageNum;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal runoutofBalanceRemindThreshold;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal prohibitiveBuyRemindThreshold;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal stopUseRemindThreshold;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zPreDeposit;

    private String a2zCardUseSwitch;

    @JsonIgnore
    private String channelCooperationModelString;

    private List<String> channelCooperationMode;

    @JsonIgnore
    private String packageUsePercentageString;

    private List<PackageUsePercentage> packageUsePercentage;

    private String overdueNotify;

    private String overdueNotifyUrl;

    private ChannelInfoDTO authObj;

    private String packageUseNotifyUrl;

    private List<String> appids;

    private Set<String> appBindIds;

//    private List<String> appidsAuth;

    /**
     * esim通知开关
     * 1：开
     * 2：关
     * 默认值：2
     */
    private String esimNotification;

    /**
     * esim通知url
     */
    private String esimNotificationUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date a2zContractStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date a2zContractEndTime;

    private String salesMail;

    private String approvalPackage;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zDepositAmount;

    private List<String> groupId;

    private List<String> a2zRuleId;

    private List<String> resourceRuleId;

    private List<FreeImsiVO> freeImsiVo;

    private List<SmsTemplateVO> smsTemplateVo;

    private String distributionAccountingPeriodId;

    private String a2zAccountingPeriodId;

    private String a2zChannelType;

    private String resourceAccountingPeriodId;

    private BigDecimal resourceRunoutofBalanceRemindThreshold;

    private BigDecimal resourceProhibitiveBuyRemindThreshold;

    private BigDecimal resourceStopUseRemindThreshold;

    private String resourceChannelType;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal marketingAmount;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal creditAmount;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PackageUsePercentage {
        private Integer index;
        private String value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FreeImsiVO {
        private String ruleId;
        private String cooperationMode;
        private String ruleName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SmsTemplateVO {
        private Long templateId;
        private String templateName;
        private String cooperationMode;
    }
}
