package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 渠道商详情
 *
 * <AUTHOR>
 * @date 2021-5-25 11:44:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("cms_channel_distributors_detail")
public class ChannelDistributorDetail extends BaseEntity {

    private Long id;
    private String corpId;

    /**
     * 是否允许订购
     * 1：允许
     * 2：不允许
     */
    private String isSub;
    /**
     * 渠道商编码
     */
    private String channelCode;

    /**
     * 渠道商通知URL
     */
    private String channelUrl;
    private String appKey;
    private String appSecret;
    private String depositeReset;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal resetPrice;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal deposit;
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal depositeRemindThreshold;

    @TableField("currency_code")
    private String currencyCode;

    /**
     * 折扣，0-100
     */
    private Integer discount;

    /**
     * 合约开始时间
     */
    private Date contractStartTime;

    /**
     * 合约结束时间
     */
    private Date contractEndTime;

    /**
     * 合约期承诺销售金额
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal depositAmount;

    private Integer directRatio;
    private Integer indirectType;

    private Integer indirectRatio;
    private Integer indirectCount;
    private String email;
    private Integer accountNum;

    /**
     * 1.押金模式
     * 2.预存模式
     */
    private String channelType;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal totalDeposit;
    /**
     * 激活通知开关 1:开  2:关
     */
    private String activateNotification;
    /**
     * 退订规则 1:自然月内退订  2:有效期内退订
     */
    private String unsubscribeRule;
    /**
     * 激活通知URL
     */
    private String activateNotificationUrl;

    private String allowNewPackage;

    private Integer limitPackageNum;

    private Long groupId;
    /**
     * 渠道商合作模式
     * 1  代销
     * 2  A2Z
     */
    private String channelCooperationMode;

    /**
     * 额度用尽提醒阈值
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal runoutofBalanceRemindThreshold;

    /**
     * 禁止购买提醒阈值
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal prohibitiveBuyRemindThreshold;

    /**
     * 停止使用提醒阈值
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal stopUseRemindThreshold;

    /**
     * a2z预存款额度
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zPreDeposit;

    /**
     * a2z已用信用额度
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal a2zUsedDeposit;

    /**
     * a2z卡控制按钮
     * 1: 启用 （默认）
     * 2：停用
     * 3：停用中
     * 4：启动中
     */
    private String a2zCardUseSwitch;

    private Integer refuelProfitMargin;

    private String packageUsePercentage;

    private String overdueNotify;

    private String overdueNotifyUrl;

    private String packageUseNotifyUrl;

    private String esimNotification;

    private String esimNotificationUrl;

    private String whetherUpdateThreshold;

    private Date a2zContractStartTime;

    private Date a2zContractEndTime;

    private String salesMail;

    private String approvalPackage;

    private Long distributionAccountingPeriodId;

    private Long a2zAccountingPeriodId;

    private Long resourceAccountingPeriodId;

    private String isSubA2z;

    private BigDecimal a2zDepositAmount;

    private String a2zChannelType;

    private BigDecimal resourceRunoutofBalanceRemindThreshold;

    private BigDecimal resourceProhibitiveBuyRemindThreshold;

    private BigDecimal resourceStopUseRemindThreshold;

    private String resourceChannelType;

    private String contractConsignmentNotify;

    private String a2zConsignmentNotify;

    private String pushPercentage;

    private String pushPercentageA2z;

    private BigDecimal marketingAmount;

    private BigDecimal creditAmount;

    private BigDecimal a2zMarketingAmount;

    @TableField(exist = false)
    private String isRefuel;

    /**
     * 是否允许订购枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum PermitSubscribeEnum {

        /**
         * 允许
         */
        YES("1"),

        /**
         * 不允许
         */
        NO("2");

        private String value;

        public boolean matches(String value) {
            return this.value.equals(value);
        }

    }

    /**
     * 激活通知开关 1:开  2:关
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum ActivateNotificationEnum {

        /**
         * 开
         */
        OPEN("1"),

        /**
         * 关
         */
        CLOSE("2");

        private String value;

        public boolean matches(String value) {
            return this.value.equals(value);
        }
    }

    /**
     * 是否允许订购枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum AllowNewChannelPackage {

        /**
         * 允许
         */
        YES("1"),

        /**
         * 不允许
         */
        NO("2");

        private String value;

    }

    /**
     * 渠道商合作模式枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum CooperationModeEnum {

        /**
         * 代销
         */
        CONSIGNMENT("1"),

        /**
         * A2Z
         */
        A2Z("2"),
        /**
         * 资源合作
         */
        RESOURCE_COOPERATION("3");

        private String type;

    }

}
