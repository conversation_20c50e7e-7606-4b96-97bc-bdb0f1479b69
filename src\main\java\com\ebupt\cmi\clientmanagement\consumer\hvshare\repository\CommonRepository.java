package com.ebupt.cmi.clientmanagement.consumer.hvshare.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.BaseMessageVO;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsDelayMessageRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.hvshare.PackageConsumeErrorLog;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.mapper.CmsDelayMessageRecordMapper;
import com.ebupt.cmi.clientmanagement.mapper.hvshare.PackageConsumeErrorLogMapper;
import com.ebupt.cmi.clientmanagement.utils.StringUtil;
import com.ebupt.elk.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CommonRepository.java
 * @Description 处理hv共享中一些通用的数据库操作
 * @createTime 2022年02月28日 16:44:00
 */
@Component
@Slf4j
public class CommonRepository {
    @Resource
    PackageConsumeErrorLogMapper packageConsumeErrorLogMapper;

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Resource
    CmsDelayMessageRecordMapper cmsDelayMessageRecordMapper;

    /**
     * 插入错误日志记录表
     *
     * @param baseContext   上下文
     * @param errorDescribe 错误描述
     */
    public void insertErrorLog(BaseContext baseContext,
                               String errorDescribe) {

        PackageConsumeErrorLog packageConsumeErrorLog = PackageConsumeErrorLog
                .builder()
                .createTime(LocalDateTime.now())
                .himsi(baseContext.getMessageVO().getHimsi())
                .imsi(baseContext.getMessageVO().getImsi())
                .iccid(baseContext.getMessageVO().getIccid())
                .errorDescribe(errorDescribe)
                .internetType(baseContext.getMessageVO().getCardType())
                .queueName(baseContext.getQueueEnum().getName())
                .msisdn(baseContext.getMessageVO().getMsisdn())
                .packageUniqueId(baseContext.getMessageVO().getPackageUniqueId())
                .build();

        packageConsumeErrorLogMapper.insert(packageConsumeErrorLog);

    }

    /**
     * 回写upcc的签约状态为失败
     *
     * @param baseContext 上下文而已
     */
    public void changeVcardUpccSignStatus(BaseContext baseContext) {

        try {
            BaseMessageVO messageVO = baseContext
                    .getMessageVO();

            //upcc签约状态
            //1:：成功
            //2:失败
            UpdateOpenStatusReq updateOpenStatusReq =
                    UpdateOpenStatusReq
                            .builder()
                            .imsi(messageVO.getImsi())
                            .upccSignStatus("2")
                            .build();

            Response.getAndCheckRemoteData(pmsFeignClient.updateVimsiOpenStatus(updateOpenStatusReq));

        } catch (Exception ex) {

            log.error(Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));

            log.error("", ex);

        }

    }

    public boolean getMessageById(String id) {
        if (StringUtils.isEmpty(id)) {
            return false;
        }
        return cmsDelayMessageRecordMapper.selectOne(Wrappers.lambdaQuery(CmsDelayMessageRecord.class)
                .eq(CmsDelayMessageRecord::getId, id)) == null;
    }


    public void deleteMessage(String id) {
        if (StringUtils.isEmpty(id)) {
            return;
        }
        cmsDelayMessageRecordMapper.deleteById(id);
    }
}
