# 渠道商详情查询接口实现方案

## 1. 接口概述

### 1.1 接口基本信息
- **接口名称**: 查询渠道商基本信息
- **接口路径**: `/channel/distributors/info`
- **请求方式**: GET
- **接口描述**: 根据渠道商ID(corpId)查询渠道商的详细信息

### 1.2 接口定义
```java
@GetMapping("/info")
@ApiOperation(value = "查询渠道商基本信息")
public Response<ChannelInfoAuthDTO> getDetailInfo(@RequestParam @ApiParam(value = "corpId", name = "corpId", required = true) String corpId)
```

## 2. 实现架构

### 2.1 分层架构
```
Controller层 -> Service层 -> Mapper层 -> 数据库
     ↓            ↓           ↓
ChannelDistributorsController -> ChannelDistributorsService -> ChannelMapper
```

### 2.2 核心类说明
- **Controller**: `ChannelDistributorsController`
- **Service接口**: `ChannelDistributorsService`
- **Service实现**: `ChannelDistributorsServiceImpl`
- **返回对象**: `ChannelInfoAuthDTO`
- **响应包装**: `Response<T>`

## 3. 详细实现逻辑

### 3.1 Controller层实现
```java
@GetMapping("/info")
@ApiOperation(value = "查询渠道商基本信息")
public Response<ChannelInfoAuthDTO> getDetailInfo(@RequestParam @ApiParam(value = "corpId", name = "corpId", required = true) String corpId) {
    return channelDistributorsService.getDetailInfo(corpId);
}
```

### 3.2 Service层核心实现逻辑

#### 3.2.1 主要业务流程
1. **查询渠道商基本信息**
   - 通过`channelMapper.getChannelInfoDTO(corpId)`查询渠道商基本信息
   - 验证查询结果，如果为空则抛出业务异常

2. **金额单位转换**
   - 将数据库中的分为单位的金额转换为元为单位
   - 涉及字段：押金提醒阈值、押金、押金金额、重置价格、总押金

3. **查询授权信息**
   - 检查渠道商是否有授权记录
   - 如果有授权，查询并填充授权相关信息

4. **查询套餐组信息**
   - 获取渠道商可购买的套餐组信息
   - 构建套餐组映射关系

5. **查询免费IMSI规则**
   - 获取渠道商的免费IMSI规则配置
   - 填充规则名称等详细信息

6. **查询短信模板信息**
   - 获取渠道商关联的短信模板
   - 包含模板ID、名称、合作模式等信息

#### 3.2.2 关键代码片段
```java
@Override
public Response<ChannelInfoAuthDTO> getDetailInfo(String corpId) {
    // 1、查询渠道商
    ChannelInfoAuthDTO channelInfoDTO = channelMapper.getChannelInfoDTO(corpId);
    Optional.ofNullable(channelInfoDTO).orElseThrow(() -> new BizException("渠道商信息查询失败"));
    
    // 2、金额单位转换（分转元）
    channelInfoDTO.setDepositeRemindThreshold(getYuanAmount(channelInfoDTO.getDepositeRemindThreshold()));
    channelInfoDTO.setDeposit(getYuanAmount(channelInfoDTO.getDeposit()));
    channelInfoDTO.setDepositAmount(getYuanAmount(channelInfoDTO.getDepositAmount()));
    channelInfoDTO.setResetPrice(getYuanAmount((channelInfoDTO.getResetPrice())));
    channelInfoDTO.setTotalDeposit(getYuanAmount(channelInfoDTO.getTotalDeposit()));
    
    // 3、查询授权信息
    Integer authCount = channelAuthMapper.selectCount(Wrappers.<CmsChannelAuth>lambdaQuery()
            .eq(CmsChannelAuth::getCorpId, corpId));
    if (authCount > 0) {
        // 处理授权信息...
    }
    
    // 4、查询套餐组信息
    // 5、查询免费IMSI规则
    // 6、查询短信模板信息
    
    return Response.ok(channelInfoDTO);
}
```

## 4. 数据传输对象(DTO)

### 4.1 ChannelInfoAuthDTO主要字段
```java
public class ChannelInfoAuthDTO extends SearchDTO {
    private String type;                    // 渠道商类型
    private String currencyCode;            // 货币种类
    private String email;                   // 联系人邮箱
    private Integer accountNum;             // 套餐购买数量
    private Integer directRatio;            // 直接比例
    private Integer indirectRatio;          // 间接比例
    private String channelCode;             // 渠道商编号
    private String channelUrl;              // 渠道商通知URL
    private BigDecimal resetPrice;          // 押金重置金额
    private String currencyCodeName;        // 币种名称
    private List<FreeImsiVO> freeImsiVo;    // 免费IMSI规则
    private List<SmsTemplateVO> smsTemplateVo; // 短信模板
    // ... 其他字段
}
```

### 4.2 Response响应包装
```java
public class Response<T> {
    private T data;        // 数据
    private Long count;    // 数量
    private String code;   // 响应码
    private String msg;    // 响应消息
}
```

## 5. 异常处理

### 5.1 业务异常
- **BizException**: 当渠道商信息查询失败时抛出
- **异常信息**: "渠道商信息查询失败"

### 5.2 参数验证
- corpId参数为必填项，通过`@RequestParam(required = true)`进行验证

## 6. 数据库操作

### 6.1 主要查询操作
1. `channelMapper.getChannelInfoDTO(corpId)` - 查询渠道商基本信息
2. `channelAuthMapper.selectCount()` - 查询授权记录数量
3. `channelMapper.getChannelInfoAuth(corpId)` - 查询授权信息
4. 套餐组、IMSI规则、短信模板等相关查询

### 6.2 数据处理
- **金额转换**: 使用`getYuanAmount()`方法将分转换为元
- **对象拷贝**: 使用`BeanUtils.copyProperties()`进行对象属性拷贝
- **集合操作**: 使用Stream API和Lambda表达式处理集合数据

## 7. 性能考虑

### 7.1 查询优化
- 使用MyBatis-Plus的条件构造器进行精确查询
- 避免N+1查询问题，合理使用关联查询

### 7.2 缓存策略
- 可考虑对频繁查询的渠道商信息进行缓存
- 使用Redis等缓存中间件提升查询性能

## 8. 安全考虑

### 8.1 参数校验
- 对输入的corpId进行格式和长度校验
- 防止SQL注入攻击

### 8.2 权限控制
- 确保只有有权限的用户才能查询渠道商信息
- 可结合Spring Security进行权限控制

## 9. 测试建议

### 9.1 单元测试
- 测试正常查询流程
- 测试异常情况（如corpId不存在）
- 测试金额转换逻辑

### 9.2 集成测试
- 测试完整的请求响应流程
- 测试数据库连接和查询
- 测试异常处理机制

## 10. 部署和监控

### 10.1 日志记录
- 记录关键业务操作日志
- 记录异常信息便于问题排查

### 10.2 性能监控
- 监控接口响应时间
- 监控数据库查询性能
- 设置告警机制
