package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 四川移动扣费配置
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties("deduct-config")
public class DeductConfigProperties {

    private UrlSuffix urlSuffix;
    private Rsa rsa;
    private String status;

    @Data
    public static class Rsa {

        private String privateKey;
        private String publicKey;

    }

    @Data
    public static class UrlSuffix {

        /**
         * 号码查询URL
         */
        private String queryUserPhone;

        /**
         * 扣费URL
         */
        private String nationInterFaceDeal;

    }

}
