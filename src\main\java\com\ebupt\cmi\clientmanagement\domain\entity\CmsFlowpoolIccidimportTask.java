package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/1/14 14:45
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsFlowpoolIccidimportTask {


    private Long id;

    private String flowPoolId;

    private Integer importCount;

    private Integer successCount;

    private Integer failCount;

    private String successFilePath;

    private String failFilePath;

    private Character taskStatus;

    private String fileName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
