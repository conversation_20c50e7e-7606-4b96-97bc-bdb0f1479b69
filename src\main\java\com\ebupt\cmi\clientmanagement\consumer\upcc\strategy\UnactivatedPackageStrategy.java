package com.ebupt.cmi.clientmanagement.consumer.upcc.strategy;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.RetryContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccSignContext;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class UnactivatedPackageStrategy extends AbstractStrategy {
    @Override
    public void doBiz(UpccSignContext context) {
        coreNetCaller.upccSignature(context.getMsisdn(), context.getNewUpccSignBizId());
    }

    @Override
    public void afterFail(RetryContext retryContext) {
        PackageDirectionRelation packageDirectionRelation = retryContext.getPackageDirectionRelation();
        if (packageDirectionRelation.getDirectType().equals(PackageDirectionRelation.DirectType.FREE_FLOW.getValue()) && retryContext.isHasSetFlow()) {
            log.debug("待激活流程设置应用流量");
            CmsPackageCycleRemain cmsPackageCycleRemain = new CmsPackageCycleRemain(null, CmsPackageCycleRemain.FlowTypeEnum.APP_REMAIN.getValue(),
                    packageDirectionRelation.getPackageUniqueId(), packageDirectionRelation.getAppGroupId(), packageDirectionRelation.getFlowLimitSum());
            flowOperationUtils.insertCycleRemainFlow(Collections.singletonList(cmsPackageCycleRemain));
        }
    }

    @Override
    public void afterSuccess(RetryContext retryContext) {
        PackageDirectionRelation packageDirectionRelation = retryContext.getPackageDirectionRelation();
        if (packageDirectionRelation.getHasUsed().equals("1")) {
            log.debug("签约成功，更改应用状态");
            packageDirectionRelation.setHasUsed(PackageDirectionRelation.Status.USING.getValue());
            packageDirectionRelationMapper.updateById(packageDirectionRelation);
        }
        if (!retryContext.isHasSetFlow() && packageDirectionRelation.getDirectType().equals(PackageDirectionRelation.DirectType.FREE_FLOW.getValue()) ) {
            log.debug("待激活流程设置应用流量");
            CmsPackageCycleRemain cmsPackageCycleRemain = new CmsPackageCycleRemain(null, CmsPackageCycleRemain.FlowTypeEnum.APP_REMAIN.getValue(),
                    packageDirectionRelation.getPackageUniqueId(), packageDirectionRelation.getAppGroupId(), packageDirectionRelation.getFlowLimitSum());
            flowOperationUtils.insertCycleRemainFlow(Collections.singletonList(cmsPackageCycleRemain));
        }
    }

    @Override
    public void afterSuccess(UpccSignContext upccSignContext) {
        upccSignContext.getCmsCardUpccRecord().setUpdateTime(new Date());
        cmsCardUpccRecordService.saveOrUpdate(upccSignContext.getCmsCardUpccRecord());
    }

    @Override
    public <T extends UpccConsumerContext> void handle(T context) {

        UpccConsumerContext upccConsumerContext = (UpccConsumerContext) context;

        String packageUniqueId = upccConsumerContext.getPackageUniqueId();
        Long appId = upccConsumerContext.getAppId();

        RetryContext retryContext = new RetryContext();
        retryContext.setPackageDirectionRelation(upccConsumerContext.getPackageDirectionRelation());
        retryContext.setHasSetFlow(upccConsumerContext.isHasSetFlow());
        ArrayList<UpccSignContext> upccSignContexts = new ArrayList<>();


        String oldBizId = null;
        String upccSignIdToV;
        String upccSignIdToH;
        CmsCardUpccRecord cmsCardUpccRecordToSave = new CmsCardUpccRecord();

        List<CmsPackageCardUpccRelation> cmsPackageCardUpccRelations = cmsPackageCardUpccRelationMapper.selectList(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                .eq(CmsPackageCardUpccRelation::getAppId, appId)
                .orderByAsc(CmsPackageCardUpccRelation::getConsumption)
                .last("limit 2"));

        if (upccConsumerContext.getVimsi() != null) {

            upccSignIdToV = cmsPackageCardUpccRelations.get(1).getUpccSignId();
            upccSignIdToH = cmsPackageCardUpccRelations.get(0).getUpccSpeed() < cmsPackageCardUpccRelations.get(1).getUpccSpeed() ?
                    cmsPackageCardUpccRelations.get(0).getUpccSignId() : upccSignIdToV;
            log.debug("H卡签约模板：{}，V卡签约模板：{}", upccSignIdToH, upccSignIdToV);

            CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                    .eq(CmsCardUpccRecord::getAppId, appId)
                    .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getVimsi()));

            if (cmsCardUpccRecord != null) {
                oldBizId = cmsCardUpccRecord.getUpccSignBizId();
                cmsCardUpccRecordToSave.setId(cmsCardUpccRecord.getId());
            }

            if (!upccSignIdToV.equals(oldBizId)) {
                UpccSignContext upccSignContext = new UpccSignContext();
                upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToV());
                upccSignContext.setNewUpccSignBizId(upccSignIdToV);
                upccSignContext.setOldUpccSignBizId(oldBizId);
                cmsCardUpccRecordToSave.setAppId(appId);
                cmsCardUpccRecordToSave.setImsi(upccConsumerContext.getVimsi());
                cmsCardUpccRecordToSave.setUpccSignBizId(upccSignIdToV);
                upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecordToSave);
                log.debug("V卡签约模板对不上，需要签约，{}", upccSignContext);
                upccSignContexts.add(upccSignContext);
            }

            cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                    .eq(CmsCardUpccRecord::getAppId, appId)
                    .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getHImsi()));
            oldBizId = null;
            cmsCardUpccRecordToSave = new CmsCardUpccRecord();
            if (cmsCardUpccRecord != null) {
                oldBizId = cmsCardUpccRecord.getUpccSignBizId();
                cmsCardUpccRecordToSave.setId(cmsCardUpccRecord.getId());
            }
            if (!upccSignIdToH.equals(oldBizId)) {
                UpccSignContext upccSignContext = new UpccSignContext();
                upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToH());
                upccSignContext.setNewUpccSignBizId(upccSignIdToH);
                upccSignContext.setOldUpccSignBizId(oldBizId);
                cmsCardUpccRecordToSave.setAppId(appId);
                cmsCardUpccRecordToSave.setImsi(upccConsumerContext.getHImsi());
                cmsCardUpccRecordToSave.setUpccSignBizId(upccSignIdToH);
                upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecordToSave);
                upccSignContexts.add(upccSignContext);
                log.debug("H卡签约模板对不上，需要签约，{}", upccSignContext);
            }
            retryContext.setUpccSignContext(upccSignContexts);


        } else {
            upccSignIdToH = cmsPackageCardUpccRelations.get(1).getUpccSignId();

            CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                    .eq(CmsCardUpccRecord::getAppId, appId)
                    .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getHImsi()));
            if (cmsCardUpccRecord != null) {
                cmsCardUpccRecordToSave.setId(cmsCardUpccRecord.getId());
                oldBizId = cmsCardUpccRecord.getUpccSignBizId();
            }
            if (!upccSignIdToH.equals(oldBizId)) {
                UpccSignContext upccSignContext = new UpccSignContext();
                upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToH());
                upccSignContext.setNewUpccSignBizId(upccSignIdToH);
                upccSignContext.setOldUpccSignBizId(oldBizId);
                cmsCardUpccRecordToSave.setAppId(upccConsumerContext.getAppId());
                cmsCardUpccRecordToSave.setImsi(upccConsumerContext.getHImsi());
                cmsCardUpccRecordToSave.setUpccSignBizId(upccSignIdToH);
                upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecordToSave);
                upccSignContexts.add(upccSignContext);
                log.debug("H卡签约模板对不上，需要签约，{}", upccSignContext);
            }
            retryContext.setUpccSignContext(upccSignContexts);
        }

        upccConsumerContext.setRetryContext(retryContext);

    }
}
