package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 16:50
 */

@AllArgsConstructor
@Getter
public enum CardPoolStatus {

    /**
     * 待分配
     */
    TO_BE_ALLOCATED("1"),

    /**
     * 已分配
     */
    ALLOCATED("2"),
    /**
     * 正常
     */
    NORMAL("3"),

    /**
     * 暂停
     */
    SUSPEND("4"),

    /**
     * 、冻结
     */
    FROZEN("5");

    String status;
}
