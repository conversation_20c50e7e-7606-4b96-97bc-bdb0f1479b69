package com.ebupt.cmi.clientmanagement.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchDeliveryFail {
    @ExcelProperty(value = "订单编号",index = 0)
    @ContentStyle(dataFormat = 70)
    private String orderId;

    @ExcelProperty(value = "ICCID",index = 1)
    @ContentStyle(dataFormat = 70)
    private String iccid;

    @ExcelProperty(value = "失败原因",index = 2)
    @ContentStyle(dataFormat = 70)
    private String failMessage;
}
