package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2021/10/12 16:23
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cms_package_delay_task_query")
@Builder
public class PackageDelayTaskQuery {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 上传文件名称
     */
    private String taskFileName;

    /**
     * 结果文件名称
     */
    private String resultFileName;

    /**
     * 上传文件存在路径
     */
    @JsonIgnore
    private String taskFilePath;

    /**
     * 结果文件存在路径
     */
    @JsonIgnore
    private String resultFilePath;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-mm-dd HH:MM:SS")
    private String createTime;
}
