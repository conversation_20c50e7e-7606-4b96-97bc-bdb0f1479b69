package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Desc
 * @<PERSON> h<PERSON> l<PERSON>ong
 * @Date 2024/03/06 11:00
 */
@Data
@Accessors(chain = true)
@TableName("cms_custom_distributed_lock")
public class Locks {

	private Long id;

	private String keyId;

	private String value;

	private long createTime;

	private long expireTime;
}
