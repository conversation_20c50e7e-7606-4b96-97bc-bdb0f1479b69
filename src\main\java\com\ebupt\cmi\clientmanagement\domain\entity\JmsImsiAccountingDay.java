package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class JmsImsiAccountingDay {

    private Long id;
    //出账金额
    private String corpId;
    //出账金额
    private BigDecimal amount;
    //imsi
    private String imsi;
    //出账月份
    private String date;
    //合作模式 1: 代销 2:A2Z
    private String cooperationMode;
    //创建时间默认值：CURRENT_TIMESTAMP
    private Date createTime;



}