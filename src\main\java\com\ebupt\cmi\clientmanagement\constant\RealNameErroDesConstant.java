package com.ebupt.cmi.clientmanagement.constant;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RealNameErroDesConstant.java
 * @Description 实名制ocr错误记录
 * @createTime 2021年12月08日 16:32:00
 */


@Component
public class RealNameErroDesConstant {

    public static Map<String, Describe> globalConstant = new HashMap<>();

    public static Map<String, String> USESTATUSMAP = new HashMap<>();

    public static Map<String, String> AUTHSTATUSMAP = new HashMap<>();

    /**
     * 证件类型
     */
    public static Map<String, String> CREADTYPE = new HashMap<>();

    /**
     * ocr认证失败原因 1、姓名校验不一致 2、证件已过期 3、证件ID校验不一致 4、未满16周岁 0、OCR识别异常
     * 5超出1证X号限制
     */
    @PostConstruct
    public void init() {

        //姓名校验不一致
        Describe nameAuth = Describe
                .builder()
                .english("Name mismatch")
                .simple("姓名校验不一致")
                .traditional("姓名校驗不一致")
                .build();
        globalConstant.put("1", nameAuth);

        //证件已过期
        Describe IDexpire = Describe
                .builder()
                .english("ID expired")
                .simple("证件已过期")
                .traditional("證件已過期")
                .build();

        globalConstant.put("2", IDexpire);

        //证件Id校验不一致
        Describe IDNumberIssue = Describe
                .builder()
                .english("ID number mismatch")
                .simple("证件ID校验不一致")
                .traditional("證件ID校驗不一致")
                .build();

        globalConstant.put("3", IDNumberIssue);

        //未满16周岁
        Describe sixteenNotYet = Describe
                .builder()
                .english("Under 16 years old")
                .simple("未满16周岁")
                .traditional("未滿16周歲")
                .build();

        globalConstant.put("4", sixteenNotYet);

        //OCR识别异常
        Describe OCRabnormal = Describe
                .builder()
                .english("ID validation failed")
                .simple("证件校验失败")
                .traditional("證件校驗失敗")
                .build();

        globalConstant.put("0", OCRabnormal);

        //超出1证X号限制
        Describe Exceed1IDX = Describe
                .builder()
                .english("Exceed 1 ID %1$s card(s) limitation")
                .simple("超出1证%1$s号限制")
                .traditional("超出1證%1$s號限制")
                .build();

        globalConstant.put("5", Exceed1IDX);

        USESTATUSMAP.put("1","处理中");
        USESTATUSMAP.put("2","在用");
        USESTATUSMAP.put("3","备份");
        USESTATUSMAP.put("4", "停用");

        AUTHSTATUSMAP.put("1","待认证");
        AUTHSTATUSMAP.put("2","认证中");
        AUTHSTATUSMAP.put("3","认证通过");
        AUTHSTATUSMAP.put("4","认证失败");
        AUTHSTATUSMAP.put("5","证件已过期");

        //证件类型 1、护照 2、港澳通行证 3、香港身份证 4、澳门身份证
        CREADTYPE.put("1","护照");
        CREADTYPE.put("2","港澳通行证");
        CREADTYPE.put("3","香港身份证");
        CREADTYPE.put("4","澳门身份证");


    }

}
