package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CmsFlowpoolInfoCycle对象", description = "")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("cms_flowpool_info_cycle")
public class CmsFlowpoolInfoCycle implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "流量池id，按照规则自生成")
    private String flowPoolId;

    @ApiModelProperty(value = "流量池唯一ID")
    private String flowPoolUniqueId;

    @ApiModelProperty(value = "流量池名称，全局唯一")
    private String flowPoolName;

    @ApiModelProperty(value = "英文流量池名称")
    private String nameEn;

    @ApiModelProperty(value = "繁体中文名称")
    private String nameTw;

    @ApiModelProperty(value = "客户ID")
    private String corpId;

    @ApiModelProperty(value = "客户名称")
    private String corpName;

    @ApiModelProperty(value = "总流量，单位byte")
    private Long flowSum;

    @ApiModelProperty(value = "控制逻辑	1：达量限速	2：达量停用	3：达量停用	")
    private String controlLogic;

    @ApiModelProperty(value = "当前控制逻辑	1、达量限速	2、达量停用	3、正常	")
    private String currentControl;

    @ApiModelProperty(value = "开始日期")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "流量池价格")
    private BigDecimal flowPoolPrice;

    @ApiModelProperty(value = "流量池超出单价，，单位：币种/G ")
    private BigDecimal flowPoolExtraPrice;

    @ApiModelProperty(value = "提醒阈值")
    private Integer alarmThreshold;

    @ApiModelProperty(value = "是否提醒（默认：否）	1、是2、否	")
    private String isAlarm;

    @ApiModelProperty(value = "流量充值量")
    private Long recharge;

    @ApiModelProperty(value = "状态：	1、应用	2、过期	")
    private String status;

    @ApiModelProperty(value = "卡单周期刷新时间	YYYYMMDDhhmmss		")
    private String cardExpireTime;

    @ApiModelProperty(value = "流量池周期过期时间	YYYYMMDDhhmmss	")
    private String poolExpireTime;

    @ApiModelProperty(value = "周期类型：	1：24小时	2：自然日	3：自然月	4：自然年	")
    private String cycleType;

    @ApiModelProperty(value = "周期数")
    private Long cycleNum;

    @ApiModelProperty(value = "实际使用总流量")
    private Long useNum;

    @ApiModelProperty(value = "超额收入")
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField("upcc_hign_sign_id")
    @ApiModelProperty(value = "高速签约模板")
    private String upccHighSignId;

    @ApiModelProperty(value = "低速签约模板")
    private String upccLowerSignId;

    @ApiModelProperty(value = "限速签约模板")
    private String upccLimitsSignId;

    @ApiModelProperty(value = "有效期开始时间")
    private Date poolStartTime;

    @ApiModelProperty(value = "有效期结束时间")
    private Date poolEndTime;


    @AllArgsConstructor
    @Getter
    public enum Status {

        /**
         * 1、应用	2、过期
         */
        IN_APPLY("1"),

        OUT_TIME("2");

        String status;

    }

    @AllArgsConstructor
    @Getter
    public enum IsAlarm {

        /**
         * "是否提醒过（默认：否）	1、是2、否
         */
        HAVE_ALARM("1"),

        NO_ALARM("2");

        String status;
    }

    @AllArgsConstructor
    @Getter
    public enum CurrentControl {
        /**
         * 达量限速
         */
        LIMIT("1"),

        /**
         * 达量停用
         */
        DISABLE("2"),

        /**
         * 正常
         */
        NORMAL("3");

        String status;

    }

    public Long getRechargeOrDefault() {
        return this.recharge == null ? 0 : this.recharge;
    }
}
