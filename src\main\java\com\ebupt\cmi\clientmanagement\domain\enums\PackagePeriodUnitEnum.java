package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 套餐周期类型枚举 流量池周期类型
 * <AUTHOR>
 * @date 2021-5-27 14:45:12
 */
@Getter
@AllArgsConstructor
public enum PackagePeriodUnitEnum {

    /**
     * 24小时
     */
    TWENTY_FOUR_HOURS(1),

    /**
     * 自然日
     */
    NATURAL_DAY(2),

    /**
     * 自然月
     */
    NATURAL_MONTH(3),

    /**
     * 自然年
     */
    NATURAL_YEAR(4);

    private Integer value;

    public boolean matches(Integer value) {
        return this.value.equals(value);
    }

}
