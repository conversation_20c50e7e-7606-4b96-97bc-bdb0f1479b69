package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.*;


/**
 * (CmsPackageDayRemain)实体类
 *
 * <AUTHOR>
 * @since 2024-04-03 14:46:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsPackageDayRemain {

    private Long id;
    /**
     * 剩余流量类型1：套餐单日限量剩余流量2：加油包单日限量剩余流量
     */
    private String flowType;
    /**
     * 套餐唯一id
     */
    private String packageUniqueId;
    /**
     * 单日限量indx值
     */
    private Long dayIndex;
    /**
     * 剩余流量
     */
    private Long remainFlow;
    /**
     * 过期时间
     */
    private String expireTime;

    /**
     * 剩余流量类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum FlowTypeEnum {

        /**
         * 1：套餐单日限量剩余流量
         */
        PACKAGE_REMAIN("1"),

        /**
         * 2：加油包单日限量剩余流量
         */
        ADD_REMAIN("2");

        @Getter
        private String value;

    }
}

