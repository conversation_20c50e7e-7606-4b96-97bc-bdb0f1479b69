package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @description 流量使用详情DTO
 * @date 2021/6/9 10:48
 */
@Data
public class FlowDetailsDTO {

    @ApiModelProperty("套餐名称")
    private String pakcageName;

    @ApiModelProperty(value = "使用日期")
    private String useDate;

    @ApiModelProperty(value = "流量, 单位MB")
    private Float flowCount;

}
