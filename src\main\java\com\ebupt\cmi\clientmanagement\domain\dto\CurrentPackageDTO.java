package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 当前位置套餐DTO
 * @date 2021/5/7 16:50
 */
@Data
@ToString
@ApiModel
public class CurrentPackageDTO {

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "套餐id")
    private String packageId;

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "套餐英文名称")
    private String packageNameEn;

    @ApiModelProperty(value = "套餐激活状态")
    private String packageStatus;

    @ApiModelProperty(value = "激活方式 1自动2手动")
    private String activeType;

    @ApiModelProperty(value = "套餐周期类型1小时2日3月4年")
    private String periodUnit;

    @ApiModelProperty(value = "套餐持续周期")
    private String keepPeriod;

    @ApiModelProperty(value = "套餐过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "子渠道商价格")
    private BigDecimal subPrice;

    @ApiModelProperty(value = "币种156CNY 840USD 344HKD")
    private String currencyCode;

    @ApiModelProperty(value = "套餐唯一id")
    private String packageUniqueId;

}
