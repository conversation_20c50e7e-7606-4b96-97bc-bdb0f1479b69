package com.ebupt.cmi.clientmanagement.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.domain.dto.GetMarketingDetailDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelMarketingRebate;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.GetMarketingDeatilDTO;
import com.ebupt.cmi.clientmanagement.service.CmsChannelMarketingRebateIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/cms_channel_marketing_rebate")
public class CmsChannelMarketingRebateController {

    @Autowired
    private CmsChannelMarketingRebateIService cmsChannelMarketingRebateIService;

    @GetMapping("/getChannelByMarketingId")
    public Response<List<CmsChannelMarketingRebate>> getChannelByMarketingId(@RequestParam Long marketingId){
        return Response.ok(cmsChannelMarketingRebateIService.getChannelByMarketingId(marketingId));
    }

    @PostMapping("/getMarketingDeatil")
    public Response<List<GetMarketingDeatilDTO>> getMarketingDeatil(@RequestBody GetMarketingDetailDTO getMarketingDetailDTO){
        return Response.ok(cmsChannelMarketingRebateIService.getMarketingDeatil(getMarketingDetailDTO));
    }

    @GetMapping("/resetMarketingAmount")
    public Response<Void> resetMarketingAmount(){
        cmsChannelMarketingRebateIService.resetMarketingAmount();
        return Response.ok();
    }

}
