package com.ebupt.cmi.clientmanagement.consumer;

import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelRecordDetail;
import com.ebupt.cmi.clientmanagement.domain.vo.VerifyVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.back.BackFeignClient;
import com.ebupt.cmi.clientmanagement.service.realname.HumanVerifyService;
import com.ebupt.cmi.common.operationlog.entity.SysOperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.ebupt.elk.utils.Utils.objectMapper;

@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues = "realNameAuth.expire.queue" )
public class RealNameAuthConsumer {

    @Resource
    CommonRepository commonRepository;

    @Autowired
    private HumanVerifyService humanVerifyService;

    @Autowired
    private BackFeignClient backFeignClient;

    @Value("${realName-auth.time-min}")
    private Integer min;

    @Value("${realName-auth.time-max}")
    private Integer max;

    @Value("${realName-auth.username}")
    private String username;

    @Value("#{'${realName-auth.ip}'.split(',')}")
    private List<String> ipList;

    @RabbitHandler
    public void process(String messageString, Channel channel, Message message) throws IOException {
        log.info("============================rabbitMQ收到消息{}==================================", messageString);
        if (commonRepository.getMessageById(message.getMessageProperties().getMessageId())) {
            log.debug("该消息已被处理");
            return;
        }
        String authId = JSON.parseObject(messageString, String.class);
        try {
            log.info("收到rabbitMq延迟消息，开始执行实名制认证通过操作");

            humanVerifyService.verify(VerifyVO.builder()
                    .authId(authId)
                    .result("1")
                    .isQueueDeal("1")
                    .build());
            log.info("实名制认证通过延期处理队列流程结束");
            HashMap<String, Object> map = new HashMap<>();
            map.put("id", authId);
            map.put("authStatus", "1");
            try {

                backFeignClient.insertLog(SysOperationLog.builder()
                        .username(username)
                        .operationType(OperationTypeEnum.AUDIT.getValue())
                        .moduleName("实名认证-人工审核接口")
                        .ip(ipList.get((int) (Math.random() * ipList.size())))
                        .time(new Date())
                        .content(objectMapper.writer(SerializationFeature.INDENT_OUTPUT).writeValueAsString(map))
                        .result("成功")
                        .build());
            } catch (JsonProcessingException e) {
                log.warn("自动审批记录操作日志失败");
            }
        } catch (Exception e) {
            log.error("实名制认证通过延期处理队列处理失败", e);
            throw new BizException(e.getMessage());
        } finally {
            commonRepository.deleteMessage(message.getMessageProperties().getMessageId());
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }

    }
}
