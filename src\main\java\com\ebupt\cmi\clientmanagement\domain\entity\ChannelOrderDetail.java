package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 客户订单详情表实体
 * @date 2021/4/19 16:34
 */
@Builder
@TableName("cms_channel_order_detail")
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelOrderDetail extends BaseEntity {


    private Long id;

    private Long orderId;

    private Date orderDate;

    private String orderType;

    /**
     * 卡片形态
     * 1：普通卡（实体卡）
     * 2：Esim卡
     * 3：贴片卡
     */
    private String cardForm;

    /**
     * 卡类型:
     * 1：主卡
     * 2：终端线上
     * 3：终端线下
     */
    private String cardType;

    /**
     * 是否是促销套餐
     * 1：是
     * 2：否
     */
    private String isPromotion;
    private String packageId;

    private String packageName;
    private String nameTw;
    private String nameEn;

    private String iccid;
    private String imsi;
    private String msisdn;
    private String billCode;

    /**
     * 价格，单位分
     */
    private BigDecimal amount;
    private String currencyCode;


    /**
     * 订购渠道
     * 102：API
     * 103：官网（H5）
     * 104:  北京移动
     * 105：批量售卖
     * 106：推广活动
     * 110: 测试渠道
     */
    private String orderChannel;

    private String thirdOrderId;

    /**
     * 订单类型归属
     * 1：个人订单
     * 2：渠道订单
     * 3：绑定订单
     */
    private String orderUserType;

    /**
     * 酬金
     */
    private BigDecimal remunerationAmount;

    /**
     * 酬金类型：
     * 1：直接收入
     * 2：间接收入
     * 3：白卡
     */
    private String remunerationType;

    /**
     * 订单状态
     * 1、待发货
     * 2、完成
     * 3、已退订
     * 4、激活退订待审批
     */
    private String orderStatus;

    /**
     * 退订时的套餐状态
     * 1：待激活
     * 2：已激活
     * 3：已使用
     * 5：已过期
     * 6：激活中
     * 7：待发货
     */
    private String packageStatus;

    private String packageUniqueId;

    private String unsubscribeChannel;

    private Date unsubscribeTime;

    private String orderUserId;

    /**
     * 购买套餐时卡归属的corpId
     */
    private String corpId;

    private Date deliveryTime;

    /**
     * 物流编号
     */
    private String logistic;

    /**
     * 物流公司
     */
    private String logisticCompany;
    private String phoneNumber;

    /**
     * 收件人
     */
    private String addressee;

    /**
     * 地址，竖线分割
     * (国家|省份|城市地址|邮寄地址)
     */
    private String address;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 邮箱
     */
    private String email;
    private Date activeAt;

    /**
     *合作模式
     * 1：代销
     * 2：A2Z
     */
    private String cooperationMode;

    /**
     * 子渠道商订单价格
     * */
    private BigDecimal subAmount;

    /**
     * 子渠道商ID
     * */
    private String subCorpId;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 折扣前金额
     */
    private BigDecimal amountBeforeDiscount;

    public String getName(String country) {
        if ("CN".equals(country)) {
            return this.packageName;
        } else if ("US".equals(country)) {
            return this.nameEn;
        } else {
            return this.nameTw;
        }
    }
}
