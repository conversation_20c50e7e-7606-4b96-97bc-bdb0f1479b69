package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.*;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardBak {
    private Long id;
    private String imsi;
    private String msisdn;
    private String iccid;
    private String pin1;
    private String puk2;
    private String kivalue;
    private String opsno;
    private String opcvalue;
    private String tplid;
    private String type;
    private String cardForm;
    private String esimUrl;
    private String activeType;
    private Long storeId;
    private String isStoreOut;
    private Long realnameId;
    private String status;
    private Date createTime;
    private Date updateTime;

    private Date expireTime;
    private Date outTime;
    private String fileNameAdm;
    private String filePathAdm;
    private String fileNameSdb;
    private String filePathSdb;
    private Long templateId;
    private String hssOpenStatus;
    private String upccOpenStatus;
    private String otaOpenStatus;
    private String upccSignStatus;
    private String upccSignId;
    private String isSingapore;
    private String sendLang;
    private String otaId;
    private String upccSignPackageUniqueId;
    private String himsi;
    /**
     * HSS开户状态
     */
    @Getter
    @AllArgsConstructor
    public enum HssOpenStatusEnum {

        /**
         * 成功
         */
        SUCCESS("1"),

        /**
         * 失败
         */
        FAIL("2");

        private String type;

    }
}