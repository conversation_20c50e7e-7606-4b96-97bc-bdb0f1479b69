package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.Address;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.AddressOptVo;
import com.ebupt.cmi.clientmanagement.domain.vo.AssociatedCard;
import com.ebupt.cmi.clientmanagement.domain.vo.HardCardVO;
import com.ebupt.cmi.clientmanagement.service.AddressService;
import com.ebupt.cmi.clientmanagement.service.HardCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * HardCardController
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>n
 * @Date: 2021/5/12 10:43
 */
@Slf4j
@RestController
@RequestMapping("/hard/card")
@Api(tags = "硬卡维护")
public class HardCardController {
    @Autowired
    private HardCardService hardCardService;

    @PostMapping("/opt")
    @ApiOperation(value = "硬卡维护接口", notes = "硬卡维护接口")
    public Response<List<AssociatedCard>> relationshipManage(@RequestBody HardCardVO hardCardVO){
        log.info("请求参数：{}",hardCardVO.toString());
        return hardCardService.relationshipManage(hardCardVO);
    }
}
