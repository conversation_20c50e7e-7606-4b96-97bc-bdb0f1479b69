package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/10 10:49
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PackageInfoDTO {
    /**
     * ICCID
     */
    private String iccid;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 激活时间
     */
    private String activeTime;

    /**
     * 到期时间
     */
    private String expireTime;

    /**
     * 货币种类
     */
    private String currencyCode;

    /**
     * 价格
     */
    private BigDecimal price;
}
