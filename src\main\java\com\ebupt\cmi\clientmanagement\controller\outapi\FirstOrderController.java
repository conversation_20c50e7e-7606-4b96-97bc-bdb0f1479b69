package com.ebupt.cmi.clientmanagement.controller.outapi;

import com.ebupt.cmi.clientmanagement.consumer.luOrder.LuOrderCancelConsumer;
import com.ebupt.cmi.clientmanagement.domain.req.BuyPackageVO;
import com.ebupt.cmi.clientmanagement.domain.req.CancelOrderVO;
import com.ebupt.cmi.clientmanagement.domain.req.CardAndPackageVO;
import com.ebupt.cmi.clientmanagement.domain.response.OutResponse;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.FirstOrderService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class FirstOrderController {


    private final FirstOrderService firstOrderService;

    private final LuOrderCancelConsumer luOrderCancelConsumer;

    @PostMapping("/api/v1/outOrder/buyCardAndPackage")
    @ApiOperation(value = "用户购卡接口")
    public OutResponse buyCardAndPackage(@RequestBody CardAndPackageVO cardAndPackageVO) {
        return firstOrderService.buyCardAndPackage(cardAndPackageVO);
    }


    @PostMapping("/api/v1/outOrder/cancelOrder")
    @ApiOperation(value = "取消订单接口")
    public OutResponse cancelOrder(@RequestBody CancelOrderVO cancelOrderVO) {
        return firstOrderService.cancelOrder(cancelOrderVO);
    }

    @PostMapping("/api/v1/outOrder/changeOrder")
    @ApiOperation(value = "套餐订购/变更接口")
    public OutResponse buyPackage(@RequestBody BuyPackageVO buyPackageVO) {
        return firstOrderService.buyPackage(buyPackageVO);
    }

    @PostMapping("/api/v1/outOrder/consumer")
    public Response<Void> apiAsyncNotifyUse(@RequestParam String orderUniqueId, @RequestParam String asyncNotifyType) {
        luOrderCancelConsumer.cancelOrder2(orderUniqueId, asyncNotifyType);
        return Response.ok();
    }

}
