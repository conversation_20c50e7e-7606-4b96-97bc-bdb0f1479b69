package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 15:13
 */

@AllArgsConstructor
@Getter
public enum UserQueryPackageStatusEnum {
    /**
     * 套餐状态，不传默认查询全部状态
     */
     /**
      *  1:使用中
      */
     USING("1"),

    /**
     *  2:已使用
     */
    USED("2"),
    /**
     * 3:未使用
     */
    UNUSED("3"),

    /**
     * 4:已过期
     */
    OVERDUE("4");

    String packageStatus;
}
