package com.ebupt.cmi.clientmanagement.domain.dto.lu;

import com.ebupt.cmi.clientmanagement.feign.pms.domain.PreActivePackageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/1 14:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PackageSurfingTypeDTO {

    private String packageId;

    private String cardPoolId;

    private boolean supportH;

    private String supplierId;

    private PreActivePackageDTO preActivePackageDTO;
}
