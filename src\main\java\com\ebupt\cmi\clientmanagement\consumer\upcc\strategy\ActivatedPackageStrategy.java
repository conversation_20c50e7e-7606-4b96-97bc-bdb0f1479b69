package com.ebupt.cmi.clientmanagement.consumer.upcc.strategy;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.RetryContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.upcc.context.UpccSignContext;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ActivatedPackageStrategy extends AbstractStrategy {


    @Override
    public void doBiz(UpccSignContext context) {
        coreNetCaller.upccSignature(context.getMsisdn(), context.getNewUpccSignBizId(), context.getOldUpccSignBizId());
    }

    @Override
    public void afterFail(RetryContext retryContext) {
        PackageDirectionRelation packageDirectionRelation = retryContext.getPackageDirectionRelation();
        if (packageDirectionRelation.getHasUsed().equals(PackageDirectionRelation.Status.USING.getValue())) {
            log.debug("签约失败且应用处于使用中，更改应用状态待使用");
            packageDirectionRelation.setHasUsed(PackageDirectionRelation.Status.NOT_USED.getValue());
            packageDirectionRelationMapper.updateById(packageDirectionRelation);
        }
    }

    @Override
    public void afterSuccess(RetryContext context) {
        PackageDirectionRelation packageDirectionRelation = context.getPackageDirectionRelation();
        if (packageDirectionRelation.getHasUsed().equals("1")) {
            log.debug("签约成功且应用处于待使用，更改应用状态使用中");
            packageDirectionRelation.setHasUsed(PackageDirectionRelation.Status.USING.getValue());
            packageDirectionRelationMapper.updateById(packageDirectionRelation);
        }
    }

    @Override
    public void afterSuccess(UpccSignContext upccSignContext) {
        if (upccSignContext.getNewUpccSignBizId() != null) {
            upccSignContext.getCmsCardUpccRecord().setUpdateTime(new Date());
            cmsCardUpccRecordService.saveOrUpdate(upccSignContext.getCmsCardUpccRecord());
        } else {
            cmsCardUpccRecordMapper.deleteById(upccSignContext.getCmsCardUpccRecord());
        }

    }

    @Override
    public <T extends UpccConsumerContext> void handle(T context) {
        UpccConsumerContext upccConsumerContext = context;
        String packageUniqueId = upccConsumerContext.getPackageUniqueId();

        PackageDirectionRelation packageDirectionRelation = upccConsumerContext.getPackageDirectionRelation();
        Long appId = upccConsumerContext.getAppId();

        RetryContext retryContext = new RetryContext();
        ArrayList<UpccSignContext> upccSignContexts = new ArrayList<>();
        retryContext.setPackageDirectionRelation(packageDirectionRelation);


        String oldBizId = null;
        String upccSignIdToV;
        String upccSignIdToH;


        BigDecimal cycleRemainFlow = flowOperationUtils.getCycleRemainFlow(packageUniqueId, CmsPackageCycleRemain.FlowTypeEnum.APP_REMAIN.getValue(), packageDirectionRelation.getAppGroupId());

        long usedFlow = packageDirectionRelation.getFlowLimitSum() - cycleRemainFlow.longValue();

        ChannelPackageCard channelPackageCard = hvShareRepository.getChannelPackageCardByPackageUniqueId(packageUniqueId);

        String packageSurfStatus = channelPackageCard.getSurfStatus();

        if (usedFlow >= packageDirectionRelation.getFlowLimitSum() && packageSurfStatus.equals("2") && packageDirectionRelation.getIsUsePackage().equals(PackageDirectionRelation.IsUsePackage.YES.getValue())) {
            log.debug("此定向应用已用完 && 套餐状态限速 && 继续使用通用流量，删除签约");
            if (upccConsumerContext.getVimsi() != null) {

                CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getAppId, appId)
                        .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getVimsi()));
                if (cmsCardUpccRecord != null) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToV());
                    upccSignContext.setNewUpccSignBizId(null);
                    upccSignContext.setOldUpccSignBizId(cmsCardUpccRecord.getUpccSignBizId());
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecord);
                    log.debug("V卡签约模板对不上，需要删除签约，{}", upccSignContext);
                    upccSignContexts.add(upccSignContext);
                }

                cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getAppId, appId)
                        .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getHImsi()));
                if (cmsCardUpccRecord != null) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToH());
                    upccSignContext.setNewUpccSignBizId(null);
                    upccSignContext.setOldUpccSignBizId(cmsCardUpccRecord.getUpccSignBizId());
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecord);
                    log.debug("H卡签约模板对不上，需要删除签约，{}", upccSignContext);
                    upccSignContexts.add(upccSignContext);
                }
                retryContext.setUpccSignContext(upccSignContexts);
            } else {

                CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getAppId, appId)
                        .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getHImsi()));
                if (cmsCardUpccRecord != null) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToH());
                    upccSignContext.setNewUpccSignBizId(null);
                    upccSignContext.setOldUpccSignBizId(cmsCardUpccRecord.getUpccSignBizId());
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecord);
                    log.debug("H卡签约模板对不上，需要删除签约，{}", upccSignContext);
                    upccSignContexts.add(upccSignContext);
                }
                retryContext.setUpccSignContext(upccSignContexts);
            }
        } else {
            CmsCardUpccRecord cmsCardUpccRecordToSave = new CmsCardUpccRecord();

            log.debug("此定向应用没用完 || 套餐状态正常 || 不继续使用通用流量，签约指定档位模板");
            CmsPackageCardUpccRelation cmsPackageCardUpccRelation = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                    .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                    .ge(CmsPackageCardUpccRelation::getConsumption, usedFlow)
                    .eq(CmsPackageCardUpccRelation::getAppId, appId)
                    .orderByAsc(CmsPackageCardUpccRelation::getConsumption));

            if (upccConsumerContext.getVimsi() != null) {

                upccSignIdToV = cmsPackageCardUpccRelation.getUpccSignId();

                CmsPackageCardUpccRelation cmsPackageCardUpccRelationToH = cmsPackageCardUpccRelationMapper.selectOne(Wrappers.lambdaQuery(CmsPackageCardUpccRelation.class)
                        .eq(CmsPackageCardUpccRelation::getAppId, appId)
                        .eq(CmsPackageCardUpccRelation::getPackageUniqueId, packageUniqueId)
                        .orderByAsc(CmsPackageCardUpccRelation::getConsumption));

                if (cmsPackageCardUpccRelationToH.getUpccSpeed() > cmsPackageCardUpccRelation.getUpccSpeed()) {
                    upccSignIdToH = upccSignIdToV;
                } else {
                    upccSignIdToH = cmsPackageCardUpccRelationToH.getUpccSignId();
                }

                CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getAppId, appId)
                        .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getVimsi()));
                if (cmsCardUpccRecord != null) {
                    cmsCardUpccRecordToSave.setId(cmsCardUpccRecord.getId());
                    oldBizId = cmsCardUpccRecord.getUpccSignBizId();
                }


                if (!upccSignIdToV.equals(oldBizId)) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToV());
                    upccSignContext.setNewUpccSignBizId(upccSignIdToV);
                    upccSignContext.setOldUpccSignBizId(oldBizId);
                    cmsCardUpccRecordToSave.setAppId(upccConsumerContext.getAppId());
                    cmsCardUpccRecordToSave.setImsi(upccConsumerContext.getVimsi());
                    cmsCardUpccRecordToSave.setUpccSignBizId(upccSignIdToV);
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecordToSave);
                    upccSignContexts.add(upccSignContext);
                    log.debug("v卡签约模板对不上，需要签约，{}", upccSignContext);
                }

                cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getAppId, appId)
                        .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getHImsi()));
                oldBizId = null;
                cmsCardUpccRecordToSave = new CmsCardUpccRecord();
                if (cmsCardUpccRecord != null) {
                    cmsCardUpccRecordToSave.setId(cmsCardUpccRecord.getId());
                    oldBizId = cmsCardUpccRecord.getUpccSignBizId();
                }
                if (!upccSignIdToH.equals(oldBizId)) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToH());
                    upccSignContext.setNewUpccSignBizId(upccSignIdToH);
                    upccSignContext.setOldUpccSignBizId(oldBizId);
                    cmsCardUpccRecordToSave.setAppId(upccConsumerContext.getAppId());
                    cmsCardUpccRecordToSave.setImsi(upccConsumerContext.getHImsi());
                    cmsCardUpccRecordToSave.setUpccSignBizId(upccSignIdToH);
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecordToSave);
                    log.debug("H卡签约模板对不上，需要签约，{}", upccSignContext);
                    upccSignContexts.add(upccSignContext);
                }
                retryContext.setUpccSignContext(upccSignContexts);


            } else {

                upccSignIdToH = cmsPackageCardUpccRelation.getUpccSignId();

                CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getAppId, appId)
                        .eq(CmsCardUpccRecord::getImsi, upccConsumerContext.getHImsi()));
                if (cmsCardUpccRecord != null) {
                    cmsCardUpccRecordToSave.setId(cmsCardUpccRecord.getId());
                    oldBizId = cmsCardUpccRecord.getUpccSignBizId();
                }
                if (!upccSignIdToH.equals(oldBizId)) {
                    UpccSignContext upccSignContext = new UpccSignContext();
                    upccSignContext.setMsisdn(upccConsumerContext.getMsisdnToH());
                    upccSignContext.setNewUpccSignBizId(upccSignIdToH);
                    upccSignContext.setOldUpccSignBizId(oldBizId);
                    cmsCardUpccRecordToSave.setAppId(upccConsumerContext.getAppId());
                    cmsCardUpccRecordToSave.setImsi(upccConsumerContext.getHImsi());
                    cmsCardUpccRecordToSave.setUpccSignBizId(upccSignIdToH);
                    upccSignContext.setCmsCardUpccRecord(cmsCardUpccRecordToSave);
                    upccSignContexts.add(upccSignContext);
                    log.debug("签约模板对不上，需要签约，{}", upccSignContext);
                }
                retryContext.setUpccSignContext(upccSignContexts);
            }
        }
        upccConsumerContext.setRetryContext(retryContext);
    }
}
