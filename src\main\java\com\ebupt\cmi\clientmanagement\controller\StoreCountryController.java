package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.entity.StoreCountryRelation;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.InsertWarehouseVO;
import com.ebupt.cmi.clientmanagement.service.StoreCountryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/storeCountry")
@Api(tags = "库存相关接口")
public class StoreCountryController {
    @Autowired
    private StoreCountryService storeCountryService;

    @PostMapping("/insertWarehouse")
    @ApiOperation(value = "新建库存")
    public Response insertWarehouse(@RequestBody InsertWarehouseVO insertWarehouseVO) {
        storeCountryService.insertWarehouse(insertWarehouseVO);
        return Response.ok();
    }

    @DeleteMapping("/deleteWarehouse")
    @ApiOperation(value = "删除库存")
    public Response deleteWarehouse(@RequestParam Long storeId) {
        storeCountryService.deleteWarehouse(storeId);
        return Response.ok();
    }

    @PostMapping("/getWarehouse")
    @ApiOperation(value = "查询库存")
    public Response<List<StoreCountryRelation>> getWarehouse(@RequestBody List<Long> storeIdList) {

        return Response.ok(storeCountryService.getWarehouse(storeIdList));
    }

}
