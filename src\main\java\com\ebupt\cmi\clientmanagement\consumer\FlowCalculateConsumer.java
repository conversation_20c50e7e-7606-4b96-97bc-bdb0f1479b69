package com.ebupt.cmi.clientmanagement.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsFlowpoolInfoCycle;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsFlowpoolRemain;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.job.context.FlowPoolResetContext;
import com.ebupt.cmi.clientmanagement.job.wrapper.RedisWarrper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsFlowpoolInfoCycleMapper;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.ebupt.cmi.clientmanagement.utils.FlowOperationUtils;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @date 2022/1/21 11:09
 */

@RabbitListener(queues = "flowPool.FlowCalculateQueue")
@Component
@Slf4j
@AllArgsConstructor
public class FlowCalculateConsumer {

    private final RedisWarrper redisWarrper;
    private final ChannelCardMapper channelCardMapper;
    private final CmsFlowpoolInfoCycleMapper cmsFlowpoolInfoCycleMapper;
    private final RedisUtil<Long> redisUtil;
    private final FlowOperationUtils flowOperationUtils;

    private final CommonRepository commonRepository;

    @RabbitHandler
    public void process(String flowPoolRabbitMQMessageString, Channel channel, Message message) throws IOException {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        if (commonRepository.getMessageById(message.getMessageProperties().getMessageId())) {
            log.debug("该消息已被处理");
            return;
        }
        CmsFlowpoolInfoCycle flowpoolInfoCycleOld = JSON.parseObject(flowPoolRabbitMQMessageString, CmsFlowpoolInfoCycle.class);
        //扣流量，写表
        calculateAndWrite(flowpoolInfoCycleOld);
        commonRepository.deleteMessage(message.getMessageProperties().getMessageId());
    }

    private void calculateAndWrite(CmsFlowpoolInfoCycle e) {
        //总量
        Long flowSum = e.getFlowSum();
//        Long currentFlow = redisWarrper.getFlowPoolRemain(e.getFlowPoolId(), e.getFlowPoolUniqueId());
        // flow_pool_{flowPoolId}_{flowUniqeID}  cms_flowpool_remain 3
        Long currentFlow = flowOperationUtils.getFlowPoolRemainFlow(e.getFlowPoolUniqueId(), e.getFlowPoolId(), null, "3");
        if (currentFlow == null) {
            throw new BizException("redis中不存在该流量池的数据，请检查！");
        }

        Long useNum = flowSum + Optional.ofNullable(e.getRecharge()).orElse(0L) - currentFlow;
        BigDecimal extraPrice = BigDecimal.ZERO;
        //超量流量
        Long extraFlow = useNum - flowSum;
        if (extraFlow > 0) {
            log.info("该周期流量池{}超量{}byte", e.getFlowPoolId(), extraFlow);
            BigDecimal extraFlowGB = new BigDecimal(extraFlow / 1024.0 / 1024.0 / 1024.0);
            extraPrice = extraFlowGB.multiply(e.getFlowPoolExtraPrice());
            log.info("计算超量流量总金额为{}", extraPrice);
        }

        //更新原数据
        CmsFlowpoolInfoCycle newCmsFlowInfoCycle = CmsFlowpoolInfoCycle
                .builder()
                .id(e.getId())
                .status(CmsFlowpoolInfoCycle.Status.OUT_TIME.getStatus())
                .useNum(useNum)
                .extraPrice(BigDecimal.valueOf(extraPrice.longValue()))
                .build();
        cmsFlowpoolInfoCycleMapper.updateById(newCmsFlowInfoCycle);
        delKey(e.getFlowPoolId(), e.getFlowPoolUniqueId());
    }

    private void delKey(String flowPoolId, String flowPoolUniqueId) {
        log.debug("删除流量池flowPoolId: {}, flowPoolUniqueId: {}相关redis key", flowPoolId, flowPoolUniqueId);
        final List<ChannelCard> channelCards = channelCardMapper.selectList(Wrappers.lambdaQuery(ChannelCard.class)
                .select(ChannelCard::getImsi)
                .eq(ChannelCard::getFlowPoolId, flowPoolId));
//        final String flowPoolRemainKey = String.format(BizConstants.FLOW_POOL_REMAIN_KEY, flowPoolId, flowPoolUniqueId);
        if (CollectionUtils.isEmpty(channelCards)) {
//            redisUtil.del(flowPoolRemainKey);
            flowOperationUtils.delFlowpoolRemain(CmsFlowpoolRemain.builder()
                    .flowType("3")
                    .flowUniqueId(flowPoolUniqueId)
                    .flowPoolId(flowPoolId)
                    .build());
            return;
        }
//        List<String> keys = new ArrayList<>(2 * channelCards.size() + 1);
//        keys.add(flowPoolRemainKey);
        channelCards.stream().map(ChannelCard::getImsi).forEach(imsi -> {
            flowOperationUtils.delFlowpoolRemain(CmsFlowpoolRemain.builder()
                    .flowType("1")
                    .flowUniqueId(flowPoolUniqueId)
                    .flowPoolId(flowPoolId)
                    .imsi(imsi)
                    .build());
            flowOperationUtils.delFlowpoolRemain(CmsFlowpoolRemain.builder()
                    .flowType("2")
                    .flowUniqueId(flowPoolUniqueId)
                    .flowPoolId(flowPoolId)
                    .imsi(imsi)
                    .build());
//            keys.add(String.format(BizConstants.FLOW_POOL_CARD_REMAIN_KEY, flowPoolId, flowPoolUniqueId, imsi));
//            keys.add(String.format(BizConstants.FLOW_POOL_CYCLE_REMAIN_KEY, flowPoolId, flowPoolUniqueId, imsi));
        });
//        redisUtil.batchDel(keys);
    }

}
