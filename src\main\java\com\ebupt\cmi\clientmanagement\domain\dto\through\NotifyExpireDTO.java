package com.ebupt.cmi.clientmanagement.domain.dto.through;

import com.ebupt.cmi.clientmanagement.domain.vo.through.NotifyExpireVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NotifyExpireDTO.java
 * @Description
 * @createTime 2021年05月18日 11:24:00
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotifyExpireDTO {

    String imsi;

    String iccid;

    String packageID;

    String activeTime;

    String expireTime;

    public NotifyExpireDTO(NotifyExpireVO notifyExpireVO) {
        this.imsi = notifyExpireVO.getImsi();
        this.iccid = notifyExpireVO.getIccid();
        this.packageID = notifyExpireVO.getPackageID();
        this.activeTime = notifyExpireVO.getActiveTime();
        this.expireTime = notifyExpireVO.getExpireTime();
    }
}
