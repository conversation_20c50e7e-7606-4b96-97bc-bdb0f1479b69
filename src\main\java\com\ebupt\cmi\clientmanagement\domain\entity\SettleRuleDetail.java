package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/4/26 20:49
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_settle_rule_detail")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class SettleRuleDetail extends BaseEntity {

    private Long id;
    private String corpId;

    private String settleRule;

    private String packageId;

    private String packageName;

    private String nameTw;

    private String nameEn;

    private String mcc;

    @TableField(exist = false)
    private List<String> mccList;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private String countryName;

    private BigDecimal price;

    private String currencyCode;
}
