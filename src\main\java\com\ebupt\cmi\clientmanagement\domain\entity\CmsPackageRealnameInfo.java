package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 套餐免实名记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsPackageRealnameInfo对象", description="套餐免实名记录表")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsPackageRealnameInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "套餐唯一id")
    private String packageUniqueId;

    @ApiModelProperty(value = "MCC")
    private String mcc;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}
