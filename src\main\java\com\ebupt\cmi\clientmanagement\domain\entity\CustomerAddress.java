package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * CustomerAddress
 *
 * @Author: z<PERSON>qiankun
 * @Date: 2021/5/10 19:41
 */
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cms_customer_address")
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAddress extends  BaseEntity{

    private Long id;

    private String userId;

    private String address;
}
