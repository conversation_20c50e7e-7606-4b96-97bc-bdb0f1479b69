package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 15:44
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserQueryPackageReDTO {
    private List<UserQueryPackageDTO> userQueryPackageDTO;
    private long count;
}
