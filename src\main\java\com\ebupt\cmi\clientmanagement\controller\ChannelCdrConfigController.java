package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.CmsChannelCdrConfigDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.CmsChannelCdrConfigPageDTO;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.CmsChannelCdrConfigVO;
import com.ebupt.cmi.clientmanagement.service.ChannelCdrConfigService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/gtpConfig")
@AllArgsConstructor
public class ChannelCdrConfigController {
    @Autowired
    private ChannelCdrConfigService channelCdrConfigService;

    /**
     * 获取企业名称列表
     *
     * @return 包含企业名称的响应对象
     */
    @GetMapping("/corpList")
    public Response<List<Map<String, String>>> corpNameList() {
        return Response.ok(channelCdrConfigService.corpNameList());
    }

    /**
     * 添加渠道话单配置信息
     *
     * @param cmsChannelCdrConfigDTOS 包含要添加的渠道话单配置信息的DTO列表
     * @return 成功响应对象
     */
    @PostMapping("/add")
    @OperationLog(operationName = "添加渠道话单配置信息", operationType = OperationTypeEnum.ADD)
    public Response<Void> add(@RequestBody List<CmsChannelCdrConfigDTO> cmsChannelCdrConfigDTOS) {
        channelCdrConfigService.add(cmsChannelCdrConfigDTOS);
        return Response.ok();
    }

    /**
     * 更新渠道话单配置信息
     *
     * @param cmsChannelCdrConfigDTO 包含要更新的渠道话单配置信息的DTO对象
     * @return 成功响应对象
     */
    @PostMapping("/update")
    @OperationLog(operationName = "编辑渠道话单配置信息", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> update(@RequestBody CmsChannelCdrConfigDTO cmsChannelCdrConfigDTO) {
        channelCdrConfigService.update(cmsChannelCdrConfigDTO);
        return Response.ok();
    }

    /**
     * 删除指定ID的渠道话单配置信息
     *
     * @param id 要删除的渠道话单配置信息的ID
     * @return 成功响应对象
     */
    @PostMapping("/delete")
    @OperationLog(operationName = "删除渠道话单配置信息", operationType = OperationTypeEnum.DELETE)
    public Response<Void> delete(@RequestParam("id") String id) {
        channelCdrConfigService.delete(id);
        return Response.ok();
    }

    /**
     * 分页查询渠道话单配置信息
     *
     * @param cmsChannelCdrConfigPageDTO 包含分页查询条件的DTO对象
     * @return 包含分页查询结果的响应对象
     */
    @PostMapping("/page")
    public Response<List<CmsChannelCdrConfigVO>> page(@RequestBody CmsChannelCdrConfigPageDTO cmsChannelCdrConfigPageDTO) {
        return Response.ok(channelCdrConfigService.page(cmsChannelCdrConfigPageDTO));
    }

    /**
     * 删除指定日期之前的旧文件
     *
     * @param day 日期字符串，表示删除该日期之前的文件
     * @return 成功响应对象
     */
    @PostMapping("/clear")
    public Response<Void> deleteRddFiles(@RequestParam(required = false,name = "day") String day) {
        channelCdrConfigService.deleteOldFiles(day);
        return Response.ok();
    }
}
