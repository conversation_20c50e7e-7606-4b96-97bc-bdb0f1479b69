package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * cms_channel_sms_template_relation
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsChannelSmsTemplateRelation implements Serializable {
    private Long id;

    private Long templateId;

    private String templateName;

    private String corpId;

    private Date createTime;

    private String cooperationMode;

    private static final long serialVersionUID = 1L;
}