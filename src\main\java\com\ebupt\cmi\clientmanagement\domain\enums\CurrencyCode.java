package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 币种编码
 * @date 2021/5/7 11:18
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum CurrencyCode {
    /**
     * 156: 人民币
     */
    RMB("156", "人民币"),
    /**
     * 840: 美元
     */
    USD("840", "美元"),
    /**
     * 344: 港币
     */
    HKD("344", "港币");

    private final String code;

    private final String name;

    public static String getCode(String name) {
        for (CurrencyCode p : CurrencyCode.values()) {
            if (p.getName().equals(name)) {
                return p.getCode();
            }
        }
        log.warn("输入不符合要求：{}", name);
        return "";
    }

    public static String getName(String code) {
        for (CurrencyCode p : CurrencyCode.values()) {
            if (p.getCode().equals(code)) {
                return p.getName();
            }
        }
        log.warn("输入不符合要求：{}", code);
        return "";
    }
}
