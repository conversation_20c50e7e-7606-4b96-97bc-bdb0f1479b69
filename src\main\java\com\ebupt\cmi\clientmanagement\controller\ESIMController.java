package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.EsimDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.EsimcardStatsDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.ESIMSearchVO;
import com.ebupt.cmi.clientmanagement.domain.vo.EsimStatusNotifyVO;
import com.ebupt.cmi.clientmanagement.domain.vo.ExportVO;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.clientmanagement.service.ESIMService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/16 11:11
 */

@Api("esim信息接口")
@RestController
@AllArgsConstructor
@RequestMapping("/esim")
public class ESIMController {

    @Autowired
    private ESIMService esimService;

    @Autowired
    private ChannelService channelService;

    @GetMapping("/getEsimInfo")
    @ApiOperation(value = "esim信息查询接口")
    public Response<EsimDTO.ProfileInfo> getEsimInfo(@RequestParam("iccid") String iccid) {
        return Response.ok(esimService.getEsimInfo(iccid));
    }

    @GetMapping("/getQrCode")
    @ApiOperation(value = "获取二维码")
    public void getQrCode(@RequestParam String iccid, HttpServletResponse response) {
        esimService.getQrCode(iccid, response);
    }

    @GetMapping("/uploadQrCode")
    @ApiOperation(value = "ESIM信息二维码下载", notes = "ESIM信息二维码下载")
    public void uploadQrCode(@RequestParam String iccid, @RequestParam String corpId,
                             HttpServletResponse response) {
        esimService.uploadQrCode(iccid, corpId, response);
    }

    @PostMapping("/getList")
    @ApiOperation(value = "ESIM信息分页查询", notes = "ESIM信息分页查询")
    public Response<List<ChannelCard>> getCardList(@RequestBody @Validated ESIMSearchVO vo) {
        vo.setCorpId(channelService.getSubChannelCorpId(vo.getCorpId(), vo.getCooperationMode()));
        return esimService.getCardList(vo);
    }

    /**
     * ESIM信息文件导出
     *
     * @param vo 请求体
     * @return 任务信息
     */
    @PostMapping("/infoExport")
    @ApiOperation(value = "ESIM信息文件导出", notes = "ESIM信息文件导出")
    public Response<ExportVO> ESIMInfoExport(@RequestBody @Validated ESIMSearchVO vo) {
        return Response.ok(esimService.ESIMInfoExport(vo));
    }

    /**
     * ESIM二维码导出
     *
     * @param vo 请求体
     * @return 任务信息
     */
    @PostMapping("/qrCodeExport")
    @ApiModelProperty(value = "ESIM二维码导出", notes = "ESIM二维码导出")
    public Response<ExportVO> ESIMQRCodeExport(@RequestBody @Validated ESIMSearchVO vo) {
        return Response.ok(esimService.ESIMQRCodeExport(vo));
    }

    @PostMapping("/qrCodeGenerate")
    @ApiModelProperty(value = "二维码统一生成", notes = "二维码统一生成")
    public void qrCodeGenerate() {
        esimService.qrCodeGenerate();
    }

    @PostMapping("/SBO_esim_notify/v1")
    public Response esimNotify(@RequestBody EsimStatusNotifyVO notify) {
        esimService.esimStatusNotify(notify);
        return Response.ok();
    }

    @GetMapping("/getEsimcardStats")
    public Response<List<EsimcardStatsDTO>> getEsimCardStats(@RequestParam String beginDate, @RequestParam String endDate,
                                                             @RequestParam Integer pageNum, @RequestParam Integer pageSize) {
        return Response.ok(esimService.getEsimCardStats(beginDate, endDate, pageNum, pageSize));
    }

    @GetMapping("/exportEsimcardStats")
    public Response<ExportVO> exportEsimcardStats(@RequestParam String beginDate, @RequestParam String endDate
            , @RequestParam String corpId) {
        return Response.ok(esimService.exportEsimcardStats(beginDate, endDate, corpId));
    }
}
