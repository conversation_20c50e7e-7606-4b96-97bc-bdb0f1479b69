package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.PackageOverdueService;
import com.ebupt.cmi.clientmanagement.service.PersonalOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Slf4j
@AllArgsConstructor
@RestController
@Api(tags = "套餐过期处理相关接口")
@RequestMapping("packageOverdue")
public class PackageOverdueController {

    private final PackageOverdueService packageOverdueService;

    private final PersonalOrderService personalOrderService;
    @PostMapping
    @ApiOperation("套餐过期处理")
    public Response handleOverduePackage() {
        packageOverdueService.handleInactive();
        packageOverdueService.handleActivating();
        packageOverdueService.handleActivated();
        return Response.ok();
    }


    @PostMapping("/preOverDue")
    @ApiOperation("已激活套餐过期前置处理")
    public Response<Void> handlePreActivated() {
        packageOverdueService.handlePreActivated();
        return Response.ok();
    }
}
