package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ChannelDistributorDto {

    //渠道商模式
    private String channelType;
    //币种
    private String currencyCode;
    //押金账户
    private BigDecimal deposit;
    //信用账户
    private BigDecimal creditAmount;
    //营销账户
    private BigDecimal marketingAmount;
    //总额度
    private BigDecimal totalAmount;
    //可用额度
    private BigDecimal balance;
    //已用额度
    private BigDecimal usedAmount;

}
