package com.ebupt.cmi.clientmanagement.domain.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefuelInputVO {
    @ApiModelProperty(value = "套餐ID")
    @NotEmpty(message = "packageID不能为空")
    private String packageID;

    @ApiModelProperty(value = "渠道商合作模式")
    private String cooperationMode;

    @ApiModelProperty(value = "加油包名称")
    private String refuelName;

    @ApiModelProperty(value = "套餐组id")
    private List<String> groupID;

    @ApiModelProperty(value = "加油包ID")
    private String refuelID;

    @ApiModelProperty(value = "pageNum")
    private Integer pageNum;

    @ApiModelProperty(value = "pageSize")
    private Integer pageSize;
}
