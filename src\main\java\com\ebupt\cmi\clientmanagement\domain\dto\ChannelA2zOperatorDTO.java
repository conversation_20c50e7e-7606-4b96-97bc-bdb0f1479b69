package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelA2zOperator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelA2zOperatorDTO extends CmsChannelA2zOperator {
    private Country country;

    private String a2zOperatorChargeType;

    private List<Operator> operators;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Country {
        String mcc;
        String name;
    }

    @Data
    public static class Operator {
        @JsonProperty("operatorName")
        String name;
        Long id;
    }
}
