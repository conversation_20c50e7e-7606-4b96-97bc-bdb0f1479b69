package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 套餐是否是促销套餐枚举
 * <AUTHOR>
 * @date 2021-5-28 18:54:55
 */
@Getter
@AllArgsConstructor
public enum PackageIsPromotionEnum {

    /**
     * 是
     */
    YES("1"),

    /**
     * 否
     */
    NO("2");

    private String value;

    public boolean matches(String value) {
        return this.value.equals(value);
    }


}
