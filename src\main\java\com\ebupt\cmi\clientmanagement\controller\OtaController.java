package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.InvokeVO;
import com.ebupt.cmi.clientmanagement.service.impl.OtaOverTimeService;
import com.ebupt.elk.annotion.NormalLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OtaController.java
 * @Description 给OTA下发专用Controller
 * @createTime 2021年06月04日 19:40:00
 */

@RestController
@RequestMapping("/ota")
@Slf4j
public class OtaController {
    @Autowired
    OtaOverTimeService otaOverTimeService;

    @PostMapping("/overTime")
    @NormalLog
    public Response<ChannelPackageCard> otaInvokeOvertime(@RequestBody InvokeVO invokeVO) {

        return Response.ok(otaOverTimeService.excuteOtaOverTimeLogical(invokeVO));

    }
}
