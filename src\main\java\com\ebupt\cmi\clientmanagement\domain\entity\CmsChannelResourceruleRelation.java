package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelResourceruleRelation implements Serializable {

    private Long id;

    private String corpId;

    private Long ruleId;

    private Date createTime;

    private static final long serialVersionUID = 1L;
}