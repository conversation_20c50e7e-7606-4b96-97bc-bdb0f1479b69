package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.CardFlowstatDay;
import com.ebupt.cmi.clientmanagement.domain.dto.CcrMsg;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelRecordDetail;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.PgwRealTimeCdrVO;
import com.ebupt.cmi.clientmanagement.service.ChannelRecordDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * ChannelRecordDetailController
 * 客户话单详情相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/recordDetail")
@Api(tags = "客户话单详情相关接口")
public class ChannelRecordDetailController {

    @Autowired
    private ChannelRecordDetailService channelRecordDetailService;

    /**
     * 根据IMSI查询最新的plmnlist
     *
     * @param imsi
     * @return
     */
    @PostMapping("/getNewPlmnlist")
    @ApiOperation(value = "查询客户话单详情最新Plmnlist", notes = "查询客户话单详情最新Plmnlist")
    public Response<String> getNewPlmnlist(@RequestParam String imsi) {
        ChannelRecordDetail channelRecordDetail = channelRecordDetailService.getNewPlmnlist(imsi);
        return Response.ok(channelRecordDetail != null ? channelRecordDetail.getPlmnlist() : null);
    }

    /**
     * 根据IMSI查询最新的话单信息
     *
     * @param imsi
     * @return
     */
    @PostMapping("/getRecordDetail")
    @ApiOperation(value = "查询客户话单详情最新话单信息", notes = "查询客户话单详情最新话单信息")
    public Response<ChannelRecordDetail> getRecordDetail(@RequestParam String imsi) {
        return Response.ok(channelRecordDetailService.getNewPlmnlist(imsi));
    }

    /**
     * 话单数据解析入库
     *
     * @param channelRecordDetails
     * @return
     */
    @PostMapping("/saveRecordDetails")
    @ApiOperation(value = "话单数据解析入库", notes = "话单数据解析入库")
    public void saveRecordDetails(@RequestBody List<ChannelRecordDetail> channelRecordDetails) {
        channelRecordDetailService.saveRecordDetails(channelRecordDetails);
    }

    @PostMapping("/selectRecordDetails")
    @ApiOperation(value = "话单数据获取", notes = "话单数据获取")
    public Response<List<CardFlowstatDay>> selectRecordDetails(@RequestParam String date ,
                                                               @RequestParam(required = false) String packageUniqueId,
                                                               @RequestParam(required = false) String imsi) {
        return Response.ok(channelRecordDetailService.selectRecordDetails(date,packageUniqueId,imsi));
    }

    @PostMapping("/getCurrentDayRecordDetail")
    public Response<List<CardFlowstatDay>> getCurrentDayRecordDetail(@RequestParam String date ,
                                                               @RequestParam String packageUniqueId) {
        return Response.ok(channelRecordDetailService.getCurrentDayRecordDetail(date,packageUniqueId));
    }

    @PostMapping("/pgw")
    @ApiModelProperty(value = "PGW话单实时调用", notes = "PGW话单实时调用")
    public Response<ChannelPackageCard> pgwRealTimeCDR(@RequestBody PgwRealTimeCdrVO vo) {
        return Response.ok(channelRecordDetailService.pgwRealTimeCDR(vo, null));
    }
}


