package com.ebupt.cmi.clientmanagement.consumer.upcc.context;

import com.ebupt.cmi.clientmanagement.domain.entity.PackageDirectionRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RetryContext {

    private List<UpccSignContext> upccSignContext;


    private PackageDirectionRelation packageDirectionRelation;

    //这儿会重复改表写流量
    private boolean hasSetFlow;


}
