package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * cms_channel_a2zrule_relation
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelA2zruleRelation implements Serializable {

    private Long id;

    private String corpId;

    private Long ruleId;

    private Date createTime;

    private static final long serialVersionUID = 1L;
}