package com.ebupt.cmi.clientmanagement.config;

import com.esotericsoftware.kryo.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@RefreshScope
@Component
@ConfigurationProperties(prefix = "gcsp")
@Validated
@Data
@EqualsAndHashCode(callSuper=false)
public class GcspSftpProperties extends SftpConfig {

    private Integer isUsing;
    @NotEmpty
    private List<String> remotePath;

    @NotNull
    private String summaryPreFileName;

    @NotNull
    private String detailPreFileName;

    private String normalLocalPath;

    private String monthLocalPath;

    private String makeLocalPath;

}
