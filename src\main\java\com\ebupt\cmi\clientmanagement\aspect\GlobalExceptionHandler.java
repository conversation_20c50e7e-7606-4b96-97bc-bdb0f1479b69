package com.ebupt.cmi.clientmanagement.aspect;

import cn.hutool.core.util.StrUtil;
import com.ebupt.cmi.clientmanagement.domain.response.ApiResponseEnum;
import com.ebupt.cmi.clientmanagement.domain.response.OutResponse;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.exception.*;
import com.ebupt.elk.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.tomcat.util.buf.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/3/25 16:15
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 参数校验异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler
    public Response<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        List<String> allErrors = ex.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        Response result = Response.error(StringUtils.join(allErrors, ','));
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    @ExceptionHandler
    public Response<Void> handleBindException(BindException ex) {
        List<String> allErrors = ex.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        Response result = Response.error(StringUtils.join(allErrors, ','));
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    @ExceptionHandler
    public Response<Void> handleMissingRequestParamException(MissingServletRequestParameterException ex) {
        Response result = Response.error(String.format("缺少必要请求参数:[%s]", ex.getParameterName()));
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    @ExceptionHandler
    public Response<Void> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        Response result = Response.error(String.format("不支持的HTTP请求方式:[%s]", ex.getMethod()));
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    /**
     * BizException
     *
     * @param ex
     * @return
     */
    @ExceptionHandler
    public Response<Void> handleBizException(BizException ex) {
        final String message = ex.getMessage();
        log.warn("业务异常：{}", message);
        if (ex.getCause() != null) {
            log.warn("", ex);
        }
        final String code = ex.getCode();
        Response result = StrUtil.isBlank(code) ? Response.error(message) : Response.error(code,message);
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), message, ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    /**
     * OutException
     *
     * @param ex
     * @return
     */
    @ExceptionHandler
    public OutResponse handleOutException(OutException ex) {
        final String message = ex.getMessage();
        log.warn("业务异常：{}", message);
        if (ex.getCause() != null) {
            log.warn("", ex);
        }
        OutResponse result = OutResponse.error();
        result.setResultMessage(message);
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), message, ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    /**
     *
     * 处理IBoss异常
     * @param ex
     * @return
     */
    @ExceptionHandler
    public Response<Void> handleIBossException(IBossException ex) {
        return ApiResponseEnum.matchCode(ex.getCode());
    }


    @ExceptionHandler
    public Response<Void> handleExistException(ExistException ex) {
        Response result = Response.response(ResponseResult.ACCOUNT_EXIST);
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }


    /**
     * NotRollBackBizException
     *
     * @param ex
     * @return
     */
    @ExceptionHandler
    public Response<Void> handleNotRollBackBizException(NotRollBackBizException ex) {
        final String message = ex.getMessage();
        log.warn("业务异常(不回滚)：{}", message);
        if (ex.getCause() != null) {
            log.warn("业务异常(不回滚)", ex);
        }
        final String code = ex.getCode();
        Response result = StrUtil.isBlank(code) ? Response.error(message) : Response.error(code,message);
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), message, ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    @ExceptionHandler
    public Response<Void> handleBizException(HssException ex) {
        if (ex.getCause() != null) {
            log.warn("业务异常：", ex);
        }
        Response result = Response.error(ex.getMessage());
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return Response.response(ResponseResult.OPEN_FAIL);
    }

    @ExceptionHandler
    public Response<Void> handleOtherException(MaxUploadSizeExceededException ex) {
        log.warn("上传文件过大：", ex);
        Response result = Response.response(ApiResponseEnum.FILE_SIZE_OVER);
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

    @ExceptionHandler
    public Response<Void> handleOtherException(Exception ex) {
        log.warn("服务器内部错误：", ex);
        Response result = Response.response(ApiResponseEnum.SERVER_ERROR);
        String resultString = Utils.objectToJson(result);
        MDC.put("Ext", Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
        MDC.put("resultString", resultString);
        return result;
    }

}
