package com.ebupt.cmi.clientmanagement.consumer.callback;

import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StrategyProcesor.java
 * @Description 回调接口
 * @createTime 2022年01月12日 17:00:00
 */

public interface StrategyProcessor {

    void callBack(FlowPoolConsumerContext flowPoolConsumerContext) throws InterruptedException;

}
