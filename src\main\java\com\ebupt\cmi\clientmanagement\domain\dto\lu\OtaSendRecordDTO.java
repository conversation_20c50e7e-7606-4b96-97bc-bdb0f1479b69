package com.ebupt.cmi.clientmanagement.domain.dto.lu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/4/29 15:43
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OtaSendRecordDTO {

    private List<Long> taskIdList;

    @JsonProperty("surfID")
    private Long surfId;

    @JsonProperty("surfDetailID")
    private Long surfDetailId;;

    /**
     * 重试次数，ota主动下发处
     */
    private Integer retryTimes;

    private String packageUniqueId;

}
