package com.ebupt.cmi.clientmanagement.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.strategy.AbstractFlowPoolConsumerStrategy;
import com.ebupt.cmi.clientmanagement.consumer.strategy.factory.StrategyFactory;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.BlockUpStrategy;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.LimitSpeedStrategy;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.PauseUseStrategy;
import com.ebupt.cmi.clientmanagement.consumer.strategy.impl.ResetStrategyForSingleCycle;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelSurfInfo;
import com.ebupt.cmi.clientmanagement.domain.enums.CurrentRateType;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import com.ebupt.cmi.clientmanagement.mapper.ChannelPackageCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.ChannelSurfInfoMapper;
import com.ebupt.elk.utils.Utils;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FlowPoolConsumer.java
 * @Description 流量池重置消费者
 * @createTime 2022年01月12日 15:37:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues = "flowPool.directQueue")
public class FlowPoolConsumer {

    private final StrategyFactory flowPoolConsumerStrategyFactory;

    private final ChannelSurfInfoMapper channelSurfInfoMapper;

    private final ChannelPackageCardMapper packageCardMapper;

    private final  PmsFeignClient pmsFeignClient;

    @RabbitHandler
    public void process(String flowPoolRabbitMQMessageString, Channel channel, Message message) throws IOException {


        log.info("============================rabbitMQ收到消息{}==================================", flowPoolRabbitMQMessageString);

        FlowPoolRabbitMQMessageVO flowPoolRabbitMQMessage =
                JSON.parseObject(flowPoolRabbitMQMessageString, FlowPoolRabbitMQMessageVO.class);

        String currentRateType = flowPoolRabbitMQMessage.getCurrentRateType();

        FlowPoolConsumerContext context = initContext();

        AbstractFlowPoolConsumerStrategy strategy;

        context.setFlowPoolRabbitMQMessage(flowPoolRabbitMQMessage);

        strategy = getStrategy(currentRateType);

        try {

            assert strategy != null;

            if (strategy.beforeEveryThing(context)) {

                //插入成功日志
                strategy.insertCmsFlowpoolConsumeSucLog(context);

                log.info("===========================当前卡状态已经是停用，无需做任何处理，IMSI:{}================================",
                        context.getFlowPoolRabbitMQMessage().getImsi());

                //若V卡
                if ("2".equals(context.getFlowPoolRabbitMQMessage().getCardType()) && strategy instanceof BlockUpStrategy) {

                    //其实该流程中只有停用才需要让V卡做额外外部网元交互

                    log.info("===============================该卡是V卡，需要调用外部网元==================================");

                    strategy.callBack(context);

                }

                return;
            }

            if ("2".equals(context.getFlowPoolRabbitMQMessage().getCardType()) &&
                    strategy instanceof ResetStrategyForSingleCycle &&
                    !context.getFlowPoolRabbitMQMessage().isInternalCall()) {
                log.debug("===============================该卡是V卡，为单卡单周期重置，非内部调用，不处理==================================");
                return;
            } else if ("1".equals(context.getFlowPoolRabbitMQMessage().getCardType()) &&
                    strategy instanceof ResetStrategyForSingleCycle) {
                log.debug("===============================该卡是H卡，为单卡单周期重置==================================");

                List<ChannelSurfInfo> channelSurfInfos = new ArrayList<>();
                if ("4".equals(flowPoolRabbitMQMessage.getTriggerType())) {
                    //获取套餐唯一ID信息
                    List<String> packageUniqueIdList = packageCardMapper.selectList(Wrappers.lambdaQuery(ChannelPackageCard.class)
                            .select(ChannelPackageCard::getPackageUniqueId)
                            .in(ChannelPackageCard::getPackageStatus, ChannelPackageCard.PackageStatusEnum.ACTIVATED.getValue(),
                                    ChannelPackageCard.PackageStatusEnum.ACTIVE.getValue())
                            .eq(ChannelPackageCard::getPackageType, ChannelPackageCard.PackageTypeEnum.FLOW_POOL.getValue())
                            .eq(ChannelPackageCard::getImsi, flowPoolRabbitMQMessage.getImsi())
                            .eq(ChannelPackageCard::getBelongPackageId, flowPoolRabbitMQMessage.getFlowPoolUniqueId()))
                            .stream()
                            .map(ChannelPackageCard::getPackageUniqueId)
                            .collect(Collectors.toList());
                    //获取上网集合信息
                    if (!CollectionUtils.isEmpty(packageUniqueIdList)) {
                        channelSurfInfos = channelSurfInfoMapper.selectList(new LambdaQueryWrapper<ChannelSurfInfo>()
                                .in(ChannelSurfInfo::getPackageUniqueId, packageUniqueIdList)
                                .eq(ChannelSurfInfo::getInternetType, ChannelSurfInfo.InternetType.V.getType()));
                    }
                } else {
                    channelSurfInfos = channelSurfInfoMapper.selectList(new LambdaQueryWrapper<ChannelSurfInfo>()
                            .eq(ChannelSurfInfo::getPackageUniqueId, flowPoolRabbitMQMessage.getPackageUniqueId())
                            .eq(ChannelSurfInfo::getInternetType, ChannelSurfInfo.InternetType.V.getType()));
                }

                channelSurfInfos.forEach(c -> {

                    ChannelPackageCard packageCard = packageCardMapper.selectOne(new LambdaQueryWrapper<ChannelPackageCard>()
                            .eq(ChannelPackageCard::getPackageUniqueId, c.getPackageUniqueId()));

                    VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                            .getVcardAccountInfo(c.getImsi()));

                    FlowPoolRabbitMQMessageVO build = FlowPoolRabbitMQMessageVO.builder()
                            .internalCall(true)
                            .currentRateType("8")
                            .cardType("2")
                            .imsi(c.getImsi())
                            .himsi(c.getHimsi())
                            .packageUniqueId(c.getPackageUniqueId())
                            .iccid(vcardInfo.getIccid())
                            .msisdn(vcardInfo.getMsisdn())
                            .upccLimitsSignId(packageCard.getSlowSpeedSignBizId())
                            .upccResumeSignId(packageCard.getSignBizId())
                            .flowPoolUniqueId(flowPoolRabbitMQMessage.getFlowPoolUniqueId())
                            .triggerType("4".equals(flowPoolRabbitMQMessage.getTriggerType()) ? "4" : null)
                            .build();
                    try {
                        this.process(JSON.toJSONString(build), null, null);
                    } catch (IOException e) {
                        throw new BizException(e);
                    }

                });
            }


            //进行处理
            strategy.handle(context);

            if (strategy instanceof ResetStrategyForSingleCycle) {
                //只有H卡走LU流程
                //模拟LU接口上报 需要查询GT码 入库LU模拟日志表【cms_channel_lu_local】

                strategy.callBack(context);

            }

            if (strategy instanceof BlockUpStrategy || strategy instanceof LimitSpeedStrategy || strategy instanceof PauseUseStrategy) {

                //新的方案是将网元的交互置于事务之外
                strategy.callBack(context);

            }


        } catch (Exception ex) {

            log.error(Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));

            if (ex instanceof GoodException) {

                log.warn("=======================该异常为自定义异常，非系统异常，此处只是代码需要做标记，不要紧张=========================");

                strategy.insertCmsFlowpoolConsumeSucLog(context);


            } else {

                strategy.insertIntoError(context);

            }


        } finally {
            //ack
            if (!flowPoolRabbitMQMessage.isInternalCall()) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
        }


    }

    FlowPoolConsumerContext initContext() {

        return FlowPoolConsumerContext
                .builder()
                .retryTimes(0)
                .build();

    }

    AbstractFlowPoolConsumerStrategy getStrategy(String currentRateType) {

        AbstractFlowPoolConsumerStrategy strategy = null;

        //限速，标识位=2或4或6
        if ("2".equals(currentRateType) || "4".equals(currentRateType) || "6".equals(currentRateType)) {

            strategy = flowPoolConsumerStrategyFactory.getStrategy("limitSpeed");

        }
        //停用，标识位=3或5或7
        else if ("3".equals(currentRateType) || "5".equals(currentRateType) || "7".equals(currentRateType)) {

            strategy = flowPoolConsumerStrategyFactory.getStrategy("blockup");

        } else if ("1".equals(currentRateType)) {

            strategy = flowPoolConsumerStrategyFactory.getStrategy("reset");

        } else if ("8".equals(currentRateType)) {

            strategy = flowPoolConsumerStrategyFactory.getStrategy("resetForSingle");
            log.debug("===============================该卡选取resetForSingle策略==================================");

        } else if (CurrentRateType.FLOW_POOL_PAUSE_USE.getType().equals(currentRateType)) {

            strategy = flowPoolConsumerStrategyFactory.getStrategy("pauseUse");

        }
        return strategy;

    }
}
