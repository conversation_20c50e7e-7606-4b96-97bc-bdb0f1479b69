package com.ebupt.cmi.clientmanagement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.entity.EopAccessDetail;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.EopAccessDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 * @date 2021-5-13 16:24:12
 */
@Api(tags = "能力接入信息相关接口")
@RestController
@RequestMapping("/eopAccess")
@AllArgsConstructor
public class EopAccessDetailController {

    private final EopAccessDetailService eopAccessDetailService;

    @ApiOperation("查询接口")
    @GetMapping("/info/{appKey}")
    public Response<EopAccessDetail> getEopAccessInfo(@PathVariable String appKey) {
        EopAccessDetail eopAccessDetail = eopAccessDetailService.getOne(
                Wrappers.<EopAccessDetail>lambdaQuery().eq(EopAccessDetail::getAppKey, appKey));
        return Response.ok(eopAccessDetail);
    }

}
