package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.packagedelay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packagecontext.PackageContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.service.CommonService;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.PackageDelayMessageVO;
import com.ebupt.cmi.clientmanagement.domain.dto.lu.OtaSendRecordDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageStatusEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.stat.StatFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.PackageOverdueService;
import com.ebupt.cmi.clientmanagement.service.mvno.context.ConfirmActivationContext;
import com.ebupt.cmi.clientmanagement.service.mvno.factory.ConfirmActivationFactory;
import com.ebupt.cmi.clientmanagement.service.mvno.strategy.ConfirmActivationStrategy;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageDelayStrategy.java
 * @Description 套餐超时激活策略
 * @createTime 2022年03月07日 14:57:00
 */

@Component("packageDelayStrategy")
@Slf4j
public class PackageDelayStrategy extends AbstractOutsideNetStrategy {

    @Autowired
    HvShareRepository hvShareRepository;

    @Autowired
    CommonService commonService;

    @Autowired
    RedisUtil<String> redisUtil;

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Resource
    private ChannelPackageCardMapper channelPackageCardMapper;

    @Resource
    private ChannelSurfDetailMapper channelSurfDetailMapper;

    @Resource
    private ChannelSurfInfoMapper channelSurfInfoMapper;

    @Resource
    private PackageSurfStatusLogMapper packageSurfStatusLogMapper;

    @Resource
    private PackageOverdueService packageOverdueService;

    @Resource
    private ChannelRecordDetailMapper channelRecordDetailMapper;

    @Resource
    private ChannelCardMapper channelCardMapper;

    @Resource
    private StatFeignClient statFeignClient;

    @Resource
    private ChannelLuReportMapper channelLuReportMapper;

    @Resource
    private CmsPackageDayUsedMapper cmsPackageDayUsedMapper;

    @Resource
    private CmsPackageDayRemainMapper cmsPackageDayRemainMapper;

    @Resource
    private CmsPackageCycleRemainMapper cmsPackageCycleRemainMapper;

    private final String TERMINALPACKAGE = "2";

    private final String NORMALPACKAGE = "1";


    @Override
    protected <T extends BaseContext> boolean tryOutsideNet(T context) {
        log.info("这是第{}次进行外部网元交互重试", context.getRetryTimes());
        PackageContext context1 = (PackageContext) context;
        boolean vDelHssResult = packageOverdueService.delHssSubscriber(context1.getVimsi());
        Set<String> needDelUpccSet = Response.getAndCheckRemoteData(pmsFeignClient.getIsSignUpcc(context1.getVimsi()));
        boolean vDelUpccResult = true;
        if (needDelUpccSet.size() > 0) {
            vDelUpccResult = packageOverdueService.delUpccSubscriber(needDelUpccSet, CardTypeEnum.V_CARD);
        }
        boolean hSignUpccZeroTemplateResult = packageOverdueService.delUpccSubscriber(context1.getImsi(), CardTypeEnum.H_CARD);
        return vDelHssResult && vDelUpccResult && hSignUpccZeroTemplateResult;
    }


    /**
     * 通过套餐唯一ID将cms_channel_package_card表中的对应数据更新为已激活,更新时判断套餐状态是否是激活中，若是则需要更新为已激活
     *
     * @param context 上下文
     * @param <T>     泛型
     */
    @Override
    public <T extends BaseContext> void handle(T context) throws InterruptedException {

        log.info("==========================进入套餐超时回滚流程，packageUniqueId：{}================================",
                context.getMessageVO().getPackageUniqueId());

        String packageUniqueId = context.getMessageVO().getPackageUniqueId();

        ChannelPackageCard channelPackageCard = hvShareRepository
                .getChannelPackageCardByPackageUniqueId(packageUniqueId);

        if (channelPackageCard == null) {
            log.warn("套餐数据不存在，请检查数据");
            return;
        }

        updateChannelPackageCardByStatus(channelPackageCard, context);
        callBack(context);
    }


    /**
     * 目标channelPackageCard有条件更改已激活状态
     *
     * @param channelPackageCard 目标PackageCard
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateChannelPackageCardByStatus(ChannelPackageCard channelPackageCard, BaseContext context) throws InterruptedException {

        //套餐激活状态
        //1：待激活
        //2：已激活
        //3：已使用
        //5：已过期
        //6：激活中
        String packageUniqueId = channelPackageCard.getPackageUniqueId();
        if (channelPackageCard.getPackageStatus().equals(PackageStatusEnum.ACTIVATING.getStatus())) {
            List<ChannelSurfInfo> channelSurfInfos = channelSurfInfoMapper.selectInfoListAndImsiDistinctByPackageUniqueIds(
                    Collections.singletonList(packageUniqueId));
            channelPackageCardMapper.update(null, new LambdaUpdateWrapper<ChannelPackageCard>()
                    .set(ChannelPackageCard::getSlowSpeedSignBizId, null)
                    .set(ChannelPackageCard::getActiveType, null)
                    .set(ChannelPackageCard::getActiveCategory, null)
                    .set(ChannelPackageCard::getActiveTime, null)
                    .set(ChannelPackageCard::getExpireTime, null)
                    .set(ChannelPackageCard::getMcc, null)
                    .set(ChannelPackageCard::getFirstActiveNumber, null)
                    .set(ChannelPackageCard::getChangeStatus, null)
                    .set(ChannelPackageCard::getPackageStatus, "1")
                    .eq(ChannelPackageCard::getId, channelPackageCard.getId()));
            channelSurfInfoMapper.delete(Wrappers.lambdaQuery(ChannelSurfInfo.class)
                    .eq(ChannelSurfInfo::getPackageUniqueId, packageUniqueId));
            packageSurfStatusLogMapper.delete(Wrappers.lambdaQuery(PackageSurfStatusLog.class)
                    .eq(PackageSurfStatusLog::getPackageUniqueId, packageUniqueId));
            Set<String> vImsis = channelSurfInfos.stream().filter(channelSurfInfo -> channelSurfInfo.getInternetType().equals("2"))
                    .map(ChannelSurfInfo::getImsi).collect(Collectors.toSet());
            channelSurfDetailMapper.delete(Wrappers.lambdaQuery(ChannelSurfDetail.class)
                    .in(ChannelSurfDetail::getSurfId, channelSurfInfos.stream().map(ChannelSurfInfo::getSurfId).collect(Collectors.toList())));
            channelRecordDetailMapper.update(ChannelRecordDetail.builder()
                    .packageUniqueId(packageUniqueId + "_Rollback")
                    .build(), Wrappers.lambdaUpdate(ChannelRecordDetail.class).eq(ChannelRecordDetail::getPackageUniqueId, packageUniqueId));
            channelCardMapper.update(null, new LambdaUpdateWrapper<ChannelCard>()
                    .set(ChannelCard::getLastVimsi, null)
                    .set(ChannelCard::getPackageUniqueId, null)
                    .eq(ChannelCard::getImsi, channelPackageCard.getImsi())
                    .eq(ChannelCard::getPackageUniqueId, packageUniqueId));
            channelLuReportMapper.delete(Wrappers.lambdaQuery(ChannelLuReport.class)
                    .eq(ChannelLuReport::getPackageUniqueId, packageUniqueId));
            cmsPackageDayUsedMapper.delete(Wrappers.lambdaQuery(CmsPackageDayUsed.class)
                    .eq(CmsPackageDayUsed::getPackageUniqueId, packageUniqueId));
            cmsPackageCycleRemainMapper.delete(Wrappers.lambdaQuery(CmsPackageCycleRemain.class)
                    .eq(CmsPackageCycleRemain::getPackageUniqueId, packageUniqueId));
            cmsPackageDayRemainMapper.delete(Wrappers.lambdaQuery(CmsPackageDayRemain.class)
                    .eq(CmsPackageDayRemain::getPackageUniqueId, packageUniqueId));
            statFeignClient.rollbackPackage(packageUniqueId);
            PackageContext packageContext = (PackageContext) context;
            packageContext.setImsi(channelPackageCard.getImsi());
            packageContext.setVimsi(vImsis);
            Set<String> keys = channelSurfInfos
                    .stream()
                    .filter(channelSurfInfo -> channelSurfInfo.getInternetType().equals("2"))
                    .map(item -> String.format(BizConstants.LU_V2_KEY_PATTERN, item.getMadeImsi(), item.getMcc()))
                    .collect(Collectors.toSet());
            Set<String> keys2 = channelSurfInfos
                    .stream()
                    .filter(channelSurfInfo -> channelSurfInfo.getInternetType().equals("2"))
                    .map(item -> String.format(BizConstants.V_NEED_CANCEL_RECORD, item.getImsi()))
                    .collect(Collectors.toSet());
            Set<String> keys3 = channelSurfInfos
                    .stream()
                    .map(item -> String.format(BizConstants.V_CARD, item.getImsi()))
                    .collect(Collectors.toSet());
            keys.addAll(keys3);
            keys.addAll(keys2);
            redisUtil.batchDel(keys);
        } else {
            throw new GoodException("套餐已激活，延迟回滚流程结束");
        }

    }

    public void removeRedisKey(ChannelPackageCard channelPackageCard,
                               BaseContext context) {

        ///**
        //     * 套餐类型：
        //     * 1：套餐
        //     * 2：终端线下卡池
        //     * 3：流量池
        //     * 4：加油包
        //     */
        if ("1".equals(channelPackageCard.getPackageType())) {

            log.info("===================普通套餐，删除对应Redis key=====================");

            PackageDelayMessageVO messageVO = (PackageDelayMessageVO) context.getMessageVO();

            String madeImsi = messageVO.getMadeImsi();

            String mcc = messageVO.getMcc();

            String recordStr = redisUtil.get(String.format(
                    BizConstants.LU_V2_KEY_PATTERN,
                    madeImsi,
                    mcc));

            OtaSendRecordDTO sendRecord = JSON.parseObject(recordStr, OtaSendRecordDTO.class);

            redisUtil.del(String.format(BizConstants.LU_V2_KEY_PATTERN, madeImsi, mcc));

            if (!CollectionUtils.isEmpty(sendRecord.getTaskIdList())) {
                redisUtil.batchDel(BizConstants.LU_V2_OTA_KEY_PREFIX, sendRecord.getTaskIdList());
            }

        }

    }

    public static void main(String[] args) {
        System.out.println(isPalindrome(123321));
    }

    public static boolean isPalindrome(int x) {
        if (x == 0) return true;
        if (x < 0 || x % 10 == 0) return false;
        int reversed = 0;
        while (x > reversed) {
            reversed = reversed * 10 + x % 10;
            x /= 10;
        }
        return x == reversed || x == reversed / 10;
    }

}
