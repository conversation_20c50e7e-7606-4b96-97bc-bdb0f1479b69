package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class CmsFlowpoolThresholdRemind implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
      @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "流量池id，按照规则自生成")
    private String flowPoolId;

    @ApiModelProperty(value = " 厂商id")
    private String corpId;

    @ApiModelProperty(value = "流量池唯一id，uuid")
    private String flowPoolUniqeId;

    @ApiModelProperty(value = "是否提醒：	1：已提醒	2：未提醒	")
    @TableField("Is_remind")
    private String isRemind;

    @ApiModelProperty(value = "提醒时间")
    private Date remindTime;

    public CmsFlowpoolThresholdRemind(String flowPoolId, String corpId, String flowPoolUniqeId, String isRemind, Date remindTime) {
        this.flowPoolId = flowPoolId;
        this.corpId = corpId;
        this.flowPoolUniqeId = flowPoolUniqeId;
        this.isRemind = isRemind;
        this.remindTime = remindTime;
    }
}
