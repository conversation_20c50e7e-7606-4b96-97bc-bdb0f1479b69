package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 主卡状态枚举
 * @date 2021/4/16 14:29
 */
@AllArgsConstructor
@Getter
public enum CardStatusEnum {

    /**
     * 正常
     */
    NORMAL("1"),
    /**
     * 暂停
     */
    STOP("2"),
    /**
     * 注销
     */
    CANCEL("3")
    ;


    private String status;

}
