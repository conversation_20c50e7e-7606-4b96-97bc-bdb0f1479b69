package com.ebupt.cmi.clientmanagement.consumer.strategy.impl;

import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.strategy.AbstractFlowPoolConsumerStrategy;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResetStrategy.java
 * @Description 恢复策略 枚举值 =1
 * @createTime 2022年01月12日 16:48:00
 */

@Component("reset")
@Slf4j
public class ResetStrategy extends AbstractFlowPoolConsumerStrategy {
    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = GoodException.class)
    public void handle(FlowPoolConsumerContext flowPoolConsumerContext) {


        log.info("==============重置流程开始，卡{}================",
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());


        /**
         * 卡类型: 1:H  2:V
         */
        if ("2".equals(flowPoolConsumerContext.getFlowPoolRabbitMQMessage()
                .getCardType())) {

            log.info("================该卡{}为V卡，无需进行任何操作================", flowPoolConsumerContext.getFlowPoolRabbitMQMessage()
                    .getImsi());
            super.insertCmsFlowpoolConsumeSucLog(flowPoolConsumerContext);

        } else {
            super.handle(flowPoolConsumerContext);
        }

        log.info("==============重置流程结束，卡{}================",
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());

    }

    @Override
    protected boolean tryOutsideNet(FlowPoolConsumerContext flowPoolConsumerContext) {
        return true;
    }
}
