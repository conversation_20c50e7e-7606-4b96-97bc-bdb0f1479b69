package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.excel.annotation.ExcelExport;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ESIMInfoExoirtDTO {

    @ExcelExport(description = "ICCID", index = "0")
    private String iccid;
    @ExcelExport(description = "IMSI", index = "1")
    private String imsi;
    @ExcelExport(description = "MSISDN", index = "2")
    private String msisdn;
    @ExcelExport(description = "PUK", index = "3")
    private String puk1;
    @ExcelExport(description = "PIN", index = "4")
    private String pin2;
    @ExcelExport(description = "DownloadURL", index = "5")
    private String esimUrl;
}
