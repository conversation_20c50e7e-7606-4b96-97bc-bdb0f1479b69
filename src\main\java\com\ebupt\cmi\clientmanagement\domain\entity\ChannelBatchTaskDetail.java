package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelBatchTaskDetail.java
 * @Description cms_channel_batch_task_detail实体类
 * @createTime 2021年06月17日 15:33:00
 */

@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_batch_task_detail")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelBatchTaskDetail extends BaseEntity {

    Long id;
    /**
     * 任务id
     */
    Long taskId;
    /**
     * iccid
     */
    String iccid;
    /**
     * 套餐id
     */
    String packageId;
    /**
     * 币种编码：
     * 156：人民币
     * 840：美元
     * 344： 港币
     */
    String currencyCode;
    /**
     * 价格，单位分
     */
    BigDecimal amount;
    /**
     * 厂商id
     */
    String corpId;
    /**
     * 是否新出库
     * 1：是
     * 2：否
     */
    String isNew;
    /**
     * imsi号码
     */
    String imsi;
    /**
     * msisdn号码
     */
    String msisdn;
    /**
     * 卡片形态
     * 1：普通卡（实体卡）
     * 2：Esim卡
     * 3：贴片卡
     */
    String cardForm;
    /**
     * 套餐有效期
     * 购买时套餐上面的当前时间+购买有效期天数
     */
    LocalDateTime effectiveDay;
    /**
     * 套餐唯一id，uuid
     */
    String packageUniqueId;
    /**
     * 语言
     * 1：中文
     * 2：英文
     * 3：简体
     */
    String sendLang;
    /**
     * 状态
     * 1：正常
     */
    String cardStatus;

    String salesChannel;
    /**
     *合作模式
     * 1：代销
     * 2：A2Z
     */
    String cooperationMode;

    String isRollback;

}
