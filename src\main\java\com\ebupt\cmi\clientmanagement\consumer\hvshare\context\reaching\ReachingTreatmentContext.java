package com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import lombok.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReachingTreatmentContext.java
 * @Description 达量处理上下文享元
 * @createTime 2022年02月28日 15:45:00
 */

@Builder
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class ReachingTreatmentContext extends BaseContext {

    protected String appGroupId;

    /**
     * 需要处理的v卡
     */
    protected Set<String> vImsi;

    /**
     * 需要处理的app
     */
    protected Set<Long> app;

    /**
     * 逻辑标识
     */
    protected String logic;


    protected boolean needCallback;

    protected boolean needLimitSpeed;

}
