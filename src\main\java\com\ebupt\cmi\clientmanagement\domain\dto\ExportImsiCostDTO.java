package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExportImsiCostDTO {
    @NotBlank
    private String corpId;

    @NotBlank
    private String userId;

    @NotBlank
    private String dateStart;

    @NotBlank
    private String dateEnd;

    private String cooperationMode;

    private List<String> cooperationModes;

    private Long id;
}
