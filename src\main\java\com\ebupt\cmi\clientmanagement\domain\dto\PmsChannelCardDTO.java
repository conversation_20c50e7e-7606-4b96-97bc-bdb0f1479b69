package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PmsChannelCardDTO
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>n
 * @Date: 2021/6/18 10:57
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class PmsChannelCardDTO {

    private Long id;

    private String corpId;

    private String imsi;

    private String msisdn;

    private String iccid;

    private String poolId;

    private String type;

    private String upccOpenStatus;

    private String gtpRouteStatus;

    private Integer routeId;

    @Override
    public String toString() {
        return id + "|" + imsi + "|" +  msisdn + "|" + iccid ;
    }
}
