package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 订单状态
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/4/16 15:57
 */
@Getter
@AllArgsConstructor
public enum OrderType {

    CARD("1", "卡"),

    PACKAGES("2", "套餐"),

    CARD_AND_PACKAGE("3", "卡+套餐"),

    CARD_POOL("4", "终端线下卡池"),

    FLOW_POOL("5", "流量池"),

    TERMINAL_PACKAGE("6", "终端厂商套餐");


    private String k;

    private String val;
}
