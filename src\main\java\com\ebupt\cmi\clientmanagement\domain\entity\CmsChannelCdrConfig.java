package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * cms_channel_cdr_config
 *
 * <AUTHOR>
@Data
@TableName("cms_channel_cdr_config")
public class CmsChannelCdrConfig implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 渠道商id
     */
    private String corpId;

    /**
     * 上传配置：1.ARCH 2.GTP Proxy
     */
    private Integer address;

    private static final long serialVersionUID = 1L;
}