package com.ebupt.cmi.clientmanagement.controller.channelself;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.domain.dto.BlandCardOrdersDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.ExportImsiCostDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.PurchasedPackageDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.RevertChannelDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channelself.GetChannelFlowDetailDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channelself.OrderDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.channelself.PackageDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelLuReport;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsInvoiceAuditRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageBatchsubRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.RefuelInputVO;
import com.ebupt.cmi.clientmanagement.domain.req.UpccControlReq;
import com.ebupt.cmi.clientmanagement.domain.response.CreateOrderResp;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.validation.CommonGroup;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.UserPackageForm;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.UserPackageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.*;
import com.ebupt.cmi.clientmanagement.domain.vo.cssupport.PackageEarlierRecoveryVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardPageFormChannel;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.clientmanagement.service.ICsSupportService;
import com.ebupt.cmi.clientmanagement.service.PackageService;
import com.ebupt.cmi.clientmanagement.service.channelself.BatchPackageBuyService;
import com.ebupt.cmi.clientmanagement.service.channelself.ChannelSelfService;
import com.ebupt.cmi.clientmanagement.service.decorator.BlankCardOrderSerivceStatusMachineDecorator;
import com.ebupt.cmi.clientmanagement.statusMachine.BlankCardOrderStatusMachine;
import com.ebupt.cmi.clientmanagement.utils.CsvExportUtil;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChannelSelfServerController.java
 * @Description 渠道自服务Controller
 * @createTime 2021年06月16日 17:37:00
 */
@Api(tags = "渠道自服务相关接口")
@RestController
@RequestMapping("/channelSelfServer")
@Slf4j
public class ChannelSelfServerController {

    @Autowired
    PackageService packageService;

    @Autowired
    ChannelService channelService;

    @Autowired
    ICsSupportService csSupportService;

    @Autowired
    ChannelSelfService channelSelfService;

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Autowired
    BatchPackageBuyService batchPackageBuyService;

    @Autowired
    @Qualifier("blankCardOrderSerivceDecorator")
    BlankCardOrderSerivceStatusMachineDecorator blankCardOrderSerivceDecorator;

    @Value("${maxExpire}")
    private Long number;

    @GetMapping("/task")
    @ApiOperation(value = "批量配置任务查询接口")
    @NormalLog
    public Response taskSearch(@Validated({CommonGroup.class}) TaskVO taskVO) {


        return Response.ok(channelSelfService.getTaskBatchList(taskVO.getCorpId(), taskVO.getStartDate(),
                taskVO.getEndDate(), taskVO.getPageSize(), taskVO.getPageNumber(), taskVO.getTaskName()));
    }

    @GetMapping("/test1")
    @NormalLog
    public void test1() {
        channelSelfService.test1();
    }

    @PutMapping("/stockTransfer")
    @ApiOperation(value = "库存划拨")
    @OperationLog(operationName = "渠道自服务——库存划拨", operationType = OperationTypeEnum.UPDATE)
    public Response stockTransfer(@Validated StockTransferVO vo) {
        channelSelfService.stockTransfer(vo);
        return Response.ok();
    }

    @GetMapping("/task/detailAlpha")
    @ApiOperation(value = "详情1", notes = "展示[cms_channel_batch_task_detail] 中对应信息即可")
    @NormalLog
    public Response detailAlpha(@RequestParam Long taskId,
                                @RequestParam Long pageSize,
                                @RequestParam Long pageNumber) {

        if (pageSize < 0 || pageNumber < 0) {
            return Response.error("Page number, page size does not meet the specification, please try again");
        }

        return Response.ok(channelSelfService.getTaskBatchDetailDTOs(taskId, pageSize, pageNumber));
    }

    @GetMapping("/updatedCardHasPackage")
    @ApiOperation("定时更新卡是否包含可用套餐")
    public Response updatedCardHasPackage() {
        channelSelfService.updatedCardHasPackage();
        return Response.ok();
    }

    @GetMapping("/task/detailCharlie")
    @ApiOperation(value = "详情3", notes = "ICCIDS按钮，根据corpId获取卡详情")
    @NormalLog
    public Response detailCharlie(@Validated({CommonGroup.class}) CharlieVO pageVO) {

        return Response.ok(channelSelfService.getTaskBatchDetailDTOs(pageVO));
    }

    @GetMapping("/task/detailBeta")
    @ApiOperation(value = "详情2", notes = "根据imsi、iccid、msisdn调用产品服务.主卡信息查询接口()")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "imsi", value = "imsi", paramType = "query", dataType = "String", required = true)
    }
    )
    @NormalLog
    public Response detailBeta(@RequestParam String imsi) {
        return pmsFeignClient.getOneCard(imsi);
    }

    @GetMapping("/cash")
    @ApiOperation(value = "查押金")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "corpId", value = "corpId", paramType = "query", dataType = "String", required = true)
    }
    )
    @NormalLog
    public Response getCash(@RequestParam String corpId, @RequestParam String cooperationMode) {

        return Response.ok(channelSelfService.getCashByCorpId(corpId, cooperationMode));

    }

    @GetMapping("/cash/record")
    @ApiOperation(value = "查询流水记录")
    @NormalLog
    public Response getRecordByCorpId(@Validated({CommonGroup.class}) ChannelPageVO pageVO) {

        return Response.ok(channelSelfService.getRecord(pageVO.getCorpId(), pageVO.getPageSize(), pageVO.getPageNumber()));
    }

    @GetMapping("/cash/package")
    @ApiOperation(value = "查询可购买套餐-给押金管理用")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "corpId", value = "渠道商Id", paramType = "query", dataType = "String", required = true)
    }
    )
    @NormalLog
    public Response getAvilablePackageByCorpId(@Validated({CommonGroup.class}) ChannelPageVO pageVO) {

        return channelSelfService.getAvilablePackages(pageVO.getCorpId(), pageVO.getCooperationMode(), pageVO.getPageSize(), pageVO.getPageNumber());
    }

    @GetMapping("/package/packageDetail")
    @ApiOperation(value = "查询可购买套餐-套餐管理模块用")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "corpId", value = "渠道商Id", paramType = "query", dataType = "String", required = true)
    }
    )
    @NormalLog
    public Response getAvilablePackageByCorpIdDetail(@Validated({CommonGroup.class}) AvlibablePackageVO pageVO) {

        return channelSelfService.getAvilablePackagesDetail(pageVO);
    }

    @GetMapping("/export/packageDetail")
    @ApiOperation(value = "导出可购买套餐-套餐管理模块用")
    @NormalLog
    public void exportAvilablePackageByCorpIdDetail(
            @Validated({CommonGroup.class}) AvlibablePackageVO pageVO,
            HttpServletResponse response) {

        try {

            pageVO.setPageNumber(1L);
            pageVO.setPageSize(number);

            List<PackageDTO> result = channelSelfService.getAvilablePackagesDetail(pageVO).getData().getRecord();

            // 构造导出数据结构

            // 设置表头
            String titles = "Package ID,Package Name,Charges";

            // 设置每列字段
            String keys = "packageId,packageName,charges";

            // 构造导出数据
            List<Map<String, Object>> datas = new ArrayList<>();

            Map<String, Object> map;

            DecimalFormat df = new DecimalFormat("#0.00");

            if (result != null) {
                for (PackageDTO data : result) {
                    map = new HashMap<>();
                    map.put("packageId", data.getPackageId());
                    map.put("packageName", data.getNameEn());
                    if ("2".equals(data.getGroupType())) {
                        map.put("charges", df.format(Double.parseDouble(data.getPackagePrice()) / 100));
                    } else {
                        map.put("charges", df.format(Double.parseDouble(data.getGroupPrice()) / 100));
                    }

                    datas.add(map);

                }
            }

            // 设置导出文件前缀
            String fName = "AvilablePackages_";

            // 文件导出
            OutputStream os = response.getOutputStream();
            CsvExportUtil.responseSetProperties(fName, response);
            CsvExportUtil.doExport(datas, titles, keys, os);
            os.close();
        } catch (Exception e) {
            log.warn("导出失败" + e.getMessage(), e);
        }

    }

    @PostMapping("/package/price")
    @ApiOperation(value = "获取套餐折扣价")
    @NormalLog
    public Response getRightPrice(@RequestBody @Valid PackageVO packageVO) {

        return Response.ok(channelSelfService.getPackagePrice(packageVO));

    }


    @PostMapping("/package/buySingle")
    @ApiOperation(value = "购买单个套餐")
    @OperationLog(operationName = "渠道自服务-购买单个套餐", operationType = OperationTypeEnum.ADD)
    @NormalLog
    public Response buySinglePackage(@RequestBody @Valid PackageVO packageVO) {
        return channelSelfService.buySingle(packageVO);
    }

    @PostMapping("/package/buyBatch")
    @ApiOperation(value = "购买批量套餐")
    @OperationLog(operationName = "渠道自服务-批量购买套餐", operationType = OperationTypeEnum.ADD)
    public Response buyBatchPackage(MultipartFile file, @RequestParam("corpId") String corpId, @RequestParam("cooperationMode") String cooperationMode) {

        Optional.ofNullable(file).orElseThrow(() -> new BizException("Upload file cannot be empty"));
        String[] split = file.getOriginalFilename().split("\\.");
        String suffix = split[split.length - 1];
        if (!"csv".equalsIgnoreCase(suffix)) {
            Response.error("Please upload a text file in csv format");
        }
        if (file.isEmpty()) {
            Response.error("Please do not upload empty files");
        }
        batchPackageBuyService.buyBatchPackage(file, corpId, cooperationMode);

        return Response.ok();
    }

    @GetMapping("/package/buyBatch")
    @ApiOperation(value = "获取购买批量套餐的文件记录")
    @NormalLog
    public Response getBatchPackagesLog(@Validated({CommonGroup.class}) ChannelPageVO channelPageVO) {

        Response response = Response.ok();

        IPage<PackageBatchsubRecord> recordIPage = batchPackageBuyService.getBatchsubRecord(channelPageVO);

        response.setData(recordIPage);

        return response;
    }

    @GetMapping("/package/file/{id}")
    @ApiOperation(value = "下载批量购买原始文件")
    public void getBatchPackagesFile(@PathVariable("id") String id, HttpServletResponse res) throws IOException {

        InputStream inputStream = null;
        OutputStream os = null;

        PackageBatchsubRecord packageBatchsubRecord = batchPackageBuyService.getFile(id);
        if (packageBatchsubRecord == null) {
            throw new BizException("Error, file not found on server");
        }

        try {
            File file = new File(packageBatchsubRecord.getSourceFilePath());

            os = res.getOutputStream();

            inputStream = new FileInputStream(file);

            // 文件导出
            CsvExportUtil.responseSetProperties(packageBatchsubRecord.getFileName(), res);
            CsvExportUtil.doExport(os, inputStream);
            os.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("An error occurred and the export failed");
        } finally {

            if (inputStream != null) {

                inputStream.close();

            }

            if (os != null) {

                os.close();

            }
        }

    }

    @GetMapping("/package/failFile/{id}")
    @ApiOperation(value = "下载批量购买失败文件")
    public void getBatchPackagesFailFile(@PathVariable("id") String id, HttpServletResponse res) throws IOException {

        InputStream inputStream = null;
        OutputStream os = null;

        PackageBatchsubRecord packageBatchsubRecord = batchPackageBuyService.getFile(id);
        if (packageBatchsubRecord == null) {
            throw new BizException("Error, file not found on server");
        }

        try {
            File file = new File(packageBatchsubRecord.getFailFilePath());

            os = res.getOutputStream();

            inputStream = new FileInputStream(file);

            // 文件导出
            CsvExportUtil.responseSetProperties(packageBatchsubRecord.getFileName(), res);
            CsvExportUtil.doExport(os, inputStream);
            os.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("An error occurred and the export failed");
        } finally {

            if (inputStream != null) {

                inputStream.close();

            }

            if (os != null) {

                os.close();

            }
        }

    }

    @GetMapping("/getRevertChannel")
    @ApiOperation(value = "查询归属渠道商")
    @NormalLog
    public Response<List<RevertChannelDTO>> getRevertChannel(@RequestParam String corpId, @RequestParam String cooperationMode) {

        Response response = Response.ok(channelSelfService.getRevertChannel(corpId, cooperationMode));
        return response;
    }

    @GetMapping("/order")
    @ApiOperation(value = "订单查询接口")
    @NormalLog
    public Response getOrders(@Validated({CommonGroup.class}) OrderVO orderVO) {

        Response response = Response.ok();
        response.setData(channelSelfService.getOrders(orderVO.getPackageName(), orderVO.getPackageNameEn(),
                orderVO.getIccid(), orderVO.getStartDate(), orderVO.getEndDate(), orderVO.getOrderUserId(), orderVO.getCorpId()
                , orderVO.getPageSize(), orderVO.getPageNumber(), orderVO.getCooperationMode(), orderVO.getRevertCorpId()));
        return response;
    }

    @PostMapping("/order")
    @ApiOperation(value = "订单退订接口")
    @OperationLog(operationName = "渠道自服务-订单退订接口", operationType = OperationTypeEnum.DELETE)
    @NormalLog
    public Response deleteOrder(@RequestBody OrderDTO orderDTO, @RequestParam("corpId") String corpId) {
        return channelSelfService.deleteOrder(orderDTO, corpId);
    }

    @PostMapping("/support/Hcard")
    @ApiOperation(value = "渠道商订购主卡分页查询接口")
    @NormalLog
    public Response getChannelCard(@RequestBody @Valid CardPageFormChannel cardPageFormChannel) {

        return Response.ok(channelSelfService.getChannelCard(cardPageFormChannel.getCorpId(),
                cardPageFormChannel.getPageSize(), cardPageFormChannel.getPageNumber(), cardPageFormChannel));
    }

    @GetMapping("/support/packageDetail")
    @ApiOperation(value = "已购买套餐")
    @NormalLog
    public Response getChannelpackageDetail(@Validated({CommonGroup.class}) CurrentPackageForChannelVO packageVO) {

        PageResult<PurchasedPackageDTO> result = channelSelfService.getPurchasedPackages(packageVO);

        return Response.ok(result);
    }

    @GetMapping("/support/packageUsed")
    @ApiOperation(value = "套餐使用详情分页查询接口")
    @NormalLog
    public Response<PageResult<ChannelLuReport>> getpackageUsedDetail(@Validated({CommonGroup.class}) CurrentPackageForChannelVO packageVO) {

        PageResult<ChannelLuReport> results = channelSelfService.getHLuRecords(packageVO);
        return Response.ok(results);
    }

    @ApiOperation(value = "激活套餐")
    @PostMapping("/support/active")
    @OperationLog(operationName = "渠道自服务-激活套餐", operationType = OperationTypeEnum.ADD)
    public Response<Void> activePackage(@RequestBody @Validated(value = CommonGroup.class) ActiveSpecificPackageVO activeSpecificPackageVO) {
        packageService.activePackage(activeSpecificPackageVO);
        return Response.ok();
    }

    @ApiOperation(value = "套餐冻结接口(提前回收)")
    @PostMapping("/support/recoveryPackage")
    @OperationLog(operationName = "渠道自服务-提前回收", operationType = OperationTypeEnum.DELETE)
    public Response<Void> recoveryPackageEarlier(@Validated PackageEarlierRecoveryVO packageEarlierRecoveryVO) {
        csSupportService.recoveryPackageEarlier(packageEarlierRecoveryVO);
        return Response.ok();
    }

    @ApiOperation(value = "订单导出接口")
    @PostMapping("/orderExport")
    public Response<ExportVO> orderExport(@RequestBody @Validated ChannelSelfOrderExportVO orderVO) {
        return Response.ok(channelSelfService.exportOrder(orderVO));
    }

    @ApiOperation(value = "库存导出接口")
    @PostMapping("/support/storeExport")
    public Response<ExportVO> storeExport(@RequestParam String corpId, @RequestParam String userId,
                                          @RequestParam String cooperationMode, @RequestParam(required = false) String chooseCorpId) {
        return Response.ok(channelSelfService.storeManageExport(corpId, userId, cooperationMode, chooseCorpId));
    }


    @ApiOperation(value = "加油包管理-套餐分页列表")
    @PostMapping("/packageRefuel/package/pageList")
    public Response<PageResult<UserPackageVO>> pageList(@RequestBody @Valid UserPackageForm form) {
        return Response.ok(channelSelfService.userPackagePageList(form));
    }

    @ApiOperation("购买加油包接口")
    @OperationLog(operationName = "渠道自服务-购买加油包", operationType = OperationTypeEnum.ADD)
    @PostMapping("/packageRefuel/buyPackageRefuel")
    public Response<CreateOrderResp> buyPackageRefuel(@RequestBody @Valid BuyPackageRefuelVO buy) {
        return Response.ok(channelSelfService.buyPackageRefuel(buy));
    }

    @ApiOperation(value = "加油包分页列表")
    @PostMapping("/packageRefuel/refuelList")
    public Response<ChannelRefuelPage> selectRefuelList(@RequestParam String corpId, @RequestBody RefuelInputVO refuelInputVO) {
        if (StrUtil.isBlank(refuelInputVO.getCooperationMode())) {
            throw new BizException("cooperationMode is mandatory");
        }

        return Response.ok(channelSelfService.selectRefuelList(corpId, refuelInputVO));
    }

    @PostMapping("/upccControl")
    public Response<Void> upccControlApi(@RequestBody UpccControlReq upccControlReq) {
        return channelSelfService.upccControl(upccControlReq);
    }

    @ApiOperation("白卡订单新增")
    @OperationLog(operationName = "渠道自服务-白卡订单新增", operationType = OperationTypeEnum.ADD)
    @PostMapping("/addCardOrders")
    public Response addBlankCardOrders(@RequestBody @Valid AddBlankCardVO addBlankCardVO) {
        channelSelfService.addBlankCardOrders(addBlankCardVO);
        return Response.ok();
    }

    @ApiOperation("白卡订单管理")
    @PostMapping("/getBlankCardOrders")
    public Response<PageResult<BlandCardOrdersDTO>> getBlankCardOrders(@RequestBody @Valid getBlankCardOrdersVO getBlankCardOrdersVO) {
        return Response.ok(channelSelfService.getBlankCardOrders(getBlankCardOrdersVO));
    }

    @ApiOperation("渠道自服务-上传付款证明")
    @PostMapping(value = "/uploadPaymentProof", headers = "content-type=multipart/form-data")
    @OperationLog(operationName = "渠道自服务-上传付款证明", operationType = OperationTypeEnum.UPDATE)
    public Response uploadPaymentProof(@Validated UploadPaymentProofVO uploadPaymentProofVO) {
        blankCardOrderSerivceDecorator.uploadPaymentProof(uploadPaymentProofVO);
        return Response.ok();
    }

    @ApiOperation("白卡订单码号导出")
    @PostMapping(value = "/blankCardExport")
    public Response blankCardExport(@RequestParam String corpId, @RequestParam String orderBatch) {
        return Response.ok(channelSelfService.blankCardExport(corpId, orderBatch));
    }

    @PostMapping(value = "/getBlankCardExportIccid")
    public Response getBlankCardExportIccid(@RequestParam String corpId, @RequestParam String orderBatch) {
        return Response.ok(channelSelfService.getBlankCardExportIccid(corpId, orderBatch));
    }

    @ApiOperation("白卡订单说明")
    @GetMapping(value = "/blankCardIllustrate")
    public Response<Map<String, String>> blankCardIllustrate() {
        return Response.ok(channelSelfService.blankCardIllustrate());
    }

    @PutMapping("/cancelOrder/{id}")
    @OperationLog(operationName = "渠道自服务——撤销订单", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> cancelOrder(@PathVariable Long id) {
        blankCardOrderSerivceDecorator.cancelOrder(id, BlankCardOrderStatusMachine.BlankCardOrderRole.USER);
        return Response.ok();
    }

    @GetMapping("/directionalAppSurfDetail")
    public Response directionalAppSurfDetail(@RequestParam String packageId, @RequestParam String imsi,
                                             @RequestParam String packageUniqueId) {
        return Response.ok(channelSelfService.getDirectionalAppSurfDetail(packageId, packageUniqueId, imsi));
    }

    // 渠道流量明细
    @GetMapping("/getChannelFlowDetail")
    public Response getChannelFlowDetail(@RequestBody GetChannelFlowDetailDTO dto) {
        return Response.ok(channelSelfService.getChannelFlowDetail(dto));
    }

    // 渠道imsi费id
    @GetMapping("/getChannelImsiCost")
    public Response<List<Long>> getChannelImsiCost(@RequestParam String corpId, @RequestParam String cooperationMode) {
        return Response.ok(channelSelfService.getChannelImsiCost(corpId,cooperationMode));
    }

    //导出渠道商imsi费详情
    @PostMapping("/exportImsiCost")
    @ApiOperation(value = "导出渠道商imsi费详情")
    public Response<ExportVO> exportImsiCost(@RequestBody ExportImsiCostDTO vo) {
        return Response.ok(channelSelfService.exportImsiCost(vo));
    }

    @PostMapping("/deposit/record")
    @ApiOperation(value = "押金充值分页查询")
    @NormalLog
    public Response<List<DepositRecordVO>> depositRecord(@RequestBody @Valid GetDepositVo getDepositVo) {
        IPage<DepositRecordVO> results = channelSelfService.depositRecord(getDepositVo);
        return Response.ok(results);
    }
    @GetMapping("/deposit/record/getById")
    @ApiOperation(value = "根据id查询押金充值")
    @NormalLog
    public Response<DepositRecordVO> depositRecordGetById(@RequestParam String id) {
        return Response.ok(channelSelfService.depositRecordGetById(id));
    }
    @GetMapping("/deposit/record/updateChargeStatus")
    @ApiOperation(value = "根据id查询押金充值")
    @NormalLog
    public Response<DepositRecordVO> updateChargeStatus(@RequestParam long id, @RequestParam String status) {
        channelSelfService.updateChargeStatus(id, status);
        return Response.ok();
    }
    @GetMapping("/deposit/info")
    @ApiOperation(value = "押金充值币种及发票类型查询")
    @NormalLog
    public Response<DepositRecordVO> depositInfo(@RequestParam String corpId, @RequestParam String cooperationMode) {
        DepositRecordVO result = channelSelfService.depositInfo(corpId, cooperationMode);
        return Response.ok(result);
    }

    @ApiOperation("申请发票")
    @OperationLog(operationName = "申请发票", operationType = OperationTypeEnum.ADD)
    @PostMapping("/deposit/applyInvoice")
    public Response applyInvoice(@RequestBody @Valid ApplyInvoiceVo applyInvoiceVo) {
        channelSelfService.applyInvoice(applyInvoiceVo);
        return Response.ok();
    }

    @PutMapping("/deposit/cancelInvoice/{chargeId}")
    @OperationLog(operationName = "申请发票取消", operationType = OperationTypeEnum.UPDATE)
    public Response cancelInvoice(@PathVariable String chargeId) {
        channelSelfService.cancelInvoice(chargeId);
        return Response.ok();
    }

    @PostMapping("/deposit/salesAppro")
    @ApiOperation(value = "申请发票销售审批接口")
    public Response<Void> salesAppro(@RequestBody @Validated InvoiceApproVo invoiceApproVo) {
        channelSelfService.salesAppro(invoiceApproVo);
        return Response.ok();
    }

    @PostMapping("/deposit/finanAppro")
    @ApiOperation(value = "申请发票财务审批接口")
    public Response<Void> finanAppro(@RequestBody @Validated InvoiceApproVo invoiceApproVo) {
        channelSelfService.finanAppro(invoiceApproVo);
        return Response.ok();
    }


    @GetMapping("/deposit/getInvoiceAuditRecord")
    @ApiOperation(value = "查询发票审核记录")
    public Response<List<CmsInvoiceAuditRecord>> getInvoiceAuditRecord(@RequestParam String procUniqueId) {
        //  查询发票审核记录
        List<CmsInvoiceAuditRecord> result = channelSelfService.getInvoiceAuditRecord(procUniqueId);
        return Response.ok(result);
    }
}
