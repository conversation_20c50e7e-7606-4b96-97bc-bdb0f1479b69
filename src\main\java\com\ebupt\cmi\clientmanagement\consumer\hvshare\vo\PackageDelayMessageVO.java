package com.ebupt.cmi.clientmanagement.consumer.hvshare.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageDelayMessageVO.java
 * @Description 套餐延迟激活消息VO类
 * @createTime 2022年03月09日 14:17:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PackageDelayMessageVO extends BaseMessageVO {

    /**
     * 激活方式 1：自动，2：手动
     */
    String activeType;

    /**
     * 是否特殊接口
     */
    boolean spec;

    String mcc;

    /**
     *  1.普通套餐 2.终端套餐
     */
    String packageType;

    String madeImsi;

}
