package com.ebupt.cmi.clientmanagement.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RedisConnectionType.java
 * @Description standalone-单节点部署方式
 *                sentinel-哨兵部署方式
 *                cluster-集群方式
 *                masterslave-主从部署方式
 * @createTime 2020年11月04日 15:27:00
 */

public enum RedisConnectionType {
    /*单节点模式*/
    STANDALONE("standalone", "单节点部署方式"),

    /*哨兵模式*/
    SENTINEL("sentinel", "哨兵部署方式"),

    /*集群模式*/
    CLUSTER("cluster", "集群方式"),

    /*主从模式*/
    MASTERSLAVE("masterslave", "主从部署方式");

    private final String connection_type;
    private final String connection_desc;

    private RedisConnectionType(String connectionType, String connectionDesc) {
        this.connection_type = connectionType;
        this.connection_desc = connectionDesc;
    }

    public String getConnection_type() {
        return connection_type;
    }

    public String getConnection_desc() {
        return connection_desc;
    }
}
