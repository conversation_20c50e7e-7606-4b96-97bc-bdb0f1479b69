package com.ebupt.cmi.clientmanagement.annotion;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DistributedLock.java
 * @Description 注解版redisson互斥锁
 * @createTime 2020年12月22日 16:44:00
 */

@Documented
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface DistributedLock {
    /**
     * 锁的名称
     */
    String keyPrefix();

    /**
     * 作为唯一id的字段
     * @return
     */
    String keySuffix() default "";

    /**
     * 如果id是直接拼在url后，默认用最后一个来
     */
    boolean keySuffixInPath() default false;

    /**
     * 倒数第几个作为id， idInPath为true必填
     * @return
     */
    int index() default 1;

    /**
     * 锁的有效时间
     */
    int leaseTime() default 20;
}
