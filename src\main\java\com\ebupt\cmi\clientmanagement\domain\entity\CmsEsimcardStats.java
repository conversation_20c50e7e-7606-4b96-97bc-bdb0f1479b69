package com.ebupt.cmi.clientmanagement.domain.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("cms_esimcard_stats")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsEsimcardStats {
    private Long id;

    private String iccid;

    private String date;

    private String type;
}
