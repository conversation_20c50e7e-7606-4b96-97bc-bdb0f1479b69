package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * UPCC签约业务类型枚举
 * <AUTHOR>
 * @date 2021-6-3 18:00:35
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum UpccSignIdEnum {

    /**
     * 非限速模板
     */
    HIGH_SPEED("1"),

    /**
     * 低速模板
     */
    LOW_SPEED("2"),

    /**
     * 限速模板
     */
    SPEED_LIMIT("3");

    private String value;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

}
