package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CmsFlowpoolInfo对象", description = "")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsFlowpoolInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流量池id")
    @TableId()
    private String flowPoolId;

    @ApiModelProperty(value = "流量池名称")
    private String flowPoolName;

    @ApiModelProperty(value = "流量池总量")
    private BigDecimal flowPoolTotal;

    @ApiModelProperty(value = "英文名")
    private String nameEn;

    @ApiModelProperty(value = "台湾名")
    private String nameTw;

    @ApiModelProperty(value = "渠道商ID")
    private String corpId;

    @ApiModelProperty(value = "渠道商名称")
    private String corpName;

    @ApiModelProperty(value = "使用状态 1、正常 2、限速 3、停用")
    private String useStatus;

    @ApiModelProperty(value = "高速签约模板")
    private String upccHignSignId;

    @ApiModelProperty(value = "低速签约模板")
    private String upccLowerSignId;

    @ApiModelProperty(value = "限速签约模板")
    private String upccLimitsSignId;

    @ApiModelProperty(value = "有效期开始")
    private Date startTime;

    @ApiModelProperty(value = "有效期结束日期")
    private Date endTime;

    @ApiModelProperty(value = "流量池价格")
    private BigDecimal flowPoolPrice;

    @ApiModelProperty(value = "流量池超出单价，单位：币种/G")
    private BigDecimal flowPoolExtraPrice;

    @ApiModelProperty(value = "提醒阈值")
    private Integer alarmThreshold;

    @ApiModelProperty(value = "审批状态 1：新建待审核 2：通过 3：新建审批不通过 4：修改待审批 5：删除待审批")
    private String authStatus;

    @ApiModelProperty(value = "流量充值额度")
    private Long recharge;

    @ApiModelProperty(value = "周期类型：1：24小时，2：自然日，3：自然月，4：自然年")
    private String cycleType;

    @ApiModelProperty(value = "周期数")
    private Long cycleNum;

    @ApiModelProperty(value = "控制逻辑 1-达量限速，2-达量停用 ,3 达量继续使用")
    private String controlLogic;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum UseStatusEnum {

        /**
         * 1、正常 2、限速 3、停用
         */
        RIGHT("1"),

        LIMIT_SPEED("2"),

        STOP_use("3");

        String status;
    }

    @AllArgsConstructor
    @Getter
    public enum AuthStatusEnum {

        NEW_NEED_APPROVAL("1"),
        /**
         * 2：通过
         */
        PASS("2"),
        /**
         * 3：不通过
         */
        NOT_PASS("3"),
        /**
         * 4：修改待审批
         */
        ALTER_NEED_APPROVAL("4"),
        /**
         * 5：删除待审批
         */
        DELETE_NEED_APPROVAL("5");

        String status;

    }

    @AllArgsConstructor
    @Getter
    public enum CurrentController {
        /**
         * 控制逻辑 1-达量限速，2-达量停用 ,3 达量继续使用
         */
        UP_LIMIT_SPEED("1"),

        UP_STOP_USE("2"),

        RIGHT("3");

        String status;
    }

    public Long getRechargeOrDefault() {
        return this.recharge == null ? 0 : this.recharge;
    }

}
