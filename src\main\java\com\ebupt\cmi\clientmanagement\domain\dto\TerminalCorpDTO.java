package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 终端厂商分页查询DTO
 * @date 2021/5/7 15:20
 */
@Data
@ToString
public class TerminalCorpDTO {

    private String corpId;

    private String corpName;

    private String appKey;

    private String appSecret;

    private String ebsCode;

    private String notifyUrl;

    private String type;

    private String checkStatus;

    private String activeNotifyType;

    private String dueNotifyType;

    private String eopCreateType;

    private String settleType;

    private String currencyCode;

    private String internalOrder;

    private String companyName;

    private String address;
}
