package com.ebupt.cmi.clientmanagement.domain.entity.flowpool;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName cmsFlowpoolConsumeErrorLog.java
 * @Description cms_flowpool_consume_error_log
 * @createTime 2022年01月13日 15:25:00
 */

@TableName("cms_flowpool_consume_error_log")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsFlowpoolConsumeErrorLog {
    Long id;

    String imsi;

    String iccid;

    String msisdn;
    /**
     * 上网方式1：H，2：V
     */
    String internetType;
    /**
     * 流量池唯一ID
     */
    String flowPoolUniqueId;
    /**
     * 业务类型
     * 1：正常 2：单卡周期达量限速 3：单卡周期达量停用 4：单卡总量达量限速 5：单卡总量达量停用 6、流量池达量限速 7：流量池达量停用
     */
    String businessType;

    /**
     * 重试次数，默认1
     */
    Integer repeatNum;
}
