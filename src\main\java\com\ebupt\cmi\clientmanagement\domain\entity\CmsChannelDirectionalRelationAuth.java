package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * (CmsChannelDirectionalRelationAuth)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-02 10:57:46
 */
@Builder
@TableName("cms_channel_directional_relation_auth")
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsChannelDirectionalRelationAuth extends BaseEntity {


    private Long id;
    //应用id
    private Long appId;
    //渠道商id
    private String corpId;
}

