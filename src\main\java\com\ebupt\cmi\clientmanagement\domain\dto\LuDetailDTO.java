package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 位置更新详情DTO
 * @date 2021/5/7 17:14
 */
@Data
@ToString
@ApiModel
public class LuDetailDTO {

    @ApiModelProperty(value = "vimsi")
    private String imsi;

    @ApiModelProperty(value = "上报时间")
    @JsonFormat(pattern = "yyyyMMdd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    @ApiModelProperty(value = "位置上报地点")
    private String location;
}
