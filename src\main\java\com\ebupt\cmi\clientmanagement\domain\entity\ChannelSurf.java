package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 上网信息表实体
 * @date 2021/4/16 16:49
 */
@TableName("cms_channel_surf_info")
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChannelSurf {

    @TableId(value = "surf_id")
    private Long id;

    private String packageUniqueId;

    private String orderUniqueId;

    @ApiModelProperty(value = "VIMSI")
    private String imsi;
    private String madeImsi;

    private String internetType;

    private String poolId;

    @ApiModelProperty(value = "mcc/位置")
    private String mcc;

    @Transient
    private transient String mccEn;

    private String himsi;


    private String corpId;

    @ApiModelProperty(value = "开始使用时间")
    @JsonFormat(pattern = "yyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private String supplierName;

    @Getter
    @AllArgsConstructor
    public enum InternetTypeEnum {

        H("1"),
        V("2");

        private String value;
    }

}
