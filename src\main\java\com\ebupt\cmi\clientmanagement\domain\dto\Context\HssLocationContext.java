package com.ebupt.cmi.clientmanagement.domain.dto.Context;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ebupt.cmi.clientmanagement.domain.enums.CardType;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.domain.hss.GETTERMSTATEType;
import com.ebupt.cmi.clientmanagement.feign.oms.OmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.oms.domain.CdrDTO;
import com.ebupt.cmi.clientmanagement.feign.oms.domain.MccAndMncDTO;
import com.ebupt.cmi.clientmanagement.feign.rms.RmsFeignClient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Struct;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/16 18:01
 */

@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HssLocationContext {
    private final SimpleDateFormat formater = new SimpleDateFormat("yyMMddHHmm");

    private Date vCardReportTime;

    private Date hCardReportTime;

    private String vCardMcc;

    private String hCardMcc;

    private OmsFeignClient omsFeignClient;

    private ControlFeignClient controlFeignClient;

    private RmsFeignClient rmsFeignClient;

    /**
     * 当前操作imsi类型
     */
    private String currentOpCardType;

    public HssLocationContext(OmsFeignClient omsFeignClient, ControlFeignClient controlFeignClient, RmsFeignClient rmsFeignClient) {
        this.omsFeignClient = omsFeignClient;
        this.controlFeignClient = controlFeignClient;
        this.rmsFeignClient = rmsFeignClient;
    }

    public Map<String, String> getLatestLocationMcc(String imsi) {
        GETTERMSTATEType gettermstateType = GETTERMSTATEType.builder()
                .location("true")
                .status("true")
                .onHLR("5")
                .detailFLAG("true")
                .imsi(imsi)
                .build();
        Response<Map<String, Object>> response = controlFeignClient.getTERMSTATE(gettermstateType);
        Map<String, String> res = new HashMap<>();
        Optional.ofNullable(response.getData())
                .map(item -> item.get("value"))
                .map(result -> ((Map<String, Object>) result).get("result"))
                .map(value -> ((Map<String, Object>) value).get("resultData"))
                .map(resultData -> ((Map<String, ArrayList<Map<String, Object>>>) resultData).get("imsiandISDNAndLOCATION"))
                .map(uselessList -> uselessList.stream()
                        .filter(e -> ((String) (e).get("name")).contains("Table1"))
                        .findFirst()
                        .map(e -> e.get("value"))
                        .map(item -> ((Map<String, Object>) item).get("item"))
                        .map(uselessList1 -> ((List<Map<String, Object>>) uselessList1).stream()
                                .filter(e -> e.containsKey("tableType1"))
                                .findFirst()
                                .map(it -> {
                                    List<Map<String, Object>> itemList = (List<Map<String, Object>>) (it.get("tableType1"));
                                    if (!CollectionUtils.isEmpty(itemList)) {
                                        for (Map<String, Object> lo : itemList) {
                                            String name = String.valueOf(lo.get("name"));
                                            if (name.contains("}LOCATION")) {
                                                res.put("LOCATION", (String) lo.get("value"));
                                            }
                                            if (name.contains("EPSLOCATION")) {
                                                res.put("EPSLOCATION", (String) lo.get("value"));
                                            }
                                            if (name.contains("LocationTimeMSC")) {
                                                res.put("LocationTimeMSC", (String) lo.get("value"));
                                            }
                                            if (name.contains("LocationTimeSGSN")) {
                                                res.put("LocationTimeSGSN", (String) lo.get("value"));
                                            }
                                            if (name.contains("LocationTimeMME")) {
                                                res.put("LocationTimeMME", (String) lo.get("value"));
                                            }
                                        }
                                        return res;
                                    }
                                    return null;
                                }).orElse(null)
                        ));

        return res;
    }

    public String getMcc(String currentOpCardType, String imsi, Map<String, String> locationInfo, Date g3ReportTime, Date g4ReportTime) {
        if (g3ReportTime.after(g4ReportTime)) {
            setReportTime(currentOpCardType, g3ReportTime);
            return getMccByGt(locationInfo);
        }

        setReportTime(currentOpCardType, g4ReportTime);
        String mcc = getMccByPlmnlist(locationInfo, imsi);
        if (StrUtil.isBlank(mcc)) {
            return getMccByGt(locationInfo);
        }

        return mcc;
    }

    private String getMccByGt(Map<String, String> locationInfo) {
        String gt = locationInfo.getOrDefault("LOCATION", null);
        MccAndMncDTO mmd = omsFeignClient.getMccByGt(gt).getData();
        return ObjectUtil.isNotNull(mmd) ? mmd.getMcc() : "";
    }

    private String getMccByPlmnlist(Map<String, String> locationInfo, String imsi) {
        String epslocation = locationInfo.get("EPSLOCATION");
        if (StrUtil.isBlank(epslocation)) {
            return "";
        }

        String mcc = null;
        String mnc = null;
        String supplierName = rmsFeignClient.getImsi2SupplierName(Collections.singletonList(imsi)).get().get(imsi).getSupplierName();
        if ("orange".equalsIgnoreCase(supplierName) || "tisparkle-tim-italy".equalsIgnoreCase(supplierName)) {
            return "";
        } else if ("bics".equalsIgnoreCase(supplierName) || "bics-telus".equalsIgnoreCase(supplierName)) {
            String plmnlist = getSpecialPlmnlist(epslocation);
            final int length = plmnlist.length();
            if (length < 4) {
                return "";
            }

            mcc = plmnlist.substring(length - 3);
            mnc = plmnlist.substring(0, length - 3);
        } else {
            for (String s : epslocation.split("\\.")) {
                if (s.toLowerCase().contains("mcc")) {
                    mcc = s.substring(3);
                } else if (s.toLowerCase().contains("mnc")) {
                    mnc = s.substring(3);
                }
            }
        }

        if ("405".equals(mcc)) {
            mcc = "404";
        } else if ("313".equals(mcc)) {
            mcc = "310";
        }

        if (StrUtil.isNotBlank(mcc) && StrUtil.isNotBlank(mnc)) {
            //查到的mnc为3位，库里的又可能是2位，所以截取后再查一次
            HashMap<String, String> map = new HashMap<>();
            String plmnlist = mcc + mnc;
            map.put(plmnlist, "");
            String compatibilityPlmnlist = mcc + mnc.substring(1);
            map.put(compatibilityPlmnlist, "");

            Map<String, CdrDTO> dto = omsFeignClient.getMccAndOperatorName(map).get();
            final String mcc1 = dto.get(plmnlist).getMcc();
            final String mcc2 = dto.get(compatibilityPlmnlist).getMcc();
            if (!"-1".equals(mcc1)) {
                return mcc1;
            } else if (!"-1".equals(mcc2)) {
                return mcc2;
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    public Date get3gReportTime(Map<String, String> locationInfo) throws ParseException {
        //3g返回时间有两个，LocationTimeSGSN优先
        String g3ReportKey = StrUtil.isNotBlank(locationInfo.get("LocationTimeSGSN")) ? "LocationTimeSGSN" : "LocationTimeMSC";
        return formater.parse(mapGetOrDefault(locationInfo.get(g3ReportKey)));
    }

    public Date get4gReportTime(Map<String, String> locationInfo) throws ParseException {
        return formater.parse(mapGetOrDefault(locationInfo.get("LocationTimeMME")));
    }

    private String mapGetOrDefault(String val) {
        if (StrUtil.isBlank(val)) {
            return "0101010101";
        } else {
            return val;
        }
    }

    private void setReportTime(String currentOpCardType, Date reportTime) {
        if (CardTypeEnum.H_CARD.getType().equals(currentOpCardType)) {
            this.hCardReportTime = reportTime;
        } else if (CardTypeEnum.V_CARD.getType().equals(currentOpCardType)) {
            this.vCardReportTime = reportTime;
        }
    }

    /**
     * 特殊逻辑返回plmnlist
     *
     * @return
     */
    private String getSpecialPlmnlist(String epslocation) {
        String[] dir = {
                ".br.epc.mnc001.mcc206.3gppnetwork.org",
                ".br.epc.mnc220.mcc302.3gppnetwork.org"
        };

        for (String s : dir) {
            if (epslocation.contains(s)) {
                final String[] split = epslocation.split("\\.");
                for (int i = 0; i < split.length; i++) {
                    if ("br".equals(split[i])) {
                        return split[i - 1];
                    }
                }
            }
        }

        return "";
    }
}
