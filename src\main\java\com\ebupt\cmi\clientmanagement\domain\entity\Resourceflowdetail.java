package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * cms_resource_flowdetail
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
@TableName("cms_resource_flowdetail")
public class Resourceflowdetail implements Serializable {
    @TableId
    private Long id;

    /**
     * ebscode
     */
    private String ebsCode;

    /**
     * 日期
     */
    private Date date;

    /**
     * Iccid
     */
    private String iccid;

    private String imsi;

    /**
     * 国家码
     */
    private String mcc;

    /**
     * 国家或地区
     */
    private String countryOrRegion;

    /**
     * 使用量（MB）
     */
    private BigDecimal usedTraffic;

    /**
     * 币种
     */
    private String currency;

    /**
     * 单价（GB）
     */
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 创建时间默认值：CURRENT_TIMESTAMP
     */
    private Date createTime;

    /**
     * 修改时间默认值：CURRENT_TIMESTAMP
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String dateStr;

    @TableField(exist = false)
    private String mccEn;

    private String dateBelongTo;

    private String operator;

    private String plmnlist;

    private BigDecimal rebateAmount;

    private Integer rebateRate;
}