package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Desc 科大讯飞登网通知配置
 * @<PERSON> h<PERSON> l<PERSON>ong
 * @Date 2021/5/17 13:01
 */
@Data
@Component
@ConfigurationProperties(prefix = "iflytek")
public class AccessNotifyConfig {

    private String accessNotifyUrl;

    private String appid;

    private String host;

    private String api;

    private String group;

    private String appSecret;

    private String password;
}
