package com.ebupt.cmi.clientmanagement.domain.dto.Context;

import com.ebupt.cmi.clientmanagement.domain.entity.Channel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/15 15:03
 */

@Data
public class ChannelContext {
    private ChannelType channelType;

    private String corpId;

    private List<String> childCorpIds;

    private List<String> relationCorpIds;

    /**********子渠道商查询可购买套餐************/

    private String currencyCode;

    private Integer packageDiscount;

    private Integer refuleDiscount;

    private Map<String, BigDecimal> packagePrice;

    private String cooperatiomMode;

    /******************************************/

    @Getter
    @AllArgsConstructor
    public enum ChannelType {
        TOP_CHANNEL,

        CHILD_CHANNEL,

        PARENT_CHANNEL
    }
}
