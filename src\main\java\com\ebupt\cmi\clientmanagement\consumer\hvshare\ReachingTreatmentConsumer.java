package com.ebupt.cmi.clientmanagement.consumer.hvshare;

import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.ContextUtil;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching.ReachingTreatmentContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.factory.HVShareStrategyFactory;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.ReachingTreatmentVO;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReachingTreatmentConsumer.java
 * @Description 达量处理消费者
 * @createTime 2022年02月28日 14:30:00
 */

@Component
@RabbitListener(queues = "singleCard.reached.queue")
@Slf4j
public class ReachingTreatmentConsumer {
    @Autowired
    HVShareStrategyFactory factory;

    @Autowired
    CommonRepository commonRepository;

    @Resource
    RedissonLock redissonLock;

    @RabbitHandler
    public void process(String reachingTreatmentMessageString, Channel channel, Message message) throws IOException {

        log.info("============================rabbitMQ收到消息{}==================================", reachingTreatmentMessageString);

        try {
            ReachingTreatmentVO reachingTreatmentVO =
                    JSON.parseObject(reachingTreatmentMessageString, ReachingTreatmentVO.class);

            if ("2".equals(reachingTreatmentVO.getCardType())) {
                return;
            }

            ReachingTreatmentContext context = (ReachingTreatmentContext) ContextUtil.getContext(ReachingTreatmentContext.class);

            //给上下文设置值
            context.setMessageVO(reachingTreatmentVO);

            context.setQueueEnum(QueueEnum.ReachingTreatment);

            context.setLogic(reachingTreatmentVO.getLogic());

            for (String appGroupId : reachingTreatmentVO.getAppGroupId()) {

                context.setAppGroupId(appGroupId);

                process(context);
            }

        } finally {

            //ack
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

        }

    }


    public void process(ReachingTreatmentContext context) {

        String name = "Reaching_" + context.getMessageVO().getPackageUniqueId();
        try {

            String logic = context.getLogic();

            AbstractStrategy strategy = null;


            //当前类型：
            //限速\释放（1：达量限速 2：达量释放）
            if ("1".equals(logic)) {

                if ("123456".equals(context.getAppGroupId())) {
                    //通用达量限速
                    strategy = factory
                            .getStrategy("reachingLimitSpeedStrategy");

                } else {
                    strategy = factory.getStrategy("dReachingLimitSpeedStrategy");
                }


            } else if ("2".equals(logic)) {
                if ("123456".equals(context.getAppGroupId())) {
                    //通用达量释放
                    strategy = factory
                            .getStrategy("reachingReleaseStrategy");
                } else {
                    strategy = factory
                            .getStrategy("dReachingReleaseStrategy");
                }

            }


            boolean lock = redissonLock.tryLock(name, 120, 300);

            //重丢队列比较好。小坑
            if (!lock) {
                throw new BizException("加锁失败");
            }

            assert strategy != null;

            strategy.handle(context);

            if (context.isNeedCallback()) {

                strategy.beforeCallBack(context);

                strategy.callBack(context);
            }


        } catch (Exception ex) {

            log.error("", ex);

            commonRepository.insertErrorLog(context, ex.getClass().getName() + " " + ex.getMessage());

        } finally {

            if (redissonLock.isHeldByCurrentThread(name)) {
                redissonLock.unlock(name);
            }
        }

    }
}
