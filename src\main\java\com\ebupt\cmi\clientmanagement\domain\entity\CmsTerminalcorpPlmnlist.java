package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

/**
 * cms_terminalcorp_plmnlist
 *
 * <AUTHOR>
@Data
@Builder
@TableName("cms_terminalcorp_plmnlist")
public class CmsTerminalcorpPlmnlist implements Serializable {

    @TableId(value = "id")
    private Long id;

    /**
     * 终端厂商corpId
     */
    private String corpId;

    /**
     * 国家码
     */
    private String mcc;

    /**
     * mnc
     */
    private String mnc;

    private static final long serialVersionUID = 1L;

    public String obtainPlmnlist() {
        return this.mcc.concat(this.mnc);
    }
}