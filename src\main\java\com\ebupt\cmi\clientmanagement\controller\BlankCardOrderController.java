package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.InvoiceInfoDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsChannelBlankcardOrder;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.validation.CommonGroup;
import com.ebupt.cmi.clientmanagement.domain.vo.BlankCardDeliverVO;
import com.ebupt.cmi.clientmanagement.domain.vo.BlankCardInvoiceVO;
import com.ebupt.cmi.clientmanagement.domain.vo.BlankCardOrderVO;
import com.ebupt.cmi.clientmanagement.service.BlankCardOrderService;
import com.ebupt.cmi.clientmanagement.service.decorator.BlankCardOrderSerivceStatusMachineDecorator;
import com.ebupt.cmi.clientmanagement.service.impl.BlankCardOrderServiceImpl;
import com.ebupt.cmi.clientmanagement.statusMachine.BlankCardOrderStatusMachine;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4 14:03
 */

@RestController
@RequestMapping("/blankCardOrder")
@AllArgsConstructor
public class BlankCardOrderController {
    private final BlankCardOrderSerivceStatusMachineDecorator blankCardOrderSerivceDecorator;

    @GetMapping("/getOrder")
    public Response<List<CmsChannelBlankcardOrder>> getOrder(@Validated BlankCardOrderVO vo) {
        return Response.ok(blankCardOrderSerivceDecorator.getOrder(vo));
    }

    @PutMapping("/comfirmOrder/{id}")
    @OperationLog(operationName = "渠道商白卡订单——确认订单", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> comfirmOrder(@PathVariable("id") Long id) {
        blankCardOrderSerivceDecorator.comfirmOrder(id);
        return Response.ok();
    }

    @PutMapping("/cancelOrder/{id}")
    @OperationLog(operationName = "渠道商白卡订单——关闭订单", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> cancelOrder(@PathVariable Long id) {
        blankCardOrderSerivceDecorator.cancelOrder(id, BlankCardOrderStatusMachine.BlankCardOrderRole.ADMIN);
        return Response.ok();
    }

    @GetMapping("/download")
    public void downloadfile(HttpServletResponse response, @RequestParam Long id, @RequestParam String type) {
        blankCardOrderSerivceDecorator.downloadFile(response, id, type);
    }

    @PutMapping("/deliver")
    @OperationLog(operationName = "渠道商白卡订单——订单发货", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> deliver(BlankCardDeliverVO vo) {
        blankCardOrderSerivceDecorator.deliver(vo);
        return Response.ok();
    }

    @PostMapping("/generateInvoice")
    @OperationLog(operationName = "渠道商白卡订单——生成发票", operationType = OperationTypeEnum.UPDATE)
    public Response<Void> generateInvoice(@RequestBody BlankCardInvoiceVO vo) {
        blankCardOrderSerivceDecorator.generateInvoice(vo);
        return Response.ok();
    }

    @GetMapping("/getInvoiceInfo")
    public Response<InvoiceInfoDTO> generateInvoice(@RequestParam String orderId) {
        return Response.ok(blankCardOrderSerivceDecorator.getInvoiceDTO(orderId));
    }
}
