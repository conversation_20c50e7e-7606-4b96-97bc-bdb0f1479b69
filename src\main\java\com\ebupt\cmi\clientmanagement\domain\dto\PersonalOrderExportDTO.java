package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/15 16:02
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class PersonalOrderExportDTO {
    //商品名称,订单编号,订购类型,订单状态,订单生成时间," +
    //                    "订单更新时间,物流编号,ICCID,用户名称,币种,金额,购买数量

    private String orderName;

    private String orderId;

    /**
     * 订购类型：
     * 1：卡
     * 2：套餐
     * 3：卡+套餐
     * 4：终端线下卡池
     * 5：流量池
     */
    private String orderType;

    /**
     * 订单状态
     * 1、待发货
     * 2、已完成
     * 3、已退订
     * 4、激活退订待审批
     * 5：部分退订
     * 6：部分发货
     * 7：已回收
     * 8：部分回收
     * 9：复合状态
     */
    private String orderStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private String logistic;

    private String iccid;

    private String user;

    private String currencyCode;

    private BigDecimal amount;

    private Integer count;

    private String thirdOrderId;

    private String phoneNumber;

    private String addressee;

    private String address;

    private String logisticCompany;

    private String orderUserName;

    private String nameEn;

    private String orderChannel;

    private String orderUniqueId;

    private String postCode;

}
