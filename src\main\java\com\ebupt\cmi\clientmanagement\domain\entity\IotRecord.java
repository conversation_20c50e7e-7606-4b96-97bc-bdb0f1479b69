package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

@TableName("cms_iot_record")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IotRecord {
    private Long id;

    private String date;

    private String postback;
}
