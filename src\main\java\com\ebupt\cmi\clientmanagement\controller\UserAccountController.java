package com.ebupt.cmi.clientmanagement.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsUserAccount;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.mapper.UserAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/userAccount")
@AllArgsConstructor
@Api(tags = "用户账户相关接口")
public class UserAccountController {

    private final UserAccountService userAccountService;

    @ApiOperation("根据MSISDN查询个人用户是否存在")
    @GetMapping("/checkExists/{msisdn}")
    public Response<Boolean> checkExistsByMsisdn(@PathVariable String msisdn) {
        int count = userAccountService.count(Wrappers.<CmsUserAccount>lambdaQuery().eq(CmsUserAccount::getMsisdn,
                msisdn));
        return Response.ok(count > 0);
    }

    @GetMapping("/checkExistsByEmail/{email}")
    public Response<Boolean> checkExistsByEmail(@PathVariable(name = "email") String email) {
        int count = userAccountService.count(Wrappers.<CmsUserAccount>lambdaQuery().eq(CmsUserAccount::getEmail,
                email));
        return Response.ok(count > 0);
    }

    @GetMapping("/getUser")
    public Response<CmsUserAccount> getUser(@RequestParam Integer roleId, @RequestParam String id) {
        if (roleId == 104) {
            return Response.ok(userAccountService.getOne(Wrappers.lambdaQuery(CmsUserAccount.class)
                    .eq(CmsUserAccount::getMsisdn, id)));
        }
        if (roleId == 107) {
            return Response.ok(userAccountService.getOne(Wrappers.lambdaQuery(CmsUserAccount.class)
                    .eq(CmsUserAccount::getEmail, id)));
        }
        return Response.ok();
    }
}
