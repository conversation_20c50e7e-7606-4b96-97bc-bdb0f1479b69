package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OperatorTypeEnum
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>n
 * @Date: 2021/5/12 10:19
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum {
    /**
     * 查询
     */
   SELECT(0),
    /**
     * 新增
     */
    INSERT(1),

    /**
     * 删除
     */
    DELETE(2),

    /**
     * 修改信息
     */
    UPDATE(3);
    private Integer type;
}
