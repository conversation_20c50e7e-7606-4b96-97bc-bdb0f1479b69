package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 流量池批量导入
 */
@Data
@TableName("cms_flowpool_iccidimport_task")
public class FlowpoolIccidimportTask{

    private Long id;

    private String flowPoolId;

    private Integer importCount;

    private Integer successCount;

    private Integer failCount;

    private String successFilePath;

    private String failFilePath;

    private String taskStatus;

    private String fileName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
