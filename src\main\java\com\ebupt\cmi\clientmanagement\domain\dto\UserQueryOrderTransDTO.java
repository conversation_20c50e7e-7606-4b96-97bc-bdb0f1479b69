package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.dto.common.Logistic;
import com.ebupt.cmi.clientmanagement.domain.dto.common.OrderItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 数据库直查
 * @date 2021/5/26 16:24
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserQueryOrderTransDTO {

    private String orderId;

    private String createUserId;

    /**
     * 订单状态枚举值:
     * 1、待发货
     * 2、已发货
     * 3、部分退订
     * 4、全额退订
     * 作为入参时，无效
     * 作为响应时，查到则必填
     */
    private String status;

    private BigDecimal totalAmount;

    private String currency;

    private String channelId;

    private Date createTime;

    private String comments;

    /**
     * 联系人号码
     */
    private String phoneNumber;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 国家名称|省份|城市名称|邮寄地址
     */
    private String address;
    /**
     * 收件人
     */
    private String addressee;

    /**
     * 物流单号
     */
    private String logisticsOrderId;
    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 子订单id
     */
    private String orderItemId;

    /**
     * 子订单创建时间
     */
    private String orderCreateTime;

    private String packageId;

    private String price;
}
