package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 审核状态枚举
 * <AUTHOR>
 * @date 2021-5-25 10:45:51
 */
@Slf4j
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum CdrConfigEnum {

    ARCH("1","ARCH"),

    GTP_Proxy("2","GTP-Proxy");

    private final String value;
    private final String desc;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

    public static String getDesc(String value) {
        for (CdrConfigEnum p : CdrConfigEnum.values()) {
            if (p.getValue().equals(value)) {
                return p.getDesc();
            }
        }
        log.info("输入不符合要求：{}", value);
        return "";
    }
}
