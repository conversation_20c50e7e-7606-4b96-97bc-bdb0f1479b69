package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.math.BigDecimal;

@Data
@ToString
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowPoolPackageDTO {
    /**
     * 流量池当前限速类型
     * 1：正常
     * 2：单卡周期达量限速
     * 3：单卡周期达量停用
     * 4：单卡总量达量限速
     * 5：单卡总量达量停用
     * 6：流量池上限限速
     * 7：流量池上限达量
     */
    private String currentRateType;
    /**
     * 流量池单周期总量，MB
     */
    private BigDecimal dailyTotal;
    /**
     * 流量池总量，MB
     */
    private BigDecimal total;
    /**
     * 流量池限速类型
     * 1：达量继续使用
     * 2：达量限速
     * 3：达量停用
     */
    private String rateType;
}
