package com.ebupt.cmi.clientmanagement.aspect;

import com.ebupt.cmi.clientmanagement.annotion.SdcMultipleQueues;
import com.ebupt.cmi.clientmanagement.domain.enums.DisasterEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Random;

@Component
@Aspect
@Slf4j
public class SdcMultipleQueuesAspect {

    @Resource
    private HttpServletRequest httpServletRequest;

    /*// @Before("@annotation(sdcMultipleQueues)")
    public void beforeExecute(SdcMultipleQueues sdcMultipleQueues) {
        try {
            DisasterEnum disasterEnum = sdcMultipleQueues.operationName();
            String value = disasterEnum.getValue();
            if (value.length() != 1) {
                String[] split = value.split(",");
                Random random = new Random();
                int num = random.nextInt(split.length);
                value = split[num];
            }
            httpServletRequest.setAttribute("entityid", value);
        } catch (Exception e) {
            log.warn("sdc多队列异常，请排查问题");
        }
    }*/

}
