package com.ebupt.cmi.clientmanagement.domain.entity;

/**
 * ChannelChargeRecord
 * 充值记录表
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/6/16 14:52
 */

import com.baomidou.mybatisplus.annotation.TableName;
import com.ebupt.cmi.clientmanagement.handler.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("cms_channel_charge_record")
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelChargeRecord {


    private Long id;

    private String corpId;

    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal amount;

    private String currencyCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime chargeTime;

    private String code;

    /**
     * 充值类型
     * 1、缴付账单
     * 2、增加押金
     * 3、增加预存款
     * 4、酬金返还
     * 5、渠道商收入金额调账
     * 6、A2Z押金充值
     * 7、A2Z账单缴付
     * 8、营销返利-代销
     * 9、营销返利-A2Z
     * 10、A~Z预存款充值
     * 14、赔付
     */
    private String chargeType;

    private String ebsCode;

}
