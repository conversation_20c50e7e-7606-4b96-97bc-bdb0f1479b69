package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.cooperation.DeriveDTO;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.cooperation.CooperationVO;
import com.ebupt.cmi.clientmanagement.domain.vo.cooperation.CooperationVOForUpdate;
import com.ebupt.cmi.clientmanagement.service.CooperationService;
import com.ebupt.cmi.clientmanagement.utils.CsvExportUtil;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CooperationController.java
 * @Description 合作商增删改查你懂的接口
 * @createTime 2021年05月07日 19:26:00
 */

@RestController
@RequestMapping("/cooperation")
@Api(tags = "合作商接口")
@Slf4j
public class CooperationController {
    @Autowired
    CooperationService cooperationService;

    @ApiOperation(value = "新建合作商", notes = "保存[客户信息表]、[能力接入详情表]、[扣费规则详情表]、[结算规则详情表]")
    @OperationLog(operationName = "合作商管理-合作商新建", operationType = OperationTypeEnum.ADD)
    @PostMapping
    @NormalLog
    public Response addNewCooperation(@RequestBody @Valid CooperationVO cooperationVO) {

        Boolean example = (cooperationVO.getBillRuleDetail() == null || cooperationVO.getBillRuleDetail().size() == 0)
                && (cooperationVO.getSettleRuleDetails() == null || cooperationVO.getSettleRuleDetails().size() == 0);
        if (example) {
            return Response.error("billruledetail和settleruledetail不能同时为空");
        }

        cooperationService.addNewCooperation(cooperationVO);

        return Response.ok();
    }

    @ApiOperation(value = "修改运营商信息")
    @PostMapping("/updateCooperation")
    @OperationLog(operationName = "合作商管理-合作商修改", operationType = OperationTypeEnum.UPDATE)
    @NormalLog
    public Response updateCooperation(@RequestBody @Valid CooperationVOForUpdate cooperationVOForUpdate) {

        Boolean example = (cooperationVOForUpdate.getBillRuleDetail() == null || cooperationVOForUpdate.getBillRuleDetail().size() == 0)
                && (cooperationVOForUpdate.getSettleRuleDetails() == null || cooperationVOForUpdate.getSettleRuleDetails().size() == 0);
        if (example) {
            return Response.error("billruledetail和settleruledetail不能同时为空");
        }

        cooperationService.updateCooperation(cooperationVOForUpdate);

        return Response.ok();

    }

    @ApiOperation(value = "获取合作商列表", notes = "从[客户信息表]、[能力接入详情表]")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageNumber", value = "页数", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "pageSize", value = "页大小", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "corpName", value = "厂商名称", paramType = "query", dataType = "String", required = true)
    }
    )
    @GetMapping
    @NormalLog
    public Response getCooperations(@RequestParam Integer pageNumber,
                                    @RequestParam Integer pageSize,
                                    @RequestParam String corpName) {
        if (pageNumber <= 0 || pageSize <= 0) {
            return Response.error("页码或页数量不能小于等于零");
        } else {
            return Response.ok(cooperationService.getCooperation(pageNumber, pageSize, corpName));
        }
    }

    @ApiOperation(value = "获取合作商资费详情", notes = "[扣费规则详情表]、[结算规则详情表]")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "corpId", value = "厂商Id", paramType = "query", dataType = "String", required = true)
    }
    )
    @GetMapping("/getCooperationMoney")
    @NormalLog
    public Response getCooperationsMoney(@RequestParam String corpId) {
        return Response.ok(cooperationService.getCooperationsMoney(corpId));
    }

    @ApiOperation(value = "获取合作商详细信息", notes = "从[套餐与卡关系表]")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageNumber", value = "页数", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "pageSize", value = "页大小", paramType = "query", dataType = "int", required = true),
            @ApiImplicitParam(name = "corpId", value = "厂商id", paramType = "query", dataType = "String", required = true)
    }
    )
    @GetMapping("detail")
    @NormalLog
    public Response getCooperationsDetail(@RequestParam Long pageNumber,
                                          @RequestParam Long pageSize,
                                          @RequestParam String corpId) {
        if (pageNumber <= 0 || pageSize <= 0) {
            return Response.error("页码或页数量不能小于等于零");
        } else {
            return Response.ok(cooperationService.getCooperationDetail(pageNumber, pageSize, corpId));
        }

    }

    @ApiOperation(value = "导出使用明细")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "corpId", value = "厂商id", paramType = "query", dataType = "String", required = true)
    }
    )
    @GetMapping("derive")
    @NormalLog
    public void deriveCooperationsDetail(@RequestParam String corpId, HttpServletResponse response) {
        try {
            List<DeriveDTO> result = cooperationService.deriveCooperationsDetail(corpId);

            // 构造导出数据结构

            // 设置表头
            String titles = "ICCID,套餐名称,激活时间,到期时间,激活国家/地区,金额,币种";

            // 设置每列字段
            String keys = "iccid,packageName,activeTime,expireTime,mcc,amount,currencyCode";

            // 构造导出数据
            List<Map<String, Object>> datas = new ArrayList<>();

            Map<String, Object> map;

            if (result != null) {
                for (DeriveDTO data : result) {
                    map = new HashMap<>();
                    map.put("iccid", data.getIccid());
                    map.put("packageName", data.getPackageName());
                    map.put("activeTime", data.getActiveTime());
                    map.put("expireTime", data.getExpireTime());
                    map.put("mcc", data.getMcc());
                    map.put("amount", data.getAmount());
                    map.put("currencyCode", data.getCurrencyCode());
                    datas.add(map);
                }
            }


            // 设置导出文件前缀
            String fName = "使用明细_";

            // 文件导出
            OutputStream os = response.getOutputStream();
            CsvExportUtil.responseSetProperties(fName, response);
            CsvExportUtil.doExport(datas, titles, keys, os);
            os.close();
        } catch (Exception e) {
            log.warn("导出失败" + e.getMessage(), e);
        }
    }

    @ApiOperation(value = "审批修改")
    @OperationLog(operationName = "合作商管理-合作商审批", operationType = OperationTypeEnum.AUDIT)
    @PutMapping
    @NormalLog
    public Response changeCheckStatus(@RequestParam(required = false) String corpId, @RequestParam(required = false) String toCheackStatus,
                                      @RequestParam(required = false) String originCheackStatus, @RequestParam(required = false) Boolean available) {

        cooperationService.changeCheckStatus(corpId, toCheackStatus, originCheackStatus, available);

        return Response.ok();

    }

    @ApiOperation(value = "删除运营商")
    @OperationLog(operationName = "合作商管理-合作商删除", operationType = OperationTypeEnum.DELETE)
    @DeleteMapping
    @NormalLog
    public Response deleteCooperation(@RequestBody String[] ids) {

        if (ids == null || ids.length == 0) {
            return Response.error("System failed, please contact the system manager.");
        }

        cooperationService.deleteCooperation(ids);

        return Response.ok();

    }

}