package com.ebupt.cmi.clientmanagement.domain.enums.lu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/4 13:49
 */
@Getter
@AllArgsConstructor
public enum UpccSignIdEnum {

    /**
     * 普通套餐高速/流量池高速签约模板
     */
    HIGH_SPEED("1"),

    /**
     * 普通套餐低速/流量池低速签约模板
     */
    LOW_SPEED("2"),

    /**
     * 普通套餐/流量池限速签约模板
     */
    LIMIT_SPEED("3");

    private String upccSignId;

}
