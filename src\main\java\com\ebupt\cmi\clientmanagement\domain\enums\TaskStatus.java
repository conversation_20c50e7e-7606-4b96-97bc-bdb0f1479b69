package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 任务状态
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/4/6 13:19
 */
@Getter
@AllArgsConstructor
public enum TaskStatus {


    IN_PROCESS("1", "处理中"),

    COMPLETE("2", "完成"),

    ROLLING_BACK("4", "回滚中"),

    ROLLED_BACK("5", "已回滚"),

    CAN_ROLLBACK("6", "可回滚");

    private String k;

    private String val;

}
