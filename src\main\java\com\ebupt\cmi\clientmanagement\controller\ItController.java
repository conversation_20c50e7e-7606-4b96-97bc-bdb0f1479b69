package com.ebupt.cmi.clientmanagement.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebupt.cmi.clientmanagement.domain.dto.PmsChannelCardDTO;
import com.ebupt.cmi.clientmanagement.domain.response.APIResponse;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.AccessNotify;
import com.ebupt.cmi.clientmanagement.domain.vo.DeductionVO;
import com.ebupt.cmi.clientmanagement.domain.vo.hss.OpenAccount;
import com.ebupt.cmi.clientmanagement.service.HssService;
import com.ebupt.cmi.clientmanagement.service.scheduled.ScheduledService;
import com.ebupt.cmi.clientmanagement.utils.RestClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * @Desc hss相关操作
 * <AUTHOR> lingsong
 * @Date 2021/4/15 10:41
 */
@Slf4j
@RestController
@RequestMapping
@RefreshScope
@Api(tags = "IT域相关接口")
public class ItController {

    @Autowired
    private HssService hssService;

    @Resource
    private ScheduledService scheduledService;

    @Value("${feign.clients.deduction}")
    private String deductionUrl;

    @PostMapping("/it/openAccount")
    @ApiOperation(value = "hss鉴权、开户", notes = "hss鉴权、开户")
    public Response openAccount(@RequestBody @Valid OpenAccount openAccount) throws InterruptedException {
        log.info("HSS开户：{}", openAccount);
        return hssService.openAccount(openAccount);
    }

    @PostMapping("/it/closeAccount")
    @ApiOperation(value = "hss动态销户", notes = "剔除有激活中、已激活套餐得卡，返回给pms")
    public Response<List<PmsChannelCardDTO>> closeAccount(@RequestBody @Valid List<PmsChannelCardDTO> card)  {
        log.info("HSS动态销户筛选没有已激活/激活中得卡");
        List<PmsChannelCardDTO> pmsChannelCardDTOS = scheduledService.closeAccount(card);
        return Response.ok(pmsChannelCardDTOS);
    }



    @PostMapping("/aep/accessNotify/v1")
    @ApiOperation("科大讯飞登网通知")
    public Response<Void> accessNotify(@RequestBody @Validated AccessNotify notify) throws IOException {
        return hssService.accessNotify(notify,null) ? new Response<>("0", "通知成功") : new Response("1000", "通知失败");
    }

    @PostMapping("/aep/deduction/v1")
    @ApiOperation("扣费接口")
    public APIResponse deduction(@RequestBody @Validated DeductionVO deductionVO) {
        String jsonParams = JSON.toJSONString(deductionVO);
        String response = RestClientUtil.postJson(deductionUrl, jsonParams);
        JSONObject object = JSONObject.parseObject(response);
        return APIResponse.ok(object.get("code") + "", object.get("description") + "");
    }

}
