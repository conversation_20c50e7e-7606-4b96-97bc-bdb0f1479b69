package com.ebupt.cmi.clientmanagement.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 已购买套餐DTO
 * @date 2021/5/12 14:34
 */
@Data
@ToString
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PurchasedPackageDTO {

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "iccid")
    private String iccid;

    @ApiModelProperty(value = "套餐名称En")
    private String packageNameEn;

    @ApiModelProperty(value = "套餐id")
    private String packageId;

    @ApiModelProperty(value = "套餐使用状态")
    private String packageStatus;

    @ApiModelProperty(value = "套餐使用状态")
    private String packageType;

    @ApiModelProperty(value = "激活类型 1H 2V")
    private String activeCategory;

    private String orderId;

    @JsonProperty(value = "orderNumber")
    private String orderUniqueId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty(value = "orderTime")
    @ApiModelProperty(value = "订购时间")
    private Date createTime;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal subAmount;

    @ApiModelProperty(value = "币种156CNY 840USD 344HKD")
    private String currencyCode;

    @ApiModelProperty(value = "订购渠道")
    private String orderChannel;

    @ApiModelProperty(value = "厂商id")
    private String corpId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "激活时间")
    private Date activeTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "套餐有效期")
    private Date effectiveDay;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "上报时间")
    private Date reportTime;

    @ApiModelProperty(value = "最新位置")
    private String currentLocation;

    @ApiModelProperty(value = "最新位置")
    private String currentLocationEn;

    @ApiModelProperty(value = "套餐唯一id")
    private String packageUniqueId;

    @ApiModelProperty(value = "套餐周期类型1小时2日3月4年")
    private String periodUnit;

    @ApiModelProperty(value = "套餐持续周期")
    private String keepPeriod;

    @ApiModelProperty(value = "流量限制类型  1:周期内限量  2:单日限量")
    private String flowLimitType;

    @ApiModelProperty(value = "上网状态")
    private String surfStatus;

    @ApiModelProperty(value = "流量上限")
    private BigDecimal flowLimitSum;

    @ApiModelProperty(value = "流量上限展示字段")
    private String flowLimitSumStr;

    @ApiModelProperty(value = "达量控制逻辑 1：达量限速 2：达量释放")
    private String controlLogic;

    @TableField(exist = false)
    @ApiModelProperty(value = "已使用流量")
    private String usedFlowBytes;


    @TableField(exist = false)
    @ApiModelProperty(value = "今日已使用流量")
    private String todayUsedFlow;


    @TableField(exist = false)
    @ApiModelProperty(value = "是否支持加油包")
    private String supportRefuel;

    @TableField(exist = false)
    @ApiModelProperty(value = "套餐剩余天数")
    private Integer daysRemaining;

    @ApiModelProperty(value = "激活方式")
    private Integer activationMode;

    @ApiModelProperty(value = "指定激活时间")
    @JsonFormat(pattern = "yyyy/MM/dd",timezone="GMT+8")
    private Date activeAt;

    @ApiModelProperty(value = "加油包充值流量")
    private String refuelRechargeFlow;


    @ApiModelProperty(value = "加油包已用流量")
    private String refuelUsedFlow;


    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "退订前套餐状态")
    private String orderPackageStatus;

    @ApiModelProperty(value = "是否是当前使用的套餐")
    private Boolean isUsing;

    @ApiModelProperty(value = "运营商名称")
    private String operatorName;

    @ApiModelProperty(value = "apnEn")
    private String apnEn;

    @ApiModelProperty(value = "apnZh")
    private String apnZh;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date packageCreateTime;

    private String upccRate;

    private String supportHotspot;

    private String unit;

    @ApiModelProperty(value = "套餐持续周期")
    private int remainderCycle;

    @AllArgsConstructor
    @Getter
    public enum PackageType{

        /**
         *套餐类型： 1：套餐，2：终端线下卡池，3：流量池，4：加油包
         */
        PACKAGE("1"),

        TERMINAL_OFFLINE_CARD_POOL("2"),

        FLOW_POOL("3"),

        REFUEL_PACK("4");

        String value;

    }
    @Getter
    @AllArgsConstructor
    public enum flowLimitTypeEnum {

        CYCLE_LIMIT("1"),
        DAY_LIMIT("2");

        private String value;
    }

}
