package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 客户与卡关系表实体
 * @date 2021/4/16 14:03
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_card")
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelCard extends BaseEntity {


    private Long id;

    private String corpId;

    private String imsi;

    private String msisdn;

    private String iccid;

    /**
     * 卡类型: 1:H  2:V
     */
    private String cardType;


    /**
     * 卡池ID 当Card_type=2时不为空
     */
    private String poolId;

    private String lastVimsi;

    private Date lastReportTime;

    /**
     * 卡形态 1普通 2esim 3贴片卡
     */
    private String cardForm;

    /**
     *  套餐唯一ID
     */
    private String packageUniqueId;

    /**
     * 状态，默认值2（DB默认）
     * 1：已分配
     * 2：待分配
     */
    private String status;

    /**
     * 流量池单周期总量，MB
     */
    private BigDecimal dailyTotal;

    /**
     * 流量池总量，MB
     */
    private BigDecimal total;

    /**
     * 流量池限速类型
     * 1：达量继续使用
     * 2：达量限速
     * 3：达量停用
     */
    private String rateType;

    /**
     * 流量池当前限速类型
     * 1：正常
     * 2：单卡周期达量限速
     * 3：单卡周期达量停用
     * 4：单卡总量达量限速
     * 5：单卡总量达量停用
     * 6：流量池上限限速
     * 7：流量池上限达量
     */
    private String currentRateType;

    /**
     * 流量池ID
     */
    private String flowPoolId;

    /**
     * 入流量池时间
     */
    private Date intoPoolTime;

    /**
     *流量池卡状态，默认值1（DB默认）1：正常 2：暂停
     */
    private String flowPoolStatus;

    /**
     *入池可用时长
     */
    private Integer availableTime;

    /**
     *卡备注
     */
    private String cardRemark;

    /**
     * 订单批次
     */
    private String orderBatch;

    /**
     * Esim卡二维码是否保存：
     * 1.是
     * 2/空.否
     */
    private String qrCodeFlag;

    /**
     * 合作模式
     * 1：代销
     * 2：A2Z
     */
    private String cooperationMode;

    private String haveUsePackage;

    private Long groupId;

    private Long ruleId;

    private Long freeimsiId;

    @TableField(exist = false)
    private String corpName;



    @AllArgsConstructor
    public enum QrCodeSaveEnum {

        /**
         * 1：已保存
         */
        HAS_SAVE("1"),

        /**
         * 3:临时状态
         */
        TEMP_SAVE("3");

        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }

    @AllArgsConstructor
    public enum StatusEnum {

        /**
         * 1：已分配
         */
        ASSIGNED("1"),

        /**
         * 2：待分配
         */
        UNASSIGNED("2");

        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }

    @AllArgsConstructor
    public enum rateTypeEnum {

        /**
         * 1：达量继续使用
         */
        CONTINUE_TO_USE("1"),

        /**
         * 2：达量限速
         */
        SPEED_LIMIT("2"),

        /**
         * 3：达量停用
         */
        DEACTIVATION("3");


        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }


    @AllArgsConstructor
    public enum FlowPoolStatusEnum {

        /**
         * 1：正常
         */
        NORMAL("1"),

        /**
         * 2：暂停
         */
        PAUSE("2");

        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }

    @AllArgsConstructor
    public enum CurrentRateTypeEnum {

        /**
         * 1：正常
         */
        NORMAL("1"),

        /**
         * 2：单卡周期达量限速
         */
        CARD_SPEED_LIMIT("2"),

        /**
         * 3：单卡周期达量停用
         */
        CARD_STOP("3"),

        /**
         * 4：单卡总量达量限速
         */
        CARD_TOTAL_SPEED_LIMIT("4"),

        /**
         * 5：单卡总量达量停用
         */
        CARD_TOTAL_STOP("5"),

        /**
         * 6 : 流量池总量达量限速
         */
        FLOW_POOL_SPEED_LIMIT("6"),

        /**
         * 7：流量池总量达量停用
         */
        FLOW_POOL_STOP("7");

        @Getter
        @Setter(AccessLevel.PRIVATE)
        private String value;

    }
}
