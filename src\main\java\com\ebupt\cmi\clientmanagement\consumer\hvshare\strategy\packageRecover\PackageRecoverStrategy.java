package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.packageRecover;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packageSuspendContext.SuspendAndRecoverContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.consumer.uitils.LuWarpper;
import com.ebupt.cmi.clientmanagement.consumer.vo.MockLuVO;
import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.enums.RoleEnum;
import com.ebupt.cmi.clientmanagement.domain.vo.HimsiStatusAndLocationVO;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.service.PackageEndService;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component("packageRecoverStrategy")
@Slf4j
public class PackageRecoverStrategy extends AbstractOutsideNetStrategy {

    @Resource
    private CCRCommonService ccrCommonService;

    @Resource
    private ControlFeignClient controlFeignClient;

    @Resource
    private PackageEndService packageEndService;

    @Resource
    private LuWarpper luWarpper;

    @Resource
    private RedisUtil<Long> redisUtil;

    @Override
    protected <T extends BaseContext> boolean tryOutsideNet(T context) {

        SuspendAndRecoverContext suspendContext = (SuspendAndRecoverContext) context;

        HcardInfo hcardInfo = context.getHcardInfo();

        if (!suspendContext.isNeedDeal()) {

            return true;

        }

        if (hcardInfo.isNewCard()) {

            if (!ccrCommonService.judgePgwSessionExists(hcardInfo.getImsi())) {

                log.info("[启用队列]新卡进行恢复流程,会话不存在，流程结束");

                return true;

            }

            controlFeignClient.sendRAR(hcardInfo.getImsi());

        } else {

            HimsiStatusAndLocationDTO himsiStatusAndLocationDTO;
            try {

                himsiStatusAndLocationDTO = packageEndService.getUserLocation(HimsiStatusAndLocationVO.builder()
                        .imsi(hcardInfo.getImsi())
                        .role(RoleEnum.PHONENUMBER.getRole())
                        .build());

            } catch (Exception e) {

                log.error("位置查询失败， 原因为：", e);

                return false;
            }

            if (himsiStatusAndLocationDTO == null || StringUtils.isEmpty(himsiStatusAndLocationDTO.getMobileCountryCode())){

                log.error("位置查询失败，没有查到当前位置");

                return false;
            }

            luWarpper.mockLu(MockLuVO.builder()
                    .activeType("1")
                    .cardForm(hcardInfo.getCardForm())
                    .cardType("1")
                    .himsi(hcardInfo.getImsi())
                    .mcc(himsiStatusAndLocationDTO.getMobileCountryCode())
                    .iccid(hcardInfo.getIccid())
                    .msisdn(hcardInfo.getMsisdn())
                    .imsi(hcardInfo.getImsi())
                    .build());

        }

        return true;

    }

    @Override
    public <T extends BaseContext> void handle(T context) throws InterruptedException {

        String packageUniqueIDFromMessage = context.getMessageVO().getPackageUniqueId();

        ChannelPackageCard channelPackageCard = hvShareRepository.getChannelPackageCardByPackageUniqueId(packageUniqueIDFromMessage);

        SuspendAndRecoverContext suspendAndRecoverContext = (SuspendAndRecoverContext) context;

        if (!"2".equals(channelPackageCard.getPackageUseStatus())) {

            log.info("此套餐状态不为停用，流程结束");

            suspendAndRecoverContext.setNeedDeal(false);

            return;
        }

        //先扣减redis，保证即使处理失败了，也让渠道商状态能有所改变。
        Long messageNumber = redisUtil.decr(String.format(BizConstants.CHANNEL_RECOVER_KEY, suspendAndRecoverContext.getCropId()), 1);

        if (messageNumber <= 0) {

            log.info("最后一条消息，更改渠道商状态为正常");

            hvShareRepository.updateChannelStatus(suspendAndRecoverContext.getCropId(), "1");

            redisUtil.del(String.format(BizConstants.CHANNEL_RECOVER_KEY, suspendAndRecoverContext.getCropId()));

        }

        setHcardIntoContext(context);

        hvShareRepository.updateChannelPackageCardUseStatus(packageUniqueIDFromMessage, "1");

    }
}
