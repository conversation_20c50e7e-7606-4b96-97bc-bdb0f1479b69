package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsChannelAuth对象", description="")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsChannelAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "UUID,厂商ID或者个人用户id")
    @TableId(value = "Corp_id")
    private String corpId;

    @ApiModelProperty(value = "厂商名称 ")
    private String corpName;

    @ApiModelProperty(value = "厂商状态 1：正常 2：暂停 3：删除(暂不用)")
    private String status;

    @ApiModelProperty(value = "类别：1：渠道商 3：合作商 4：后付费 5：测试渠道 6：个人用户 7：终端线上 8：终端线下 9：能力渠道商 10:流量池 11：线下售卡")
    @TableField(value = "Type")
    private String Type;

    @ApiModelProperty(value = "扣费类型：1：资费编码+二次定价 2：流量方向 4：折扣")
    @TableField("Bill_type")
    private String billType;

    @ApiModelProperty(value = "EBSCode")
    @TableField("EBSCode")
    private String EBSCode;

    @ApiModelProperty(value = "币种编码：156 人民币 840 美元 344 港币")
    private String currencyCode;

    @ApiModelProperty(value = "扣费规则，uuid")
    @TableField("Bill_rule")
    private String billRule;

    @ApiModelProperty(value = "结算类型：1：资费编码 2：流量方向")
    @TableField("Settle_type")
    private String settleType;

    @ApiModelProperty(value = "结算规则，uuid")
    @TableField("Settle_rule")
    private String settleRule;

    @ApiModelProperty(value = "审批状态 1：新建待审批 2：通过 3：不通过 4：删除待审批5：修改待审批")
    private String checkStatus;

    private String companyName;

    private String internalOrder;

    private String address;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
