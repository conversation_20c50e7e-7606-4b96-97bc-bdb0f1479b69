package com.ebupt.cmi.clientmanagement.consumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MockLuVO.java
 * @Description 模拟LU的抽象VO
 * @createTime 2022年01月17日 15:51:00
 */

@ApiModel
@Data
@ToString
@Builder
public class MockLuVO {
    private String imsi;
    //mcc
    private String mcc;

    private String iccid;

    private String msisdn;

    /**
     * 上报类型
     * 1：H
     * 2：V
     */
    private String cardType;


    private String himsi;

    private String cardForm;

    private String packageUniqId;

    private String activeType;
}
