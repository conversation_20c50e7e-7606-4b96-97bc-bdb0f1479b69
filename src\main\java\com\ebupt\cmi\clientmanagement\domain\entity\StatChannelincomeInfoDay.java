package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="StatChannelincomeInfoDay对象", description="")
public class StatChannelincomeInfoDay implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "统计日：yyyymmdd")
    private String statTime;

    @ApiModelProperty(value = "客户id")
    private String corpId;

    @ApiModelProperty(value = "客户名称")
    private String corpName;

    @ApiModelProperty(value = "客户EBScode")
    private String ebscode;

    @ApiModelProperty(value = "比种编码")
    private String currency;

    @ApiModelProperty(value = "订单总额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "退订订单总额")
    private BigDecimal unsubscribeAmount;

    @ApiModelProperty(value = "代销充值调账总额")
    private BigDecimal chargeAmount;

    @ApiModelProperty(value = "当日押金余额（每日累减）")
    private BigDecimal deposit;

    @ApiModelProperty(value = "a2z收入总额")
    private BigDecimal a2zAmount;

    @ApiModelProperty(value = "a2z调账金额")
    private BigDecimal a2zAdjustAmount;

    @ApiModelProperty(value = "当日a2z已用信用额度（每日累加）")
    private BigDecimal a2zUsedDeposit;

    private BigDecimal resourceAmount;
    private BigDecimal imsiSellAmount;
    private BigDecimal imsiA2zAmount;
    private BigDecimal imsiResourceAmount;

    private BigDecimal resourceAdjustAmount;

    private BigDecimal rebateUsedAmount;




}
