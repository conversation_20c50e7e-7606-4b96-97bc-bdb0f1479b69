package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ChangeStatusEnum.java
 * @Description 写卡状态枚举值
 * @createTime 2021年04月23日 15:49:00
 */
@AllArgsConstructor
@Getter
public enum ChangeStatusEnum {

    FAIL("写卡失败", "1"),
    SUCCESSNOUP("写卡成功未上报", "2"),
    SUCCESSANDSWITCH("写卡成功已切换", "3"),
    WRITTING("写卡中", "4");

    /**
     * 状态名称
     */
    private String valueName;
    /**
     * 状态值
     */
    private String value;

}