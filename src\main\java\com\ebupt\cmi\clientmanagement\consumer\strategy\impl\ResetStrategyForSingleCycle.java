package com.ebupt.cmi.clientmanagement.consumer.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.client.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.context.FlowPoolConsumerContext;
import com.ebupt.cmi.clientmanagement.consumer.strategy.AbstractFlowPoolConsumerStrategy;
import com.ebupt.cmi.clientmanagement.consumer.uitils.LuWarpper;
import com.ebupt.cmi.clientmanagement.consumer.vo.MockLuVO;
import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.enums.RoleEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.HimsiStatusAndLocationVO;
import com.ebupt.cmi.clientmanagement.exception.GoodException;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.job.vo.FlowPoolRabbitMQMessageVO;
import com.ebupt.cmi.clientmanagement.service.PackageEndService;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.service.impl.FlowPoolServiceImpl;
import com.ebupt.elk.utils.Utils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResetStrategyForSingleCycle.java
 * @Description 单卡单周期重置策略
 * @createTime 2022年01月14日 10:27:00
 */

@Component("resetForSingle")
@Slf4j
public class ResetStrategyForSingleCycle extends AbstractFlowPoolConsumerStrategy {

    @Autowired
    PackageEndService packageEndService;

    @Autowired
    LuWarpper luWarpper;

    @Autowired
    CCRCommonService ccrCommonService;

    @Autowired
    FlowPoolServiceImpl flowPoolServiceImpl;

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = GoodException.class)
    @GlobalTransactional(rollbackFor = Exception.class, noRollbackFor = GoodException.class, timeoutMills = 30000)
    public void handle(FlowPoolConsumerContext flowPoolConsumerContext) {
        log.info("==============单卡重置流程开始，卡{}================",
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());

        super.setPriority(flowPoolConsumerContext);
        super.handle(flowPoolConsumerContext);
        connectWithPms(flowPoolConsumerContext);

        log.info("==============单卡重置流程结束，卡{}================",
                flowPoolConsumerContext.getFlowPoolRabbitMQMessage().getImsi());
    }

    /**
     * @param flowPoolConsumerContext
     * @return
     */
    @Override
    protected boolean tryOutsideNet(FlowPoolConsumerContext flowPoolConsumerContext) {
        try {
            log.info("========================这是第{}次进行外部网元交互重试============================",
                    flowPoolConsumerContext.getRetryTimes());
            FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                    .getFlowPoolRabbitMQMessage();
            boolean useFlowPool = false;
            if ("1".equals(messageVO.getCardType()) && "4".equals(messageVO.getTriggerType())) {
                useFlowPool = true;
            } else {
                useFlowPool = useFlowPool(flowPoolConsumerContext);
            }

            //卡类型: 1:H  2:V，
            if ("1".equals(messageVO.getCardType())) {
                // 套餐唯一id比对成功 或 为流量池卡恢复操作
                if (useFlowPool) {
                    //判断是否是新卡
                    Boolean isNewCard = ccrCommonService.DetermineNewAndOldCards(messageVO.getHimsi());
                    if (isNewCard){
                        log.info("==================检测到是H新卡，判断是否触发rar====================");

                        ChannelCard channelCard = channelCardMapper.selectOne(Wrappers.lambdaQuery(ChannelCard.class)
                                .eq(ChannelCard::getIccid, messageVO.getIccid()));

                        if (ccrCommonService.judgePgwSessionExists(channelCard.getImsi())) {
                            log.info("==================触发H卡RAR====================");
                            controlFeignClient.sendRAR(channelCard.getImsi());
                        }else {
                            log.info("==================H卡会话不存在不下发H卡RAR====================");
                        }

                    }else {
                        log.info("==================检测到是H旧卡，模拟LU上报流程====================");
                        HimsiStatusAndLocationVO andLocationVO =
                                HimsiStatusAndLocationVO.builder()
                                        .imsi(messageVO.getHimsi())
                                        .role(RoleEnum.PHONENUMBER.getRole())
                                        .build();
                        HimsiStatusAndLocationDTO himsiStatusAndLocationDTO =
                                packageEndService.getUserLocation(andLocationVO);
                        flowPoolConsumerContext.setHimsiStatusAndLocationDTO(himsiStatusAndLocationDTO);
                        MockLuVO mockLuVO = getMockLuVO(flowPoolConsumerContext);
                        luWarpper.mockLu(mockLuVO);
                    }
                }
            } else {
                /* 1. 限速
                 * 2. 停用
                 */
                int priority = flowPoolConsumerContext.getPriorityFromTable();
                if (priority == 1 && useFlowPool && StringUtils.isNotBlank(flowPoolConsumerContext.getUpccSignId()) &&
                        !"4".equals(messageVO.getTriggerType())) {
                    log.info("==================检测到是V卡，不需要模拟LU上报流程，直接签约upccResumeSignId====================");
                    VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                            .getVcardAccountInfo(messageVO.getImsi()));
                    CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                            .getCardPoolByImsi(messageVO.getImsi()));

                    //is_sign_upcc 是否去UPCC动态签约;1 true|0 false,注意该字段可能为空
                    if (StringUtils.isNotBlank(cardPool.getIsSignUpcc()) &&
                            "1".equals(cardPool.getIsSignUpcc())) {
                        LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                                .usrIdentifier(vcardInfo.getMsisdn())
                                .srvName(flowPoolConsumerContext.getUpccSignId())
                                .build();
//                        Response<Void> upccSignResult = controlFeignClient
//                                .subscribeService(loadSubscribe);
//                        String bizSignId = "";
//                        if (upccSignResult.isOk()) {
//                            bizSignId = flowPoolConsumerContext.getUpccSignId();
//                        }
//                        pmsFeignClient.updateCardUpccSignBizId("2", vcardInfo.getImsi(), bizSignId);
                        Response.getAndCheckRemoteData(controlFeignClient
                                .subscribeService(loadSubscribe));
                        pmsFeignClient.updateCardUpccSignBizId("2", vcardInfo.getImsi(), flowPoolConsumerContext.getUpccSignId());

                    } else {
                        log.debug("V卡为非动态签约，不进行UPCC签约 imsi: {}", messageVO.getImsi());
                    }
//                    // V卡存在会话校验 存在则下发RAR
//                    if (ccrCommonService.judgePgwSessionExists(vcardInfo.getImsi())) {
//                        log.info("==================触发V卡RAR====================");
//                        controlFeignClient.sendRAR(vcardInfo.getImsi());
//                    }
                }
            }
            log.info("=======================非常幸运，通过了，单卡单周期恢复外部网元交互流程===========================");
            return true;
        } catch (Exception ex) {
            log.error("调用外部网元时发生致命错误，位置：单卡单周期恢复流程");
            log.error("", ex);
            return false;
        }
    }

    /**
     * 加一层，好看点
     *
     * @param flowPoolConsumerContext 流量池上下文
     */
    public void connectWithPms(FlowPoolConsumerContext flowPoolConsumerContext) {
        FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                .getFlowPoolRabbitMQMessage();

        //upcc开户状态
        //1：成功
        //2:失败

        //upcc签约状态
        //1:：成功
        //2:失败
        UpdateOpenStatusReq updateOpenStatusReq =
                UpdateOpenStatusReq
                        .builder()
                        .imsi(messageVO.getImsi())
                        .build();

        //卡类型: 1:H  2:V
        if ("1".equals(messageVO.getCardType())) {
            log.debug("H卡恢复，更新UPCC开户状态、签约状态为失败，upccSignId为空");
            //调用PMS将pms_card表对应的upcc开户置为失败、upcc签约状态置为失败，upcc签约id置空
//            updateOpenStatusReq.setUpccOpenStatus("2");
//            updateOpenStatusReq.setUpccSignStatus("2");
            //为了让LU激活流程去走限速签约流程
            updateOpenStatusReq.setUpccSignId("");
            updateOpenStatusReq.setUpccSignBizId("");
            Response.getAndCheckRemoteData(pmsFeignClient.updateCardOpenStatus(updateOpenStatusReq));
        }
        //要是是V卡
        else if ("2".equals(messageVO.getCardType())) {
            /* 1. 限速
             * 2. 停用
             */
            int priority = flowPoolConsumerContext.getPriorityFromTable();
            //前置状态属于“停用” 或 恢复，则调用PMS将pms_vcard_relation对应的upcc开户置为失败，hss开户置为失败，upcc签约状态置为失败
            if (priority == 2 || "4".equals(messageVO.getTriggerType())) {
                log.debug("V卡停用恢复，更新HSS开户状态、UPCC开户状态、签约状态为失败");
                updateOpenStatusReq.setUpccOpenStatus("2");
                updateOpenStatusReq.setHssOpenStatus("2");
                updateOpenStatusReq.setUpccSignStatus("2");
                updateOpenStatusReq.setUpccSignBizId("");
                Response.getAndCheckRemoteData(pmsFeignClient.updateVimsiOpenStatus(updateOpenStatusReq));
            }
        }
    }

    /**
     * 回写upcc的签约状态为失败
     *
     * @param flowPoolConsumerContext 上下文而已
     */
    public void changeVcardUpccSignStatus(FlowPoolConsumerContext flowPoolConsumerContext) {
        try {
            FlowPoolRabbitMQMessageVO messageVO = flowPoolConsumerContext
                    .getFlowPoolRabbitMQMessage();
            //upcc签约状态
            //1:：成功
            //2:失败
            UpdateOpenStatusReq updateOpenStatusReq =
                    UpdateOpenStatusReq
                            .builder()
                            .imsi(messageVO.getImsi())
                            .upccSignStatus("2")
                            .build();
            Response.getAndCheckRemoteData(pmsFeignClient.updateVimsiOpenStatus(updateOpenStatusReq));
        } catch (Exception ex) {
            log.error(Utils.stackTraceToString(ex.getClass().getName(), ex.getMessage(), ex.getStackTrace()));
            log.error("", ex);
            super.insertIntoError(flowPoolConsumerContext);
        }

    }

    MockLuVO getMockLuVO(FlowPoolConsumerContext context) {
        FlowPoolRabbitMQMessageVO messageVO = context
                .getFlowPoolRabbitMQMessage();
        HcardInfo hcardInfo = context.getHcardInfo();
        HimsiStatusAndLocationDTO himsiStatusAndLocationDTO = context.getHimsiStatusAndLocationDTO();
        return MockLuVO.builder()
                .imsi(messageVO.getImsi())
                .activeType("1")
                .cardForm(hcardInfo.getCardForm())
                .himsi(messageVO.getHimsi())
                .iccid(hcardInfo.getIccid())
                .msisdn(hcardInfo.getMsisdn())
                .packageUniqId(messageVO.getPackageUniqueId())
                .mcc(himsiStatusAndLocationDTO.getMobileCountryCode())
                .cardType(messageVO.getCardType())
                .build();
    }
}
