package com.ebupt.cmi.clientmanagement.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class BlandCardOrdersDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    //订单ID
    private Long orderId;

    //卡片形态1：普通卡（实体卡）2：Esim卡3：贴片卡4：IMSI号
    private String cardForm;

    //收货人
    private String addressee;

    //收货人电话【加密】
    private String phoneNumber;

    //地址，竖线分割【加密】(国家|省份|城市地址|邮寄地址)
    private String address;

    //数量
    private Integer count;

    //订单状态1：已下单2 ：已取消3 ：待付款4 ：已付款5 ：待发货6 ：发货中7 ：已发货8 ：发货失败
    private String orderStatus;

    //订购唯一id，uuid
    private String orderUniqueId;

    //订单批次
    private String orderBatch;

    //收费模式：1：定制卡2：普通卡
    private String chargingMode;

    //发票文件保存路径(NFS)
    private String invoicePath;

    //付款证明保存路径(NFS)
    private String paymentProofsPath;

    private String deliverFailPath;

    private String logistic;

    private String logisticCompany;

}