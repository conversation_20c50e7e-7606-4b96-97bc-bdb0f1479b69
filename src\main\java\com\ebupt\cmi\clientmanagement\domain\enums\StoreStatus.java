package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 主卡出入库状态
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/4/16 15:57
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum StoreStatus {

    /**
     * 在库中
     */
    IN("1", "是"),


    /**
     * 已出库，不在库中
     */
    OUT("2", "否");


    private String k;

    private String val;

    public static String getVal(String k) {
        for (StoreStatus p : StoreStatus.values()) {
            if (p.getK().equals(k)) {
                return p.getVal();
            }
        }
        log.warn("输入不符合要求：{}", k);
        return "";
    }
}
