package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description
 * 限速类型
 */

@AllArgsConstructor
@Getter
public enum RateLimitType {

    /**
     * 1：达量继续使用
     */
    CARD_REACH_CONTINUE("1"),

    /**
     * 2：达量限速
     */
    CARD_REACH_LIMIT("2"),

    /**
     * 3：达量停用
     */
    CARD_REACH_STOP("3"),

    /**
     * 3：达量继续使用
     */
    CYCLE_REACH_CONTINUE("3"),

    /**
     * 1：达量限速
     */
    CYCLE_REACH_LIMIT("1"),

    /**
     * 2：达量停用
     */
    CYCLE_REACH_STOP("2");

//    /**
//     * 1：高速上网
//     */
//    HIGH_SPEED("1"),
//    /**
//     * 2：低速上网
//     */
//    LOW_SPEED("2"),
//    /**
//     * 3：停用
//     */
//    PAUSE("3");

    String type;
}
