package com.ebupt.cmi.clientmanagement.consumer.strategy.factory;

import com.ebupt.cmi.clientmanagement.consumer.strategy.AbstractFlowPoolConsumerStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StrategyFactory.java
 * @Description 策略模式工厂类
 * @createTime 2022年01月12日 16:36:00
 */

@Service("flowPoolConsumerStrategyFactory")
public class StrategyFactory {
    @Autowired
    Map<String, AbstractFlowPoolConsumerStrategy> strategys = new ConcurrentHashMap<>(4);

    public AbstractFlowPoolConsumerStrategy getStrategy(String component) {
        AbstractFlowPoolConsumerStrategy strategy = strategys.get(component);
        if(strategy == null) {
            throw new RuntimeException("no strategy defined");
        }
        return strategy;
    }

}
