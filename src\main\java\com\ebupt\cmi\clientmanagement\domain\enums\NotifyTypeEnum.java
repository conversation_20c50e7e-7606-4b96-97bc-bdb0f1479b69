package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/14 14:39
 */

@Getter
@AllArgsConstructor
public enum NotifyTypeEnum {
    /**
     * 1： 非异步通知
     */
     NON_ASYNC_NOTIFY("1"),
     /**
     * 2： 异步通知失败
     */
     ASYNC_NOTIFY_FAIL("2"),
     /**
     * 3： 异步通知超时
     */
     ASYNC_NOTIFY_TIMEOUT("3");

     private String type;

}
