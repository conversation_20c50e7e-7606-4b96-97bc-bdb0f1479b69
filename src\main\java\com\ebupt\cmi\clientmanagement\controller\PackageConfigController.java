package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.util.StrUtil;
import com.ebupt.cmi.clientmanagement.domain.properties.TestChannelProperties;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.PackageVO;
import com.ebupt.cmi.clientmanagement.service.ChannelBatchTaskDetailService;
import com.ebupt.cmi.clientmanagement.service.ChannelBatchTaskService;
import com.ebupt.cmi.clientmanagement.service.ChannelPackageCardService;
import com.ebupt.cmi.clientmanagement.service.PackageConfigService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Desc 套餐配置
 * <AUTHOR> lingsong
 * @Date 2021/4/19 17:47
 */
@Slf4j
@RestController
@RequestMapping("/package/config")
@Api(tags = "套餐配置相关接口")
public class PackageConfigController {

    @Autowired
    private PackageConfigService configService;

    @Autowired
    private ChannelPackageCardService packageCardService;

    @Autowired
    private ChannelBatchTaskService taskService;

    @Autowired
    private ChannelBatchTaskDetailService detailService;

    @Autowired
    private TestChannelProperties testChannelProperties;

    @PostMapping("/purchased")
    @ApiOperation(value = "已订购套餐列表")
    Response<List<PackageVO>> purchasedPageList(@RequestBody PackageForm form) {
        return Response.ok(packageCardService.purchasedPageList(form));
    }

    @PostMapping("/bind")
    @ApiOperation(value = "绑定套餐", notes = "packageId:自增主键套餐id, imsi:主卡imsi")
    @OperationLog(operationName = "套餐配置-绑定套餐",operationType = OperationTypeEnum.ADD)
    public Response bind(@RequestParam("packageId") String packageId,
                         @RequestParam("imsi") String imsi,
                         @RequestParam(name = "activitionTime",required = false) String activitionTime) {
       SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d hh:mm:ss");
        Date date1 = null;
        if(StrUtil.isNotBlank(activitionTime)){
            try {
                date1 = dateFormat.parse(activitionTime+" 00:00:00");
            }catch (ParseException e){
                throw new BizException("日期格式不对");
            }
        }

        return configService.bind(packageId, imsi ,date1, null) ? Response.ok() : Response.error();
    }

    @PostMapping("/unbind/")
    @ApiOperation(value = "解绑套餐")
    @OperationLog(operationName = "套餐配置-解绑套餐",operationType = OperationTypeEnum.DELETE)
    public Response<List<String>> unBind(@RequestBody @Valid UnBindForm form) {
        return configService.unBind(form) ? Response.ok() : Response.error();
    }

    @ApiOperation(value = "新建批量配置接口")
    @PostMapping(value = "/task", headers = "content-type=multipart/form-data")
    @OperationLog(operationName = "套餐配置-批量配置",operationType = OperationTypeEnum.ADD)
    Response<Void> task(@Validated TaskForm form) {
        return taskService.task(form) ? new Response<>(ResponseResult.SUCCESS.getCode(), "批量配置处理中，请稍后查看！") : Response.error();
    }


    @ApiOperation(value = "套餐页面批量配置")
    @PostMapping(value = "/taskPage")
    @OperationLog(operationName = "套餐配置-批量配置（页面）",operationType = OperationTypeEnum.ADD)
    Response<Void> packageBatchConfigurationByPage(@RequestBody @Validated PackageBatchConfigurationPageVO vo) {
        return taskService.packageBatchConfigurationByPage(vo) ? new Response<>(ResponseResult.SUCCESS.getCode(), "批量配置处理中，请稍后查看！") : Response.error();
    }

    @PostMapping("/task/pageList")
    @ApiOperation(value = "批量配置分页接口")
    Response<List<ChannelTaskVO>> taskPageList(@RequestBody ChannelTaskForm form) {
        return Response.ok(taskService.pageList(form));
    }

    @PostMapping("/task/rollback/{taskId}")
    @ApiOperation(value = "批量配置回滚接口")
    @OperationLog(operationName = "套餐配置-批量配置回滚",operationType = OperationTypeEnum.DELETE)
    Response<List<ChannelTaskVO>> taskRollback(@PathVariable("taskId") Long taskId) {
        return taskService.taskRollback(taskId) ? new Response<>(ResponseResult.SUCCESS.getCode(), "任务回滚中，请稍后查看！") : Response.error();
    }

    @PostMapping("/task/download/{taskId}")
    @ApiOperation(value = "下载任务执行情况文件", notes = "status:[1-任务成功日志,2-任务失败日志,3-回滚成功日志，4-回滚失败日志]")
    public void download(@PathVariable("taskId") Long taskId, @RequestParam(required = false, value = "status") Integer status, HttpServletResponse response) {
        detailService.downloadLog(taskId, status, response);
    }


    @ApiOperation(value = "查询配置的测试渠道商")
    @GetMapping(value = "/getTextChannel")
    Response<List<String>> getTextChannel() {
        return Response.ok(testChannelProperties.getCorpIds());
    }

}
