package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel
public class PackageAndMccDTO {

    @ApiModelProperty(value = "packageUniqueId")
    private String packageUniqueId;

    @ApiModelProperty(value = "套餐Id")
    private String packageId;

    @ApiModelProperty(value = "mcc")
    private String mcc;
}
