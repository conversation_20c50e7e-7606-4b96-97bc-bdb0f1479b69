package com.ebupt.cmi.clientmanagement.controller.pgw;

import com.ebupt.cmi.clientmanagement.domain.dto.QuotaAllocationDTO;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.CcrMessageReportVo;
import com.ebupt.cmi.clientmanagement.service.pgw.CcrMessageReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/ccr")
@RequiredArgsConstructor
public class CcrMessageReportController {

    private final CcrMessageReportService ccrMessageReportService;

    @PostMapping
    public Response<List<QuotaAllocationDTO>> ccrMessageReport(@RequestBody CcrMessageReportVo ccrMessageReportVo){

        return ccrMessageReportService.ccrMessageReport(ccrMessageReportVo);

    }
}
