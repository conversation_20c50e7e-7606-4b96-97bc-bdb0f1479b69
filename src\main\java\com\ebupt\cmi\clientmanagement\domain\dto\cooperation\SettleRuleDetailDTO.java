package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SettleRuleDetailDTO.java
 * @Description 结算规则DTO
 * @createTime 2021年05月07日 20:19:00
 */

@Data
public class SettleRuleDetailDTO {

    @ApiModelProperty(value = "价格")
    @NotNull(message = "price不能为空")
    private BigDecimal price;

    @ApiModelProperty(value = "国家码，给流量方向用")
    private List<String> mcc;

}
