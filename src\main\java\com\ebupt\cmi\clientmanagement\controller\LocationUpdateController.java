package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.lu.LocationUpdateVO;
import com.ebupt.cmi.clientmanagement.service.lu.ILocationUpdateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2021/4/16 10:53
 */
@Api(tags = "位置上报更新相关接口")
@AllArgsConstructor
@RestController
@RequestMapping("/api/v1/lu")
public class LocationUpdateController {

    private final ILocationUpdateService locationUpdateService;

    @ApiOperation(value = "位置上报更新")
    @PostMapping
    public Response<Void> locationUpdate(@RequestBody @Validated LocationUpdateVO locationUpdateVO) {
        locationUpdateService.locationUpdate(locationUpdateVO);
        return Response.ok();
    }

}
