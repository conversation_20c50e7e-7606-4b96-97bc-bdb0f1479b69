package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.reaching;

import com.alibaba.nacos.client.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.reaching.ReachingTreatmentContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.ReachingTreatmentVO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelSurfInfo;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsCardUpccRecord;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageSurfStatusLog;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.domain.hss.CancelLocationVO;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.UnSubscribeServiceVO;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.mapper.CmsCardUpccRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReachingLimitSpeedStrategy.java
 * @Description 通用流量达量限速策略类
 * @createTime 2022年02月28日 16:24:00
 */

@Component("reachingLimitSpeedStrategy")
@Slf4j
public class ReachingLimitSpeedStrategy extends AbstractOutsideNetStrategy {

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Autowired
    ControlFeignClient controlFeignClient;

    @Autowired
    HvShareRepository hvShareRepository;

    @Resource
    CmsCardUpccRecordMapper cmsCardUpccRecordMapper;

    @Override
    protected boolean tryOutsideNet(BaseContext context) {
        try {

            log.info("========================这是第{}次进行外部网元交互重试============================",
                    context.getRetryTimes());

            ReachingTreatmentVO messageVO = (ReachingTreatmentVO) context
                    .getMessageVO();

            HcardInfo hcardInfo = context.getHcardInfo();

            ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;

            log.debug("循环处理此套餐所有的V卡，处理顺序【1. V卡定向模板更新】【2. V卡普通模板更新】【3. H卡定向模板更新】【4. H卡普通模板更新】");


            for (String vimsi : reachingTreatmentContext.getVImsi()) {

                VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                        .getVcardAccountInfo(vimsi));
                if ("4".equals(vcardInfo.getStatus())) {
                    log.debug("此V卡处于暂停状态，不用处理");
                    continue;
                }

                CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                        .getCardPoolByImsi(vimsi));
                if (org.springframework.util.StringUtils.isEmpty(cardPool.getIsSignUpcc()) || !"1".equals(cardPool.getIsSignUpcc())) {
                    log.debug("此V卡卡池不是UPCC动态签约，不用处理");
                    continue;
                }

                log.debug("循环处理此V卡需要处理的定向应用， app: {}", reachingTreatmentContext.getApp());
                if (!CollectionUtils.isEmpty(reachingTreatmentContext.getApp())) {

                    List<CmsCardUpccRecord> cmsCardUpccRecords = cmsCardUpccRecordMapper.selectList(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                            .eq(CmsCardUpccRecord::getImsi, vimsi)
                            .in(CmsCardUpccRecord::getAppId, reachingTreatmentContext.getApp()));

                    for (CmsCardUpccRecord cmsCardUpccRecord : cmsCardUpccRecords) {
                        UnSubscribeServiceVO unSubscribeServiceVO = new UnSubscribeServiceVO(vcardInfo.getMsisdn(), cmsCardUpccRecord.getUpccSignBizId());

                        Response.getAndCheckRemoteData(controlFeignClient.unSubscribeUpccService(unSubscribeServiceVO));

                        cmsCardUpccRecordMapper.deleteById(cmsCardUpccRecord);

                        reachingTreatmentContext.setRetryTimes(0);
                    }
                }

                log.debug("处理此V卡通用流量签约");

                if (!messageVO.getUpccLimitsSignId().equals(vcardInfo.getUpccSignBizId())) {

                    LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                            .usrIdentifier(vcardInfo.getMsisdn())
                            .srvName(messageVO.getUpccLimitsSignId())
                            .build();

                    Response.getAndCheckRemoteData(controlFeignClient
                            .subscribeService(loadSubscribe));

                    Response.getAndCheckRemoteData(pmsFeignClient.
                            updateCardUpccSignBizId("2", vcardInfo.getImsi(), messageVO.getUpccLimitsSignId()));

                    reachingTreatmentContext.setRetryTimes(0);

                }

            }

            log.debug("处理H卡定向应用签约");
            if (!CollectionUtils.isEmpty(reachingTreatmentContext.getApp())) {

                List<CmsCardUpccRecord> cmsCardUpccRecords = cmsCardUpccRecordMapper.selectList(Wrappers.lambdaQuery(CmsCardUpccRecord.class)
                        .eq(CmsCardUpccRecord::getImsi, hcardInfo.getImsi())
                        .in(CmsCardUpccRecord::getAppId, reachingTreatmentContext.getApp()));

                for (CmsCardUpccRecord cmsCardUpccRecord : cmsCardUpccRecords) {
                    UnSubscribeServiceVO unSubscribeServiceVO = new UnSubscribeServiceVO(hcardInfo.getMsisdn(), cmsCardUpccRecord.getUpccSignBizId());

                    Response.getAndCheckRemoteData(controlFeignClient.unSubscribeUpccService(unSubscribeServiceVO));

                    cmsCardUpccRecordMapper.deleteById(cmsCardUpccRecord);

                    reachingTreatmentContext.setRetryTimes(0);

                }
            }

            log.debug("处理H卡通用流量签约");

            if (!messageVO.getUpccLimitsSignId().equals(hcardInfo.getUpccSignBizId())) {

                LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                        .usrIdentifier(hcardInfo.getMsisdn())
                        .srvName(messageVO.getUpccLimitsSignId())
                        .build();

                Response.getAndCheckRemoteData(controlFeignClient
                        .subscribeService(loadSubscribe));

                Response.getAndCheckRemoteData(pmsFeignClient.
                        updateCardUpccSignBizId("1", hcardInfo.getImsi(), messageVO.getUpccLimitsSignId()));

            }
            log.info("=======================非常幸运，通过了，通用达量限速外部网元交互流程===========================");

            return true;

        } catch (Exception ex) {

            log.error("调用外部网元时发生致命错误，位置：通用达量限速流程");

            log.error("", ex);

            return false;

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T extends BaseContext> void handle(T context) throws InterruptedException {

        log.info("============================进入通用达量限速处理流程,imsi:{}=================================", context.getMessageVO().getImsi());

        ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;

        super.setHcardIntoContext(reachingTreatmentContext);

        String packageUniqueIDFromTable = reachingTreatmentContext.getHcardInfo().getUpccSignPackageUniqueId();

        String packageUniqueIDFromMessage = reachingTreatmentContext.getMessageVO().getPackageUniqueId();

        hvShareRepository.updatePackageSurfLogStatus(packageUniqueIDFromMessage, PackageSurfStatusLog.SurfStatusEnum.LIMIT.getStatus());

        hvShareRepository.refreshChannelPackageCardSurfStatus(packageUniqueIDFromMessage, ChannelPackageCard.SurfStatusEnum.LIMIT.getValue(), false);

        //如果二者相等，才进行业务处理
        if (vertifyPackageUniqueID(packageUniqueIDFromMessage, packageUniqueIDFromTable)) {

            reachingTreatmentContext.setNeedCallback(true);

        } else {
            log.info("==========================该卡使用的不是传入的套餐，不做处理=============================");
        }

    }

    @Override
    public <T extends BaseContext> void beforeCallBack(T context) {

        ReachingTreatmentContext reachingTreatmentContext = (ReachingTreatmentContext) context;

        String packageUniqueIDFromMessage = reachingTreatmentContext.getMessageVO().getPackageUniqueId();

        Set<String> vCard = hvShareRepository.getSurfCardByPackageUniqueId(packageUniqueIDFromMessage);

        reachingTreatmentContext.setVImsi(vCard);

        Set<Long> app = hvShareRepository.getSurfAppByPackageUniqueId(packageUniqueIDFromMessage, null);

        reachingTreatmentContext.setApp(app);

    }


}
