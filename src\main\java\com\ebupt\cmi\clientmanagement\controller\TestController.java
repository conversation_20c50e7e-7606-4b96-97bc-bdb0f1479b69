package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.FlowCalculateConsumer;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.delay.DelayContext;
import com.ebupt.cmi.clientmanagement.domain.dto.PmsCardpoolVcardRelation;
import com.ebupt.cmi.clientmanagement.domain.dto.PmsChannelCardDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.through.NotifyActivationDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.through.NotifyExpireDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.through.QuotaNoticeDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.enums.LanguageEnum;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.PackageActiveTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.properties.ActiveNotificationProps;
import com.ebupt.cmi.clientmanagement.domain.response.through.BaseResult;
import com.ebupt.cmi.clientmanagement.domain.vo.AddCCRAppTestVO;
import com.ebupt.cmi.clientmanagement.domain.vo.through.NotifyActivationVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.sms.SmsFeignClient;
import com.ebupt.cmi.clientmanagement.job.context.FlowPoolResetContext;
import com.ebupt.cmi.clientmanagement.mapper.ChannelPackageCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageCardUpccRelationMapper;
import com.ebupt.cmi.clientmanagement.mapper.EopAccessDetailMapper;
import com.ebupt.cmi.clientmanagement.mapper.PackageDirectionRelationMapper;
import com.ebupt.cmi.clientmanagement.service.AccountService;
import com.ebupt.cmi.clientmanagement.service.OutSideApiService;
import com.ebupt.cmi.clientmanagement.service.pgw.impl.CcrMessageReportServiceImpl;
import com.ebupt.cmi.clientmanagement.service.scheduled.ScheduledService;
import com.ebupt.cmi.clientmanagement.utils.AuthorizationUtils;
import com.ebupt.cmi.clientmanagement.utils.DateUtilWrapper;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import com.ebupt.cmi.clientmanagement.utils.Utils;
import com.ebupt.elk.annotion.NormalLog;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/6/6 10:21
 */
@RestController
@RequestMapping("/test")
@Slf4j
@AllArgsConstructor
public class TestController {

    private final SmsFeignClient smsFeignClient;

    private final PmsFeignClient pmsFeignClient;

    private final RestTemplate restTemplate;

    private final RedisUtil redisUtil;

    private final RabbitTemplate rabbitTemplate;

    private final EopAccessDetailMapper eopAccessDetailMapper;
    protected final ActiveNotificationProps activeNotificationProps;

    private final ScheduledService scheduledService;

    private final FlowCalculateConsumer flowCalculateConsumer;

    private final CcrMessageReportServiceImpl ccrMessageReportService;

    @PostMapping("/notifyActivation")
    public BaseResult notifyActivation(@RequestBody NotifyActivationDTO notifyVO) {
        log.info(notifyVO.toString());
        return BaseResult.ok("hao");
    }

    @PostMapping("/notifyExpire")
    public BaseResult notifyExpire(@RequestBody NotifyExpireDTO notifyVO) {
        log.info(notifyVO.toString());
        return BaseResult.ok("hao");
    }

    @PostMapping("/quotaNotice")
    public BaseResult quotaNotice(@RequestBody QuotaNoticeDTO quotaNoticeVO) {
        log.info(quotaNoticeVO.toString());
        return BaseResult.ok("hao");
    }

    @Value("#{${sms.default-content.manual-active-notification}}")
    private Map<String, String> contents;

    @GetMapping("/h")
    @NormalLog
    public void ttt(@RequestParam String path, HttpServletResponse response) {
        Utils.download(path, response);
    }

    private final AccountService accountService;

    private final ChannelPackageCardMapper channelPackageCardMapper;

    @PostMapping("/h/ttt")
    @NormalLog
    public void ttt() throws URISyntaxException {
        pmsFeignClient.getPackageSupportMcc("********");
    }

    @GetMapping("/redis-lua")
    public void redis() {
//        String luaScript = "return {redis.call('DECRBY',KEYS[1],ARGV[1]),redis.call('DECRBY',KEYS[2],ARGV[2]),redis.call('DECRBY',KEYS[3],ARGV[3])}";
//        List<String> res = redisUtil.getLuaExecute(luaScript, new ArrayList<>(Arrays.asList("k1", "k2", "k3")), 1, 3, 4);
//        System.out.println(res);

        String singleDayRedisLuaStr = "local res = {}\n" +
                "local left  = KEYS[1]\n" +
                "local used  = KEYS[2]\n" +
                "local value  = ARGV[1]\n" +
                "local flag  = redis.call('EXISTS',used)\n" +
                "\n" +
                "if( 1 == redis.call('EXISTS',left) )\n" +
                "then\n" +
                "res[1] = redis.call('DECRBY',left,value)\n" +
                "\n" +
                "if( 0 == flag )\n" +
                "then\n" +
                "res[2] = redis.call('INCRBY',used,value)\n" +
                "redis.call('EXPIRE',used,90000)\n" +
                "else\n" +
                "res[2] = redis.call('INCRBY',used,value)\n" +
                "end\n" +
                "end\n" +
                "\n" +
                "return res";
        String aaa = "local res = {}\n" +
                "local left  = KEYS[1]\n" +
                "local value  = ARGV[1]\n" +
                "\n" +
                "if( 1 == redis.call('EXISTS',left) )\n" +
                "then\n" +
                "res[1] = redis.call('DECRBY',left,value)\n" +
                "\n" +
                "end\n" +
                "\n" +
                "return res";
        String bbb = "local res = {}\n" +
                "local left  = KEYS[1]\n" +
                "local used  = KEYS[2]\n" +
                "local value  = ARGV[1]\n" +
                "\n" +
                "if( 1 == redis.call('EXISTS',left) )\n" +
                "then\n" +
                "res[1] = redis.call('DECRBY',left,value)\n" +
                "end\n" +
                "if( 1 == redis.call('EXISTS',used) )\n" +
                "then\n" +
                "res[2] = redis.call('INCRBY',used,value)\n" +
                "end\n" +
                "\n" +
                "return res";
        List<String> res = redisUtil.getLuaExecute(singleDayRedisLuaStr, new ArrayList<>(Arrays.asList("k1", "k2")),3);
        List<String> resa = redisUtil.getLuaExecute(aaa, new ArrayList<>(Arrays.asList("k3")),3);
        List<String> resaaa = redisUtil.getLuaExecute(bbb, new ArrayList<>(Arrays.asList("k4","k5")),3);
        System.out.println(res);
        System.out.println(CollectionUtils.isEmpty(res));
        System.out.println(resa);
        System.out.println(resaaa);
    }

    @PostMapping("/test")
    public void queueTest(){

        try {
            InetAddress localHost = InetAddress.getLocalHost();
            System.out.println("localHost: " + localHost);
            byte[] hardwareAddress = NetworkInterface.getByInetAddress(InetAddress.getLocalHost()).getHardwareAddress();
            System.out.println("hardwareAddress: " + Arrays.toString(hardwareAddress));
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }

//        log.info("延迟队列测试");
//
//        FlowPoolResetContext e = FlowPoolResetContext.builder()
//                .cmsFlowpoolInfoCycleOld(CmsFlowpoolInfoCycle.builder()
//                        .flowPoolId("123")
//                        .flowPoolUniqueId("456").build()).build();
//        String message = JSONObject.toJSONString(e);
//        rabbitTemplate.convertAndSend("flowPool.FlowCalculateDelayQueue", message);
    }

    @PostMapping("/addAppInfoTest")
    public void addAppInfoTest(@RequestBody AddCCRAppTestVO addCCRAppTestVO){
        ccrMessageReportService.addCCRAppInfo(addCCRAppTestVO.getCcrMessageReportVo(),addCCRAppTestVO.getCcrMessageContext());
    }

    @PostMapping("/lua/test01")
    public void luaTest01(@RequestParam String uniqueId,
                          @RequestParam boolean isDecrby,
                          @RequestParam Long flowCount) {
        String luaKey = "local res = {}\n" +
                "local add = KEYS[1]\n" +
                "local used = KEYS[2]\n" +
                "local used24 = KEYS[3]\n" +
                "local value = ARGV[1]\n" +
                "local isDecrby = ARGV[2]\n" +
                "local times  = ARGV[3]\n" +
                "local x = 0\n" +
                "\n" +
                "if( 1 == redis.call('EXISTS', add) and tonumber(redis.call('GET', add)) > 0 ) then" +
                "\tif( '1' == isDecrby ) then\n" +
                "\t\tx = redis.call('DECRBY', add, value)\n" +
                "\t\tif ( tonumber(x) <= 0 ) then\n" +
                "\t\t\tres[1] = x\n" +
                "\t\t\tres[2] = redis.call('INCRBY', used, value + x)\n" +
                "\t\t\tredis.call('EXPIRE', used, times)\n" +
                "\t\t\tres[3] = redis.call('INCRBY', used24, value + x)\n" +
                "\t\t\tredis.call('EXPIRE', used24, times)\n" +
                "\t\t\tredis.call('DEL', add)\n" +
                "\t\telse\n" +
                "\t\t\tres[1] = x\n" +
                "\t\t\tres[2] = redis.call('INCRBY', used, value)\n" +
                "\t\t\tredis.call('EXPIRE', used, times)\n" +
                "\t\t\tres[3] = redis.call('INCRBY', used24, value)\n" +
                "\t\t\tredis.call('EXPIRE', used24, times)\n" +
                "\t\tend\n" +
                "\telse\n" +
                "\t\tres[1] = redis.call('DECRBY', add, 0)\n" +
                "\t\tres[2] = redis.call('INCRBY', used, value)\n" +
                "\t\tredis.call('EXPIRE', used, times)\n" +
                "\t\tres[3] = redis.call('INCRBY', used24, value)\n" +
                "\t\tredis.call('EXPIRE', used24, times)\n" +
                "    end\n" +
                "end\n" +
                "\n" +
                "return res";
        // 当日已使用流量key（自然日）
        String usedDayKey = "package_day_used_".concat(uniqueId).concat("_").concat("20230328");
        // 套餐单日（自然日/24小时）已使用流量 key：package_day_used_{packageUniqueID}_0~n-1
        String usedDayKeyTo24Hours = "package_day_used_".concat(uniqueId).concat("_").concat(String.valueOf(0));
        // 加油包流量key
        String packageAddDayKey = "package_add_day_".concat(uniqueId).concat("_").concat(String.valueOf(0));
        long effectiveDayCount = 20000;
        log.debug("[单日限量]获取套餐加油包单日流量剩余key：{}，当日已使用流量key（自然日）：{}，" +
                "当日已使用流量key（自然日/24小时）：{}", packageAddDayKey, usedDayKey, usedDayKeyTo24Hours);
        List<Long> res;
        try {
            res = redisUtil.getLuaExecute(
                    luaKey,
                    new ArrayList<>(Arrays.asList(packageAddDayKey, usedDayKey, usedDayKeyTo24Hours)),
                    flowCount, isDecrby ? 1 : 0, effectiveDayCount);
            log.debug("[单日限量][流量扣除][加油包]进行Redis流量扣除，扣除流量返回值：{}", res.toString());
        } catch (Exception ex) {
            throw new BizException("[单日限量][流量扣除]Redis流量扣除异常，流程结束");
        }
        if (!CollectionUtils.isEmpty(res)) {
            log.debug("[单日限量][流量扣除]加油包剩余流量：{}，自然日已使用流量：{}，自然日/24h已使用流量：{}",
                    res.get(0), res.get(1), res.get(2));
            if (!isDecrby) {
                // 限速不扣减 只累加 然后返回流程结束
                log.debug("[单日限量][流量扣除]当前话单流量类型为限速流量类型，流程结束");
                return;
            }
            // 限速 流量扣减
            // 扣减后大于0 扣减前也大于0（一般都是）
            if(res.get(0) > 0 && (res.get(0) + flowCount) > 0){
                log.debug("该已激活套餐加油包流量扣除结束且尚存在流量，不进行套餐流量扣除");
                return;
            }
            // 扣减后小于0 继续进行套餐流量扣除
            flowCount = res.get(0) * -1;
            log.debug("该已激活套餐加油包流量扣除结束且已无流量，进行套餐流量扣除，已使用流量累加值为部分，现对套餐进行流量处理，值为：{}", flowCount);
            // ...


        } else {
            log.debug("该已激活套餐不存在可用加油包流量，现进行套餐流量扣除");
        }
        log.debug("流量值为：{}", flowCount);
    }

}
