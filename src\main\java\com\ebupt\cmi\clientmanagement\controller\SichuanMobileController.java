package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.cooperation.DeductVO;
import com.ebupt.cmi.clientmanagement.domain.vo.cooperation.QueryUserPhoneVO;
import com.ebupt.cmi.clientmanagement.service.SichuanMobileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 四川移动相关接口
 */
@Slf4j
@AllArgsConstructor
@Api(tags = "四川移动相关接口")
@RestController
@RequestMapping("/sichuanMobile")
public class SichuanMobileController {

    private final SichuanMobileService sichuanMobileService;

    /**
     * 查询卡号码
     * @param queryUserPhoneVO
     * @return
     */
    @ApiOperation("查询卡号码")
    @PostMapping("queryUserPhone")
    public Response<List<String>> queryUserPhone(@RequestBody @Valid QueryUserPhoneVO queryUserPhoneVO) {
        return Response.ok(sichuanMobileService.queryUserPhone(queryUserPhoneVO));
    }

    @ApiOperation("扣费请求")
    @PostMapping("deduct")
    public Response<Map<String, Object>> deduct(@RequestBody @Valid DeductVO deductReq) {
        return Response.ok(sichuanMobileService.deduct(deductReq));
    }

}
