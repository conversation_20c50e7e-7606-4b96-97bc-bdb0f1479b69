package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/11 16:30
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("cms_channel_package_relation")
public class ChannelPackageRelation extends BaseEntity {

    private Long id;
    /**
     * 厂商id
     */
    private String corpId;
    /**
     * 套餐组id
     */

    private String groupId;

    private String groupName;

    private String cooperationMode;

    /**
     * 是否是自建套餐
     * 1：是
     * 2：不是
     */
    private String isChannelCreate;
}
