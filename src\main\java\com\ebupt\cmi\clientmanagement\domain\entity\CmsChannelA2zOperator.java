package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @description 套餐与卡绑定表
 * @date 2021/4/19 11:14
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_channel_a2z_operator")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsChannelA2zOperator extends BaseEntity {

    private String corpId;

    private String a2zOperatorMcc;

    private String a2zOperatorChargeType;

    private String a2zOperator;

}

