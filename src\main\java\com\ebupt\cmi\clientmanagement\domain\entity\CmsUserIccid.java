package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

/**
 * CmsUserIccid
 *
 * @Author: <PERSON>haoqiankun
 * @Date: 2021/5/10 15:25
 */
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@TableName(value = "cms_user_iccid")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsUserIccid {

    private Long id;

    private String userid;

    private String iccid;
    /**
     * 备注
     */
    private String remark;
}
