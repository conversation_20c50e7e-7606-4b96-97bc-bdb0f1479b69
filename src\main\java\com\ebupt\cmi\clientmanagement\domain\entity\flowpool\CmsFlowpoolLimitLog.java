package com.ebupt.cmi.clientmanagement.domain.entity.flowpool;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmsFlowpoolLimitLog.java
 * @Description cms_flowpool_limit_log
 * @createTime 2022年01月13日 15:33:00
 */

@TableName("cms_flowpool_limit_log")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CmsFlowpoolLimitLog {
    Long id;

    /**
     * iccid或者流量池ID
     */
    String limitId;
    /**
     * 流量池周期唯一ID
     */
    String flowPoolUniq;

    /**
     * 类型
     * 1、卡
     * 2、流量池
     */
    String corpId;

    /**
     * 创建时间，默认数据入库时间
     */
    LocalDateTime createTime;
}
