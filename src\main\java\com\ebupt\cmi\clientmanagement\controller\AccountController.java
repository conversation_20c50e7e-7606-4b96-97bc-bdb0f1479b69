package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.Address;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsUserAccount;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsUserIccid;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.feign.back.vo.RePasswordVO;
import com.ebupt.cmi.clientmanagement.service.AccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Desc 账户相关接口
 * <AUTHOR>
 * @Date 2021/4/25 10:50
 */
@Slf4j
@RestController
@RequestMapping("/account")
@Api(tags = "客户信息相关接口")
public class AccountController {

    @Autowired
    private AccountService accountService;

    @PostMapping("/search")
    @ApiOperation(value = "查询客户信息", notes = "查询客户信息")
    public Response<CmsUserAccount> searchChannelInfo(@RequestParam String msisdn) {
        return accountService.searchOne(msisdn);
    }

    @GetMapping(value = "/searchByType")
    public Response<CmsUserAccount> searchOneByType(@RequestParam("account") String account, @RequestParam("type") Integer type){
        return accountService.searchOneByType(account, type);
    }

    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "新增客户信息", notes = "新增客户信息")
    public Response<Object> searchChannelInfo(@RequestParam String id, @RequestParam String msisdn
            ,@RequestParam String directRatio, @RequestParam("type") String type) {
        accountService.addOne(id,directRatio,msisdn, type);
        return Response.ok();
    }

    @GetMapping(value = "/bind")
    public Response<Void> bindAccount(String phone, String email, String serviceType){
        accountService.bindAccount(phone, email, serviceType);
        return Response.ok();
    }

    @PostMapping("/search/iccid")
    @ApiOperation(value = "查询ICCID", notes = "根据userid查询iccic")
    public Response<List<CmsUserIccid>> searchIccid(@RequestParam String userId) {
        return accountService.searchIccid(userId);
    }

    @PostMapping("/search/address")
    @ApiOperation(value = "查询用户地址", notes = "根据msisdn查询用户地址")
    public Response<List<Address>> getAddressList(@RequestParam String msisdn) {
        return accountService.getAddressList(msisdn);
    }

    @PostMapping("/update/password")
    @ApiOperation(value = "密码重置", notes = "密码重置")
    public Response<Object> modifyPassword(@RequestBody RePasswordVO rePasswordVO) {
        return accountService.modifyPassword(rePasswordVO.getId(),rePasswordVO.getNewPwd());
    }

    @GetMapping("/search/phone")
    @ApiOperation(value = "查询用户个人手机号", notes = "根据msisdn查询用户地址")
    public Response<String> getPhoneByIccid(@RequestParam String iccid) {
        return Response.ok(accountService.getPhoneByIccid(iccid));
    }

}
