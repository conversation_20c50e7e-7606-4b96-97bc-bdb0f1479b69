package com.ebupt.cmi.clientmanagement.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.domain.dto.InactivePackageDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageDelayTask;
import com.ebupt.cmi.clientmanagement.domain.entity.PackageDelayTaskQuery;
import com.ebupt.cmi.clientmanagement.domain.enums.PackageDelayStatusEnums;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.packagedelay.InactivePackageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.packagedelay.PackageDelayTaskAddVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.service.PackageDelayTaskService;
import com.ebupt.cmi.clientmanagement.utils.CsvExportUtil;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 套餐延期管理接口
 *
 * <AUTHOR> sir
 * @Description TODO
 * @date 2021/6/15 18:09
 */
@Slf4j
@RestController
@RequestMapping("/packageDeferred")
@Api(tags = "套餐延期管理相关接口")
@RefreshScope
@RequiredArgsConstructor
public class ChannelPackageDeferredController {

    private final PackageDelayTaskService packageDelayTaskService;

    private final String format = "yyyy-MM-dd HH:mm:ss";


    @ApiOperation(value = "分页未激活套餐查询接口")
    @PostMapping(value = "/getList/inactivePackage", headers = "content-type=multipart/form-data")
    public Response<String> selectPackageCardList(@Validated InactivePackageVO inactivePackageVO) {
        try {
            packageDelayTaskService.getPackagePageList(inactivePackageVO);
            return Response.ok();
        } catch (BizException e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation(value = "分页未激活套餐下载接口")
    @GetMapping("/getList/download/{id}/{type}")
    public void downloadResultFile(@PathVariable("id") Long id, @PathVariable("type") String type,
                                               HttpServletResponse response) throws IOException {
        packageDelayTaskService.downloadResultFile(id, type, response);
    }

    @ApiOperation(value = "分页未激活套餐文件列表查询接口")
    @GetMapping("/getFileList/inactivePackage")
    public Response<List<PackageDelayTaskQuery>> getFileList(@RequestParam(required = false) String fileName,
                                                             @RequestParam int pageNum, @RequestParam int pageSize) {
        try {
            return Response.ok(packageDelayTaskService.getFileList(fileName, pageNum, pageSize));
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("未激活套餐延期任务查询")
    @GetMapping("/queryPackageDelayTask")
    public Response<List<PackageDelayTask>> queryPackageDelayTask(@ApiParam("页数") @RequestParam int pageNum, @ApiParam("每页行数") @RequestParam int pageSize) {
        return Response.ok(packageDelayTaskService.getPackageDelayTaskList(pageNum, pageSize));
    }


    @ApiOperation("未激活套餐延期任务新增")
    @PostMapping(value = "packageDelayTask/add", headers = "content-type=multipart/form-data")
    @OperationLog(operationName = "套餐延期管理——未激活套餐延期任务新增", operationType = OperationTypeEnum.ADD)
    public Response<Void> addPackageDelayTask(@Validated PackageDelayTaskAddVO packageDelayTaskAddVO) {

        try {
            Date date = new SimpleDateFormat(format).parse(packageDelayTaskAddVO.getDelayDate().trim() + " 23:59:59");
            packageDelayTaskService.addPackageDelayTask(packageDelayTaskAddVO.getFile(), date);

            return Response.ok();
        } catch (BizException e) {
            return Response.error(e.getMessage());
        } catch (ParseException e) {
            return Response.error("传入延期时间格式异常");
        }
    }


    @ApiOperation(value = " 套餐延期任务审核接口")
    @PutMapping("/packageDelayTask/check/{id}/{auditStatus}")
    @OperationLog(operationName = "套餐延期管理——套餐延期任务审核", operationType = OperationTypeEnum.UPDATE)
    public Response<Object> checkPackageTask(@ApiParam("套餐任务ID") @PathVariable Long id, @PathVariable @ApiParam("审核结果 [2: 通过 3：不通过]") String auditStatus) {
        if (!PackageDelayStatusEnums.PASSED.getValue().equals(auditStatus) && !PackageDelayStatusEnums.NOT_PASS.getValue().equals(auditStatus)) {
            return Response.error("套餐传入审核状态错误");
        }
        try {
            packageDelayTaskService.check(id, auditStatus);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }


    }
}
