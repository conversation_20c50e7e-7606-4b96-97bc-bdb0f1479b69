package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 16:50
 */
@Slf4j
@AllArgsConstructor
@Getter
public enum CardStatus {
    /**
     * 1、正常
     */
    NORMAL("1", "正常"),

    /**
     * 2、暂停
     */
    SUSPEND("2", "暂停"),

    /**
     * 3、注销
     */
    CANCELLATION("3", "注销");

    String status;
    String explain;

    public static String getExplain(String status) {
        for (CardStatus p : CardStatus.values()) {
            if (p.getStatus().equals(status)) {
                return p.getExplain();
            }
        }
        log.warn("输入不符合要求：{}", status);
        return "";
    }
}
