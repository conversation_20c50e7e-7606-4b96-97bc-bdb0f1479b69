package com.ebupt.cmi.clientmanagement.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties("http")
public class HttpClientConfigProperties {

    /**
     * 建立连接超时时间，毫秒
     */
    private int connectionTimeout;

    /**
     * 获取响应超时时间，毫秒
     */
    private int readTimeout;

    /**
     * 从连接池获取连接超时时间，毫秒
     */
    private int connectionRequestTimeout;

    /**
     * 连接池最大连接数
     */
    private int maxTotal;

    /**
     * 单个请求路由最大连接数
     */
    private int maxPerRoute;

}
