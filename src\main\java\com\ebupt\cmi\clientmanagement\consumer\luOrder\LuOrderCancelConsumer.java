package com.ebupt.cmi.clientmanagement.consumer.luOrder;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.enums.NotifyTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.req.AuditOrderReq;
import com.ebupt.cmi.clientmanagement.domain.response.OutResponse;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.mapper.*;
import com.ebupt.cmi.clientmanagement.service.PackageOverdueService;
import com.ebupt.cmi.clientmanagement.service.PersonalOrderService;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues = "luOrderCancel.delay.queue")
public class LuOrderCancelConsumer {

    private final static String CANCEL_ORDER_LOCK_PREFIX = "apicancelorder_";

    private final PersonalOrderService personalOrderService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private final RedissonLock redissonLock;

    private final static String prefix = "lu_order_";

    private final ChannelOrderMapper channelOrderMapper;

    private final ChannelPackageCardMapper channelPackageCardMapper;

    @Resource
    private CmsPackageDayUsedMapper cmsPackageDayUsedMapper;

    @Resource
    private CmsPackageDayRemainMapper cmsPackageDayRemainMapper;

    @Resource
    private CmsPackageCycleRemainMapper cmsPackageCycleRemainMapper;

    private final CommonRepository commonRepository;

    @RabbitHandler
    public void cancelOrder(String orderUniqueId, Message message, Channel channel) throws IOException {

        try {
            log.info("收到rabbitMq延迟消息，开始处理订单，订单唯一id：{}", orderUniqueId);
            if (commonRepository.getMessageById(message.getMessageProperties().getMessageId())) {
                log.debug("该消息已被处理");
                return;
            }
            cancelOrder2(orderUniqueId, NotifyTypeEnum.ASYNC_NOTIFY_TIMEOUT.getType());
            log.info("队列处理成功，订单已退订");
        } catch (Exception e) {
            log.error("队列处理失败，e: ", e);
            throw new BizException(e.getMessage());
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            commonRepository.deleteMessage(message.getMessageProperties().getMessageId());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder2(String orderUniqueId, String asyncNotifyType) {
        String value = redisTemplate.opsForValue().get(prefix + orderUniqueId);
        if (value == null) {
            log.info("此订单归属redis不存在，不需要队列处理，流程结束");
            return;
        }
        ChannelOrder order = channelOrderMapper.selectOne(Wrappers.lambdaQuery(ChannelOrder.class)
                .eq(ChannelOrder::getOrderUniqueId, orderUniqueId));
        ChannelPackageCard channelPackageCard = channelPackageCardMapper.selectOne(Wrappers.lambdaQuery(ChannelPackageCard.class)
                .eq(ChannelPackageCard::getOrderUniqueId, orderUniqueId));
        if (order == null || channelPackageCard == null) {
            log.info("数据异常，队列处理失败");
            return;
        }

        log.debug("开始退订订单：{}", order.getId());
        personalOrderService.unsubscribeOrder1(order.getId(), asyncNotifyType);
        if (!channelPackageCard.getPackageStatus().equals(ChannelPackageCard.PackageStatusEnum.TO_BE_ACTIVATED.getValue())) {
            personalOrderService.auditOrder(new AuditOrderReq(String.valueOf(order.getId()), "2"));
        }
        String packageUniqueId = channelPackageCard.getPackageUniqueId();
        cmsPackageDayUsedMapper.delete(Wrappers.lambdaQuery(CmsPackageDayUsed.class)
                .eq(CmsPackageDayUsed::getPackageUniqueId, packageUniqueId));
        cmsPackageCycleRemainMapper.delete(Wrappers.lambdaQuery(CmsPackageCycleRemain.class)
                .eq(CmsPackageCycleRemain::getPackageUniqueId, packageUniqueId));
        cmsPackageDayRemainMapper.delete(Wrappers.lambdaQuery(CmsPackageDayRemain.class)
                .eq(CmsPackageDayRemain::getPackageUniqueId, packageUniqueId));
        redisTemplate.delete(prefix + orderUniqueId);
    }
}
