package com.ebupt.cmi.clientmanagement.consumer.hvshare.context.delay;

import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.domain.entity.CmsPackageCardUpccRelation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DelayContext.java
 * @Description 单日延时恢复上下文
 * @createTime 2022年03月07日 11:25:00
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class DelayContext extends BaseContext {
    HimsiStatusAndLocationDTO himsiStatusAndLocationDTO;
    ChannelPackageCard channelPackageCard;
    List<CmsPackageCardUpccRelation> directionalAppPacks;
}
