package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 客户订单表实体
 * @date 2021/4/19 16:34
 */
@Builder
@TableName("cms_channel_order")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelOrder extends BaseEntity {

    @TableId(value = "order_id")
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    private String orderName;

    /**
     * 订购类型：
     * 1：卡
     * 2：套餐
     * 3：卡+套餐
     * 4：终端线下卡池
     * 5：流量池
     * 6：终端厂商套餐
     * 7：加油包
     */
    private String orderType;

    /**
     * 对应订购类型：
     * order_type = 2、3 为套餐ID
     * order_type = 4 为卡池ID
     * order_type = 5 为流量池流程ID
     */
    private String packageId;

    private String iccid;
    private String packageName;

    private String nameEn;

    private String nameTw;

    private String periodUnit;

    private Integer keepPeriod;

    /**
     * 套餐订购有效期，套餐有效期（天） + 当前时间
     */
    private Date effectiveDay;

    private String signBizId;

    private String limitSpeedSignBizId;

    /**
     * 订购渠道
     * 102：API
     * 103：官网（H5）
     * 104:  北京移动
     * 105：批量售卖
     * 106：推广活动
     * 110: 测试渠道
     */
    private String orderChannel;

    /**
     * 卡片形态
     * 1：普通卡（实体卡）
     * 2：Esim卡
     * 3：贴片卡
     */
    private String cardForm;

    /**
     * 物流编号
     */
    private String logistic;

    /**
     * 地址
     */
    private String address;

    /**
     * 收件人
     */
    private String addressee;

    /**
     * 联系号码
     */
    private String phoneNumber;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 邮箱
     */
    private String email;

    private Integer count;

    private String currencyCode;

    /**
     * 价格，单位分 折扣后金额
     */
    private BigDecimal amount;

    private String thirdOrderId;

    /**
     * 订单类型归属
     * 1：个人订单
     * 2：渠道订单
     * 3：绑定订单
     */
    private String orderUserType;

    /**
     * 订单状态
     * 1、待发货
     * 2、完成
     * 3、已退订
     * 4、激活退订待审批
     * 	5：部分退订
     * 	6：部分发货
     * 	7：已回收
     * 	8：部分回收
     * 9：复合状态
     */
    private String orderStatus;

    /**
     * 退订类型
     * 1-过期未激活
     * 2-用户退款
     */
    private String unsubscribeType;

    //退订时间
    @TableField(exist = false)
    private Date unsubscribeTime;

    /**
     * 支付方式（当前为1）
     * 1：直接支付
     * 3：页面支付
     */
    private String payType;

    /**
     * 购买用户的corpid
     */
    private String orderUserId;

    /**
     * 购买用户的名称
     * 1)	个人已注册用户：注册手机号码MSISDN
     * 2)	个人ICCID用户：ICCID
     * 3)	临时用户：临时分配用户ID（uuid）
     * 4)	渠道用户：渠道名称corp_name
     */
    private String orderUserName;

    private String sendLang;

    private String logisticCompany;
    /**
     * 订购唯一ID
     */
    private String orderUniqueId;

    /**
     * 发货时间
     */
    private Date deliveryTime;

    private Date activeAt;

    private String billFlow;

    private String isBindCard;

    private String goodsId;

    @TableField(exist = false)
    private String packageActiveTime;

    private String asyncNotifyType;

    /**
     *合作模式
     * 1：代销
     * 2：A2Z
     */
    private String cooperationMode;

    private BigDecimal subAmount;

    private String subCorpId;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 折扣前金额
     */
    private BigDecimal amountBeforeDiscount;

    /**
     * 订单状态枚举
     * <AUTHOR>
     * @date 2021-5-24 19:34:01
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum OrderStatusEnum {

        /**
         * 1：待发货
         */
        NOT_DELIVERY("1"),

        /**
         * 2：已完成
         */
        FINISHED("2"),

        /**
         * 3：已退订
         */
        UNSUBSCRIBE("3"),

        /**
         * 4：激活退订待审批
         */
        UNSUBSCRIBE_NOT_AUDIT("4"),

        /**
         * 5：部分退订
         */
        PARTIAL_UNSUBSCRIBE("5"),

        /**
         * 6：部分发货
         */
        PARTIAL_DELIVERED("6"),

        /**
         * 9：复合状态
         */
        COMBINATION("9"),

        /**
         * 订单处理中
         */
        ORDER_PROCESS("10"),

        /**
         * 订单处理失败
         */
        ORDER_FAIL("11"),

        /**
         * 发货处理中
         */
        DELIVER_PROCESS("12"),


        /**
         * 退订处理中
         */
        UNSUBSCRIBE_PROCESS("13"),


        UNBIND_PROCESS("14");

        @JsonValue
        private String value;

    }

    /**
     * 支付方式枚举
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum PayTypeEnum {
        /**
         * 直接支付
         */
        DIRECT("1"),

        /**
         * 页面支付
         */
        PAGE("2");

        private String value;
    }

    /**
     * 订购类型
     */
    @Getter
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public enum orderTypeEnum {
        /**
         * 卡
         */
        CARD("1"),
        /**
         * 套餐
         */
        PACKAGE("2"),
        /**
         * 卡+套餐
         */
        CARD_PACKAGE("3"),
        /**
         * 终端线下卡池
         */
        TERMINAL_OFFLINE("4"),
        /**
         * 流量池
         */
        FLOW_POOL("5"),
        /**
         * 加油包
         */
        FUELPACK("7");

        private String value;
    }
}
