package com.ebupt.cmi.clientmanagement.domain.dto;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 终端线下流量/套餐DTO
 * @date 2021/5/17 16:43
 */
@ToString
public class TerminalPackageDTO {

    @ApiModelProperty(value = "套餐名称")
    private String packageName;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "币种编码")
    private String currencyCode;

    @ApiModelProperty(value = "国家码list")
    private List<String> mccList;

    private String mcc;

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMcc() {
        return CollectionUtils.isEmpty(mccList) ? null : mccList.iterator().next();
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public List<String> getMccList() {
        return mccList;
    }

    public void setMccList(List<String> mccList) {
        this.mccList = mccList;
    }
}
