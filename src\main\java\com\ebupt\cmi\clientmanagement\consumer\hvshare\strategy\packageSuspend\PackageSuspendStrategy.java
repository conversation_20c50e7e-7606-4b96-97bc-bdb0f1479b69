package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.packageSuspend;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.packageSuspendContext.SuspendAndRecoverContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelCard;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelPackageCard;
import com.ebupt.cmi.clientmanagement.mapper.ChannelCardMapper;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import com.ebupt.cmi.clientmanagement.utils.SpringContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Set;

@Component("packageSuspendStrategy")
@Slf4j
@RequiredArgsConstructor
public class PackageSuspendStrategy extends AbstractOutsideNetStrategy {

    @Resource
    private ChannelCardMapper channelCardMapper;

    @Resource
    private RedisUtil<Long> redisUtil;

    @Override
    protected <T extends BaseContext> boolean tryOutsideNet(T context) {

        SuspendAndRecoverContext suspendAndRecoverContext = (SuspendAndRecoverContext) context;

        if (!suspendAndRecoverContext.isNeedDeal()) {

            log.info("此套餐不需要外部网元交互，流程结束");

            return true;
        }

        boolean recoverVCard = hvShareRepository.recoverVCard(suspendAndRecoverContext.getVimsi());

        boolean recoverHCard = hvShareRepository.signUpccZeroTemplate(suspendAndRecoverContext.getHcardInfo());

        return recoverVCard && recoverHCard;
    }

    @Override
    public <T extends BaseContext> void handle(T context) throws InterruptedException {

        String packageUniqueIDFromMessage = context.getMessageVO().getPackageUniqueId();

        ChannelPackageCard channelPackageCard = hvShareRepository.getChannelPackageCardByPackageUniqueId(packageUniqueIDFromMessage);

        SuspendAndRecoverContext suspendAndRecoverContext = (SuspendAndRecoverContext) context;

        //避免消息重复消费，导致redis扣减不准确
        if ("2".equals(channelPackageCard.getPackageUseStatus())) {

            log.info("此套餐状态已经为停用，流程结束");

            suspendAndRecoverContext.setNeedDeal(false);

            return;
        }

        Long messageNumber = redisUtil.decr(String.format(BizConstants.PACKAGE_SUSPEND_KEY, suspendAndRecoverContext.getCropId()), 1);

        if (messageNumber <= 0) {

            log.info("最后一条消息，更改渠道商状态为停用");
            hvShareRepository.updateChannelStatus(suspendAndRecoverContext.getCropId(), "2");

            redisUtil.del(String.format(BizConstants.PACKAGE_SUSPEND_KEY, suspendAndRecoverContext.getCropId()));

        }

        setHcardIntoContext(context);

        String packageUniqueIDFromTable = context.getHcardInfo().getUpccSignPackageUniqueId();

        PackageSuspendStrategy packageSuspendStrategy = SpringContextHolder.getBean(PackageSuspendStrategy.class);

        packageSuspendStrategy.updateTable(packageUniqueIDFromTable, packageUniqueIDFromMessage, suspendAndRecoverContext);
    }

    @Transactional(rollbackFor = Exception.class)
    public  void updateTable(String packageUniqueIDFromTable, String packageUniqueIDFromMessage, SuspendAndRecoverContext suspendAndRecoverContext) {

        hvShareRepository.updateChannelPackageCardUseStatus(packageUniqueIDFromMessage, "2");

        if (vertifyPackageUniqueID(packageUniqueIDFromMessage, packageUniqueIDFromTable)) {

            Set<String> vImsiSet = hvShareRepository.getSurfCardByPackageUniqueId(packageUniqueIDFromMessage);

            suspendAndRecoverContext.setVimsi(vImsiSet);

            suspendAndRecoverContext.setImsi(suspendAndRecoverContext.getMessageVO().getImsi());

            channelCardMapper.update(null, new LambdaUpdateWrapper<ChannelCard>()
                    .set(ChannelCard::getLastVimsi, null)
                    .set(ChannelCard::getPackageUniqueId, null)
                    .eq(ChannelCard::getImsi, suspendAndRecoverContext.getMessageVO().getImsi())
                    .eq(ChannelCard::getPackageUniqueId, packageUniqueIDFromMessage));

        } else {

            suspendAndRecoverContext.setNeedDeal(false);

            log.info("[停用队列]主卡当前在用套餐与入队套餐不一致，流程结束");
        }

    }
}
