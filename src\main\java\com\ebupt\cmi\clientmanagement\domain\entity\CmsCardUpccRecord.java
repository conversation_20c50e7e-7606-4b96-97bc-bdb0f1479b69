package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CmsCardUpccRecord对象", description="")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmsCardUpccRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "imsi")
    private String imsi;

    @ApiModelProperty(value = "应用id")
    private Long appId;

    @ApiModelProperty(value = "Upcc签约模板id")
    private String upccSignBizId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


}
