package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PackageDelayStatusEnums {
    /**
     * 待审核
     */
    NOT_AUDIT("1"),

    /**
     * 审核通过
     */
    PASSED("2"),

    /**
     * 审核不通过
     */
    NOT_PASS("3"),

    /**
     * 执行中
     */
    RUNNING("4"),

    /**
     * 延期失败
     */
    DELAY_FAIL("5");

    private String value;

    public boolean matches(String value) {
        return this.value.equals(value);
    }

}
