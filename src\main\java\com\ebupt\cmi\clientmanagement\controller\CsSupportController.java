package com.ebupt.cmi.clientmanagement.controller;

import com.ebupt.cmi.clientmanagement.domain.dto.CurrentPackageDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.GetCDRDTO;
import com.ebupt.cmi.clientmanagement.domain.dto.PurchasedPackageDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelLuReport;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelRecordDetail;
import com.ebupt.cmi.clientmanagement.domain.entity.ChannelSurf;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.validation.CommonGroup;
import com.ebupt.cmi.clientmanagement.domain.validation.CurrentPackageGroup;
import com.ebupt.cmi.clientmanagement.domain.validation.VimsiLuGroup;
import com.ebupt.cmi.clientmanagement.domain.vo.ActiveSpecificPackageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.GetConsumptionVO;
import com.ebupt.cmi.clientmanagement.domain.vo.QueryPackageFlowVO;
import com.ebupt.cmi.clientmanagement.domain.vo.ReplaceIccidVO;
import com.ebupt.cmi.clientmanagement.domain.vo.cssupport.CardFlowDetailsVO;
import com.ebupt.cmi.clientmanagement.domain.vo.cssupport.CurrentPackageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.cssupport.PackageEarlierRecoveryVO;
import com.ebupt.cmi.clientmanagement.domain.vo.cssupport.VImsiReplaceVO;
import com.ebupt.cmi.clientmanagement.service.ICsSupportService;
import com.ebupt.cmi.clientmanagement.service.PackageService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客服支撑Controller
 * @date 2021/5/7 16:17
 */
@Api(tags = "客服支撑相关接口")
@RestController
@RequestMapping("/api/v1/customerService")
@AllArgsConstructor
public class CsSupportController {

    private final ICsSupportService csSupportService;
    private final PackageService packageService;

    @ApiOperation(value = "分页查询卡当前位置套餐")
    @GetMapping("/package/current")
    public Response<PageResult<CurrentPackageDTO>> getCurrentMccPackages(@Validated({CommonGroup.class, CurrentPackageGroup.class}) CurrentPackageVO currentPackageVO) {
        PageResult<CurrentPackageDTO> result = csSupportService.getCurrentMccPackages(currentPackageVO);
        return Response.ok(result);
    }

    @ApiOperation(value = "激活套餐")
    @PostMapping("/package/active")
    @OperationLog(operationName = "客服支撑——套餐激活",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> activePackage(@RequestBody @Validated(value = CommonGroup.class) ActiveSpecificPackageVO activeSpecificPackageVO) {
        packageService.activePackage(activeSpecificPackageVO);
        return Response.ok();
    }

    @ApiOperation(value = "客服换卡")
    @PostMapping("/package/replaceIccid")
    @OperationLog(operationName = "客服支撑——客服换卡",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> replaceIccid(@RequestBody @Validated(value = CommonGroup.class) ReplaceIccidVO replaceIccidVO) {
        packageService.replaceIccid(replaceIccidVO);
        return Response.ok();
    }

    @ApiOperation(value = "卡已购买套餐详情查询")
    @GetMapping("/package/purchasedDetail")
    public Response<PageResult<PurchasedPackageDTO>> getPurchasedPackagesDetail(@Validated({CommonGroup.class}) CurrentPackageVO packageVO) {
        PageResult<PurchasedPackageDTO> result = csSupportService.getPurchasedPackagesDetail(packageVO);
        return Response.ok(result);
    }

    @ApiOperation(value = "卡已购买套餐查询")
    @GetMapping("/package/purchased")
    public Response<PageResult<PurchasedPackageDTO>> getPurchasedPackages(@Validated({CommonGroup.class}) CurrentPackageVO packageVO) {
        PageResult<PurchasedPackageDTO> result = csSupportService.getPurchasedPackages(packageVO);
        return Response.ok(result);
    }

    @ApiOperation(value = "卡已购买套餐流量层级查询")
    @PostMapping("/package/getConsumption")
    public Response<List<GetConsumptionVO>> getConsumption(@RequestParam String packageUniqueId) {
        return Response.ok(csSupportService.getConsumption(packageUniqueId));
    }

    @ApiOperation(value = "已购买套餐CDR信息查询接口")
    @PostMapping("/package/getCDR")
    public Response getCDR(@RequestBody GetCDRDTO getCDRDTO) {
        return Response.ok(csSupportService.getCDR(getCDRDTO));
    }


    @ApiOperation(value = "已购买套餐CDR信息覆盖时间查询接口")
    @PostMapping("/package/getCoverHours")
    public Response getCoverHours() {
        return Response.ok(csSupportService.getCoverHours());
    }

    @ApiOperation(value = "主卡位置更新查询接口")
    @GetMapping("/luDetails/h")
    public Response<PageResult<ChannelLuReport>> getHLuRecords(@Validated({CommonGroup.class}) CurrentPackageVO packageVO) {
        PageResult<ChannelLuReport> results = csSupportService.getHLuRecords(packageVO);
        return Response.ok(results);
    }

    @ApiOperation(value = "h卡位置更新查询接口")
    @GetMapping("/luDetails/hOnly")
    public Response<PageResult<ChannelLuReport>> getHLuRecordsOnly(@Validated({CommonGroup.class}) CurrentPackageVO packageVO) {
        PageResult<ChannelLuReport> results = csSupportService.getHLuRecordsOnly(packageVO);
        return Response.ok(results);
    }

    @ApiOperation(value = "V卡位置更新查询接口")
    @GetMapping("/luDetails/v")
    public Response<PageResult<ChannelLuReport>> getVLuRecords(@Validated({CommonGroup.class, VimsiLuGroup.class}) CurrentPackageVO packageVO) {
        PageResult<ChannelLuReport> results = csSupportService.getVLuRecords(packageVO);
        return Response.ok(results);
    }

    @ApiOperation(value = "套餐v卡上报国家查询接口")
    @GetMapping("/surf/getMcc/v")
    public Response<PageResult<ChannelSurf>> getVSurfMccRecords(@Validated({CommonGroup.class, VimsiLuGroup.class}) CurrentPackageVO packageVO) {
        PageResult<ChannelSurf> results = csSupportService.getVSurfMccRecords(packageVO);
        return Response.ok(results);
    }
    

    @ApiOperation(value = "套餐提前回收接口")
    @PostMapping("/recoveryPackage")
    @OperationLog(operationName = "客服支撑——套餐提前回收",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> recoveryPackageEarlier(@RequestBody @Validated PackageEarlierRecoveryVO packageEarlierRecoveryVO) {
        csSupportService.recoveryPackageEarlier(packageEarlierRecoveryVO);
        return Response.ok();
    }

    @ApiOperation(value = "更换vimsi接口")
    @PostMapping("/replaceVImsi")
    @OperationLog(operationName = "客服支撑——更换vimsi",operationType = OperationTypeEnum.UPDATE)
    public Response<Void> replaceVImsi(@RequestBody @Validated VImsiReplaceVO vImsiReplaceVO) {
        csSupportService.replaceVImsi(vImsiReplaceVO);
        return Response.ok();
    }

    @ApiOperation(value = "查询套餐流量接口")
    @GetMapping("/queryPackageFlow")
    public Response<QueryPackageFlowVO> queryPackageFlow(@RequestParam String cardType, @RequestParam String imsi,
                                                         @RequestParam String packageUniqueId) {
        return Response.ok(csSupportService.queryPackageFlow(cardType, imsi, packageUniqueId));
    }
}
