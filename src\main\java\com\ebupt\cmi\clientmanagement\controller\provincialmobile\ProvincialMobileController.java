package com.ebupt.cmi.clientmanagement.controller.provincialmobile;

import com.ebupt.cmi.clientmanagement.domain.response.CmLinkResponse;
import com.ebupt.cmi.clientmanagement.domain.vo.CardBindUnbindVo;
import com.ebupt.cmi.clientmanagement.domain.vo.PackageSearchVo;
import com.ebupt.cmi.clientmanagement.service.ProvincialMobileService;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/pm")
@Api(tags = "省移动对接相关接口")
public class ProvincialMobileController {


    private final ProvincialMobileService provincialMobileService;

    /**
     * 号码验证接口
     *
     * @param cmccNumber 省移动号码
     * @param iccid      iccid
     * @return
     */
    @GetMapping("/num_verify")
    @ApiOperation("号码验证接口")
    @NormalLog
    CmLinkResponse numberVerify(@RequestParam String cmccNumber, @RequestParam String iccid) {
        return provincialMobileService.numberVerify(cmccNumber, iccid);
    }

    /**
     * 主副卡绑定/解绑接口
     * @param vo
     * @return
     */
    @PostMapping("/card_bind_unbind")
    @ApiOperation("主副卡绑定/解绑接口")
    @NormalLog
    CmLinkResponse cardBindUnbind(@RequestBody CardBindUnbindVo vo) {
        return provincialMobileService.cardBindUnbind(vo);
    }


    @PostMapping("/package_search")
    @ApiOperation("副卡套餐信息查询")
    @NormalLog
    public CmLinkResponse packageSearch(@RequestBody PackageSearchVo vo) {
        return provincialMobileService.packageSearch(vo);
    }
}
