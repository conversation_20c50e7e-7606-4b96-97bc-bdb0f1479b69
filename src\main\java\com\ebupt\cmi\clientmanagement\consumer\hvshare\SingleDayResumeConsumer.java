package com.ebupt.cmi.clientmanagement.consumer.hvshare;

import com.alibaba.fastjson.JSON;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.ContextUtil;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.delay.DelayContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.enums.QueueEnum;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.CommonRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.singledaydelay.SingleDayDelayStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.factory.HVShareStrategyFactory;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.CardTypeEnum;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResumeDelayQueue.java
 * @Description 单日恢复延时队列消费
 * @createTime 2022年02月28日 15:01:00
 */

@Component
@RabbitListener(queues = "singleDayResume.delay.queue")
@Slf4j
public class SingleDayResumeConsumer {
    @Autowired
    HVShareStrategyFactory factory;

    @Autowired
    CommonRepository commonRepository;

    @RabbitHandler
    public void process(String messageString, Channel channel, Message message) throws IOException {

        log.info("============================rabbitMQ收到消息{}==================================", messageString);

        try {
            if (commonRepository.getMessageById(message.getMessageProperties().getMessageId())) {
                log.debug("该消息已被处理");
                return;
            }

            SingleDayDelayVO messageVO =
                    JSON.parseObject(messageString, SingleDayDelayVO.class);

            DelayContext context = (DelayContext) ContextUtil
                    .getContext(DelayContext.class);

            //给上下文设置值
            context.setMessageVO(messageVO);

            context.setQueueEnum(QueueEnum.ResumeDelayQueue);

            try {

                SingleDayDelayStrategy strategy = (SingleDayDelayStrategy) factory
                        .getStrategy("singleDayDelayStrategy");

                assert strategy != null;

                strategy.handle(context);

                //请求lu
                strategy.callBack(context);

            } catch (Exception ex) {

                log.error("", ex);

                commonRepository.insertErrorLog(context,
                        ex.getClass().getName() + " " + ex.getMessage());

            }

        } catch (Exception ex) {

            log.error("", ex);

        } finally {

            //ack
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

            commonRepository.deleteMessage(message.getMessageProperties().getMessageId());

        }

    }
}
