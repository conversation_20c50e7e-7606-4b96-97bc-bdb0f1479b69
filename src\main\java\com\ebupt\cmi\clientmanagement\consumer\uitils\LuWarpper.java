package com.ebupt.cmi.clientmanagement.consumer.uitils;

import com.ebupt.cmi.clientmanagement.consumer.vo.MockLuVO;
import com.ebupt.cmi.clientmanagement.domain.entity.flowpool.CmsChannelLuLocal;
import com.ebupt.cmi.clientmanagement.domain.enums.lu.LuTypeEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.lu.LocationUpdateVO;
import com.ebupt.cmi.clientmanagement.exception.BizException;
import com.ebupt.cmi.clientmanagement.feign.oms.OmsFeignClient;
import com.ebupt.cmi.clientmanagement.mapper.flowpool.CmsChannelLuLocalMapper;
import com.ebupt.cmi.clientmanagement.service.lu.ILocationUpdateService;
import com.ebupt.elk.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import static cn.hutool.core.convert.Convert.toDate;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LuWarpper.java
 * @Description 封装LU，装饰者
 * @createTime 2022年01月17日 11:31:00
 */

@Component
@Slf4j
public class LuWarpper {

    @Autowired
    ILocationUpdateService iLocationUpdateService;

    @Autowired
    OmsFeignClient omsFeignClient;

    @Autowired
    CmsChannelLuLocalMapper cmsChannelLuLocalMapper;

    public void mockLu(MockLuVO mockLuVO) {

        String gtCode = Response.getAndCheckRemoteData(omsFeignClient.getOneGtByMcc(mockLuVO.getMcc()));

        LocalDateTime now = LocalDateTime.now();

        LocationUpdateVO locationUpdateVO =
                LocationUpdateVO
                        .builder()
                        .imsi(mockLuVO.getImsi())
                        .rTime(toDate(now))
                        .currNum(gtCode)
                        .luType(LuTypeEnum.DEFAULT_LU_TYPE.getLuType())
                        .mockLu(true)
                        .isCcrMessage(false)
                        .build();

        try {

            iLocationUpdateService.locationUpdate(locationUpdateVO);

        } catch (BizException ex) {

            log.error("调用模拟lu失败",ex);

        }


        insertIntoCmsChannelLuLocal(mockLuVO, now);
    }

    public void insertIntoCmsChannelLuLocal(MockLuVO mockLuVO, LocalDateTime date) {

        CmsChannelLuLocal cmsChannelLuLocal = CmsChannelLuLocal
                .builder()
                .imsi(mockLuVO.getImsi())
                .activeType(mockLuVO.getActiveType())
                .himsi(mockLuVO.getHimsi())
                .mcc(mockLuVO.getMcc())
                .iccid(mockLuVO.getIccid())
                .msisdn(mockLuVO.getMsisdn())
                .cardForm(mockLuVO.getCardForm())
                .packageUniqueId(mockLuVO.getPackageUniqId())
                .reportTime(date)
                .reportType(mockLuVO.getCardType())
                .build();

        cmsChannelLuLocalMapper.insert(cmsChannelLuLocal);

    }

    public static LocalDateTime convertDateToLocalDateTime(Date date) {
        ZoneId zoneId = ZoneId.systemDefault();

        return LocalDateTime.ofInstant(date.toInstant(), zoneId);
    }

}
