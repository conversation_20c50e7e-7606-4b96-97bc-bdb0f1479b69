package com.ebupt.cmi.clientmanagement.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebupt.cmi.clientmanagement.controller.channel.ChannelA2zruleRelationVo;
import com.ebupt.cmi.clientmanagement.domain.dto.*;
import com.ebupt.cmi.clientmanagement.domain.dto.Context.ChannelContext;
import com.ebupt.cmi.clientmanagement.domain.dto.channel.MktChannelPageDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.req.RuleInformation;
import com.ebupt.cmi.clientmanagement.domain.response.PageResult;
import com.ebupt.cmi.clientmanagement.domain.response.RealRule;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.*;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.ChannelCorpPageVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channel.IsChargingForm;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.DepositRecordVO;
import com.ebupt.cmi.clientmanagement.domain.vo.channelself.GetDepositVo;
import com.ebupt.cmi.clientmanagement.feign.back.vo.User;
import com.ebupt.cmi.clientmanagement.service.ChannelBindService;
import com.ebupt.cmi.clientmanagement.service.ChannelCardService;
import com.ebupt.cmi.clientmanagement.service.ChannelOrderService;
import com.ebupt.cmi.clientmanagement.service.ChannelService;
import com.ebupt.cmi.common.operationlog.annotation.OperationLog;
import com.ebupt.cmi.common.operationlog.enums.OperationTypeEnum;
import com.ebupt.elk.annotion.NormalLog;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Desc 渠道商相关接口
 * <AUTHOR> lingsong
 * @Date 2021/4/25 10:50
 */
@Slf4j
@RestController
@RequestMapping("/channel")
@Api(tags = "渠道商相关接口")
public class ChannelController {

    @Autowired
    private ChannelService channelService;

    @Autowired
    private ChannelBindService bindService;

    @Autowired
    private ChannelCardService channelCardService;

    @Autowired
    private ChannelOrderService orderService;

    @GetMapping("/{corpId}")
    @ApiOperation(value = "根据id获取厂商", notes = "根据id获取厂商")
    public Response<Channel> getOne(@PathVariable String corpId) {
        return Response.ok(channelService.getOne(corpId));
    }

    @PostMapping("/list")
    @ApiOperation(value = "根据id列表获取厂商列表", notes = "根据id列表获取厂商列表")
    public Response<List<Channel>> list(@RequestBody(required = false) Set<String> corpIds) {
        return Response.ok(channelService.getList(corpIds));
    }

    @PostMapping("/pagelist")
    public Response<List<Channel>> pagelist(@RequestBody ChannelVO form) {
        return Response.ok(channelService.pagelist(form));
    }

    @PostMapping("/searchList")
    @ApiOperation(value = "获取厂商列表", notes = "获取厂商列表")
    public Response<List<Channel>> list(@RequestBody ChannelVO channelVO) {
        return Response.ok(channelService.getList(channelVO));
    }

    @PostMapping("/listByImsis")
    @ApiOperation(value = "根据imsi列表获取厂商列表", notes = "根据imsi列表获取厂商列表")
    public Response<List<ChannelCardVO>> listByImsis(@RequestBody List<String> imsis) {
        return Response.ok(channelService.getChannelCardByImsis(imsis));
    }

    @PostMapping("/listBakByImsis")
    @ApiOperation(value = "根据imsi列表获取厂商列表", notes = "根据imsi列表获取厂商列表")
    public Response<List<ChannelCardVO>> listBakByImsis(@RequestBody List<String> imsis) {
        return Response.ok(channelService.getChannelCardBakByImsis(imsis));
    }

    @GetMapping("/emailList")
    @ApiOperation(value = "根据sales_mail列表获取厂商列表", notes = "根据sales_mail列表获取厂商列表")
    public Response<List<String>> emailList() {
        return Response.ok(channelService.getEmailList());
    }

    @GetMapping("/package")
    @ApiOperation(value = "获取合作商下的套餐", notes = "获取合作商下的套餐")
    public Response<List<ChannelPackage>> getChannelPackage() {
        return Response.ok(channelService.channelPackage());
    }


    @PostMapping("/bind/package")
    @ApiOperation(value = "批量为合作商的卡绑定套餐", notes = "批量为合作商的卡绑定套餐")
    public Response<Void> channelBind(@RequestBody List<ChannelBind> channelBinds) {
        return bindService.channelPackageBind(channelBinds) ? Response.ok() : Response.error();
    }

    @GetMapping("/bind/list/{iccid}")
    @ApiOperation(value = "查询套餐卡绑定信息", notes = "查询套餐卡绑定信息")
    public Response<List<ChannelBind>> getchannelBindList(@PathVariable("iccid") @ApiParam("卡iccid编号") String iccid) {
        return Response.ok(bindService.getChannelBind(iccid));
    }

    @GetMapping("/bind/getbindlist/{packageid}")
    @ApiOperation(value = "根据套餐ID查询套餐卡绑定信息", notes = "根据套餐ID查询套餐卡绑定信息")
    public Response<List<ChannelBind>> getchannelBindListByPackage(@PathVariable("packageid") @ApiParam("套餐编号") String packageid) {
        return Response.ok(bindService.getChannelBindByPackageId(packageid));
    }

    @DeleteMapping("/delbind/{iccid}")
    @ApiOperation(value = "卡套餐解绑", notes = "卡套餐解绑")
    public Response<Void> channelBind(@PathVariable String iccid) {
        return bindService.channelPackagedelBind(iccid) ? Response.ok() : Response.error();
    }

    @DeleteMapping("/delCard/{imsi}")
    @ApiOperation(value = "客户与卡删除", notes = "客户与卡删除")
    public Response<Void> channelCardDelByImsi(@PathVariable String imsi) {
        return channelCardService.channelCardDelByImsi(imsi) ? Response.ok() : Response.error();
    }


    @PostMapping("/card")
    @ApiOperation(value = "添加客户与卡关系", notes = "添加客户与卡关系")
    public Response<Void> channelCard(@RequestBody List<ChannelCard> channelCards) {
        return channelCardService.channelCard(channelCards) ? Response.ok() : Response.error();
    }

    @PostMapping("/searchOne")
    @ApiOperation(value = "查询渠道用户客户信息", notes = "查询渠道用户客户信息")
    public Response<Channel> searchChannelInfo(@RequestParam String corpName) {
        return channelService.searchChannelInfo(corpName);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增渠道用户客户信息", notes = "新增渠道用户客户信息")
    public Response searchChannelInfo(@RequestBody ChannelVO channelVO) {
        channelService.addOne(channelVO);
        return Response.ok();
    }


    @ApiOperation(value = "码号资源回收接口")
    @PostMapping("/recycling")
    public Response<Void> recycling(@RequestBody @Valid Recycling recycling) {
        orderService.recycling(recycling);
        return Response.ok();
    }

    @PostMapping("/card/getLastImsiList")
    @ApiOperation(value = "查询最后分配的VIMIS", notes = "查询最后分配的VIMIS")
    public Response<Map<String, String>> getLastImsiList(@RequestBody List<String> himsiList) {
        return Response.ok(channelCardService.getLastImsiList(himsiList));
    }

    /***************************************************************************
     * 渠道接口
     * ch
     * ************************************************************************/
    @ApiOperation("渠道新增")
    @PostMapping("/newChannel")
    @OperationLog(operationName = "渠道管理——渠道新增", operationType = OperationTypeEnum.ADD)
    public Response newChannel(@RequestBody NewChannelVO newChannelVO) {
        try {
            channelService.newChannel(newChannelVO);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道修改")
    @PutMapping("/updateChannel")
    @OperationLog(operationName = "渠道管理——渠道修改", operationType = OperationTypeEnum.UPDATE)
    public Response updateChannel(@RequestBody UpdateChannelVO updateChannelVO) {
        try {
            channelService.updateChannel(updateChannelVO);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("渠道审批")
    @PutMapping("/approvalChannel")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "corpId", value = "渠道id"),
            @ApiImplicitParam(name = "status", value = "状态")
    })
    @OperationLog(operationName = "渠道管理——渠道审批", operationType = OperationTypeEnum.AUDIT)
    public Response approvalChannel(@RequestParam String corpId, @RequestParam String status) {
        try {
            channelService.approvalChannel(corpId, status);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("测试imsi新增")
    @PostMapping("/testImsi/add")
    @OperationLog(operationName = "渠道管理——测试imsi新增", operationType = OperationTypeEnum.ADD)
    public Response<String> addTestImsi(@RequestParam String imsi) {
        try {
            channelService.addTestImsi(imsi);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("测试imsi删除")
    @DeleteMapping("/testImsi/delete")
    @OperationLog(operationName = "渠道管理——测试imsi删除", operationType = OperationTypeEnum.DELETE)
    public Response<String> deleteTestImsi(@RequestBody List<String> ids) {
        try {
            channelService.deleteTestImsi(ids);
            return Response.ok();
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @ApiOperation("测试imsi查询")
    @GetMapping("/testImsi/query")
    public Response<List<TestImsi>> getTestImsi(@RequestParam(required = false) String imsi,
                                                @RequestParam int pageNum, @RequestParam int pageSize) {
        try {
            return Response.ok(channelService.getTestImsi(imsi, pageNum, pageSize));
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }


    /***********************************************************************************************************************/
    @PostMapping("/card/pageList")
    public Response<List<ChannelCard>> channelCardPageList(@RequestBody ChannelCardVO form) {
        return channelCardService.pageList(form);
    }

    @PostMapping("/card/pageListImsi")
    public Response<List<String>> pageListImsi(@RequestBody ChannelCardVO form) {
        return channelCardService.pageListImsi(form);
    }

    @PostMapping("/card/pageListCount")
    public Response<Integer> pageListCount(@RequestBody ChannelCardVO form) {
        return channelCardService.pageListCount(form);
    }

    @PostMapping("/card/pageListbak")
    public Response<List<ChannelCardBak>> channelCardPageListBak(@RequestBody ChannelCardVO form) {
        return channelCardService.pageListBak(form);
    }

    @PostMapping("/packageGroupBind")
    public Response<List<String>> packageGroupBind(@RequestBody List<String> groupIdList) {
        try {
            return Response.ok(channelService.packageGroupBind(groupIdList));
        } catch (Exception e) {
            return Response.error(e.getMessage());
        }
    }

    @GetMapping("/getChanelPackageGroups")
    public Response<List<ChannelPackageGroupsVO>> getChanelPackageGroups(@RequestParam String groupId) {
        return Response.ok(channelService.getChanelPackageGroups(groupId));
    }

    @DeleteMapping
    public Response deleteChannel(@RequestParam("corpId") String corpId) {

        if (channelService.deleteChannel(corpId)) {
            return Response.ok();
        } else {
            return Response.error("删除失败，该客户不符合删除条件");
        }
    }

    @PostMapping("/getCardRealNameInfos")
    public Response<RealRule> getCardRealNameInfo(@RequestBody RuleInformation ruleInformation) {
        try {
            return Response.ok(channelService.getCardRealNameInfo(ruleInformation));
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(e.getMessage());
        }
    }

    @PostMapping("/getChannelType")
    public Response<List<ChannelTypeDTO>> getChannelType(@RequestBody List<String> ebsCode) {
        return Response.ok(channelService.getChannelType(ebsCode));
    }

    @ApiOperation("0级渠道新增")
    @PostMapping("/newTopChannel")
    @OperationLog(operationName = "渠道管理——0级渠道新增", operationType = OperationTypeEnum.ADD)
    public Response newTopChannel(@RequestBody @Valid NewTopChannelVO newTopChannelVO) {
        channelService.newTopChannel(newTopChannelVO);
        return Response.ok();
    }

    @ApiOperation("0级渠道修改")
    @PutMapping("/updateTopChannel")
    @OperationLog(operationName = "渠道管理——0级渠道修改", operationType = OperationTypeEnum.UPDATE)
    public Response updateTopChannel(@RequestBody @Valid UpdateTopChannelVO updateTopChannelVO) {
        channelService.updateTopChannel(updateTopChannelVO);
        return Response.ok();
    }

    @ApiOperation("0级渠道删除")
    @DeleteMapping("/deleteTopChannel")
    @OperationLog(operationName = "渠道管理——0级渠道删除", operationType = OperationTypeEnum.DELETE)
    public Response deleteTopChannel(@RequestParam("corpId") String corpId) {
        channelService.deleteTopChannel(corpId);
        return Response.ok();
    }

    @ApiOperation("0级渠道查询")
    @GetMapping("/getTopChannel")
    public Response<List<UpdateTopChannelVO>> getTopChannel(@RequestParam int pageNum, @RequestParam int pageSize) {
        return Response.ok(channelService.getTopChannel(pageNum, pageSize));
    }

    @GetMapping("/getTopChannelRelationCorpIds")
    public Response<List<String>> getTopChannelRelationCorpIds(@RequestParam String corpId) {
        return Response.ok(channelService.getTopChannelRelationCorpIds(corpId));
    }

    @GetMapping("/getAllKindsOfChannel")
    public Response<ChannelContext> getAllKindsOfChannel(@RequestParam String corpId) {
        return Response.ok(channelService.getAllKindsOfChannel(corpId));
    }

    @ApiOperation("0级渠道商专用查询一级渠道商")
    @GetMapping("/getCorpList")
    public Response<List<SeconedChannelVO>> getCorpList(@RequestParam int pageNum,
                                                        @RequestParam int pageSize,
                                                        @RequestParam(required = false) String corpName,
                                                        @RequestParam(required = false, defaultValue = "") String topChannelCorpId) {
        return Response.ok(channelService.getCorpList(pageNum, pageSize, corpName, topChannelCorpId));
    }

    @ApiOperation("物联网平台流量明细查询")
    @GetMapping("/getCorpFlowDetail")
    public Response<List<CmsCorpFlowdetail>> getCorpFlowDetail(@RequestParam int pageNum,
                                                               @RequestParam int pageSize,
                                                               @RequestParam String corpId,
                                                               @RequestParam(required = false) String country,
                                                               @RequestParam(required = false) String beginDate,
                                                               @RequestParam(required = false) String endDate) {
        return Response.ok(channelService.getCorpFlowDetail(pageNum, pageSize, country, corpId, beginDate, endDate));
    }

    @ApiOperation("渠道自服务-流量明细导出")
    @GetMapping("/corpFlowDetailExport")
    @OperationLog(operationName = "渠道自服务-流量明细导出", operationType = OperationTypeEnum.EXPORT)
    public Response<ExportVO> corpFlowDetailExport(@RequestParam String corpId,
                                                   @RequestParam(required = false) String country,
                                                   @RequestParam(required = false) String beginDate,
                                                   @RequestParam(required = false) String endDate) {
        return Response.ok(channelService.corpFlowDetailExport(country, corpId, beginDate, endDate));
    }

    @ApiOperation("资源流量明细查询")
    @GetMapping("/getResourceFlowDetail")
    public Response<List<Resourceflowdetail>> getResourceFlowDetail(@RequestParam int pageNum,
                                                                    @RequestParam int pageSize,
                                                                    @RequestParam String corpId,
                                                                    @RequestParam(required = false) String country,
                                                                    @RequestParam(required = false) String beginDate,
                                                                    @RequestParam(required = false) String endDate,
                                                                    @RequestParam(required = false) String month) {
        return Response.ok(channelService.getResourceFlowDetail(pageNum, pageSize, country, corpId, beginDate, endDate, month));
    }

    @ApiOperation("资源流量明细导出")
    @PostMapping("/exportResourceFlowDetail")
    public Response<ExportVO> exportResourceFlowDetail(@RequestParam String corpId,
                                                       @RequestParam String userId,
                                                       @RequestParam Boolean en,
                                                       @RequestParam(required = false) String country,
                                                       @RequestParam(required = false) String beginDate,
                                                       @RequestParam(required = false) String endDate,
                                                       @RequestParam(required = false) String month) {
        return Response.ok(channelService.exportResourceFlowDetail(country, en, corpId, userId, beginDate, endDate, month));
    }

    @ApiOperation("物联网平台流量明细文件入库")
    @PostMapping("/recordchannelflowDetail")
    public void recordchannelflowDetail(@RequestParam(required = false) String date) throws ParseException {
        channelService.recordchannelflowDetailV2(date, false);
    }

    @ApiOperation("物联网平台流量明细文件入库")
    @PostMapping("/recordchannelflowDetailGtp")
    public void recordchannelflowDetailGtp(@RequestParam(required = false) String date) throws ParseException {
        channelService.recordchannelflowDetailV2(date, true);
    }

//    @ApiOperation("物联网平台流量明细文件入库")
//    @PostMapping("/postbackRecordchannelflowDetail")
//    public void postbackRecordchannelflowDetail(@RequestParam(required = false) String date) throws ParseException {
//        channelService.recordPostbakcChannelflowDetail();
//    }

    @ApiOperation("a2z流量明细自结算入库")
    @PostMapping("/a2zOwnCharging")
    public Response<Void> a2zOwnCharging(@RequestBody List<ChannelFlowDetailDTO> flowdetails, @RequestParam Boolean isSupplement) throws ParseException {
        channelService.a2zOwnCharging(flowdetails, isSupplement);
        return Response.ok();
    }

    @ApiOperation("a2z流量是否结算入库")
    @PostMapping("/isCharging")
    public Response<Boolean> isCharging(@RequestBody IsChargingForm form) {
        return Response.ok(channelService.isCharging(form));
    }

    @ApiOperation("查询a2z需要重新结算入库的明细")
    @PostMapping("/getSupplementCard")
    public Response<List<CmsCorpFlowdetail>> getSupplementCard(@RequestBody IsChargingForm form) {
        return Response.ok(channelService.getSupplementCard(form));
    }

    @ApiOperation("a2z渠道商绑定的结算规则")
    @GetMapping("/corpChargingRule")
    public Response<List<ChannelA2zruleRelationVo>> corpChargingRule(@RequestParam(required = false) Long ruleId, @RequestParam(required = false) String corpId) {
        return Response.ok(channelService.corpChargingRule(ruleId, corpId));
    }

    @ApiOperation("资源合作模式渠道商绑定的结算规则")
    @GetMapping("/resCorpChargingRule")
    public Response<List<ChannelA2zruleRelationVo>> resCorpChargingRule(@RequestParam(required = false) Long ruleId, @RequestParam(required = false) String corpId) {
        return Response.ok(channelService.resCorpChargingRule(ruleId, corpId));
    }

    @PostMapping("/updateDeposit")
    public Response updateDeposit(@RequestBody List<ChargeVO> chargeVOS) {
        channelService.updateDeposit(chargeVOS);
        return Response.ok();
    }

    @PostMapping("/indemnityUpdateDeposit")
    public Response indemnityUpdateDeposit(@RequestBody ChargeVO chargeVO) {
        channelService.indemnityUpdateDeposit(chargeVO);
        return Response.ok();
    }

    /**
     * 获取渠道商列表 corpId、corpName
     *
     * @return
     */
    @ApiOperation("渠道商列表")
    @GetMapping("/getChannelList")
    public Response<List<Channel>> getChannelList() {
        try {
            return Response.ok(channelService.getChannelList());
        } catch (Exception ex) {
            log.error("渠道商列表获取失败", ex);
            return Response.error("渠道商列表获取失败");
        }
    }

    @GetMapping("/getChannelUpccTemplateRelation")
    public List<ChannelUpcctemplateRelation> getChannelUpccTemplateRelation(@RequestParam String templateId) {
        return channelService.getChannelUpcctemplateRelation(templateId);
    }

    @GetMapping("/getChannelCardsByFlowPoolId")
    public Response<List<ChannelCard>> getChannelCardsByFlowPoolId(@RequestParam String flowPoolId) {
        return Response.ok(channelCardService.getChannelCardsByFlowPoolId(flowPoolId));
    }

    @ApiOperation(value = "查询渠道商合作模式")
    @GetMapping("/getChannelCooperationMode")
    public Response<List<String>> getChannelCooperationMode(@RequestParam String corpId) {
        return Response.ok(channelService.getChannelCooperationMode(corpId));
    }

    @ApiOperation(value = "获取子渠道商")
    @GetMapping("/getLowerChannel")
    public Response<List<Channel>> getLowerChannel(@RequestParam String corpId,
                                                   @RequestParam(required = false) boolean selfContain) {
        return Response.ok(channelService.getLowerChannel(corpId, selfContain));
    }

    @ApiOperation(value = "获取销售邮箱")
    @GetMapping("/listBySalesMail")
    public Response<List<String>> listBySalesMail(@RequestParam String salesMail
    ) {
        return Response.ok(channelService.listBySalesMail(salesMail));
    }

    @ApiOperation(value = "判断是否是子渠道商")
    @GetMapping("/getIsSubChannel")
    public Response<Boolean> getIsSubChannel(@RequestParam String corpId) {
        return Response.ok(channelService.getIsSubChannel(corpId));
    }

    @ApiOperation("渠道新增自建套餐组")
    @PostMapping("/newSelfPackageGroup")
    public Response newSelfPackageGroup(@RequestBody NewSelfPackageGroupVO newSelfPackageGroupVO) {
        channelService.newSelfPackageGroup(newSelfPackageGroupVO);
        return Response.ok();
    }

    @PostMapping("/writeDetailRecord")
    public void writeDetailRecord(@RequestParam(required = false) String date,  @RequestParam String platform) {
        channelService.writeDetailRecord(date,platform);
    }


    @PostMapping("/getResourceFeeImsi")
    public Response<List<CmsAssignedImsiRecord>> getResourceFeeImsi(@RequestBody List<String> imsis) {
        return Response.ok(channelService.getResourceFeeImsi(imsis));
    }


    @GetMapping("getPeriod")
    public Response<List<ChannelVO>> getPeriod() {
        return Response.ok(channelService.getPeriod());
    }

    @GetMapping("getPeriodAuth")
    public Response<List<ChannelVO>> getPeriodAuth() {
        return Response.ok(channelService.getPeriodAuth());
    }

    @PostMapping("getChargingInfo")
    Response<ChargingInfo> getChargingInfo(@RequestBody ChargingInfoForm form) {
        return Response.ok(channelService.getChargingInfo(form));
    }

    @PostMapping("getAssignedImsiRecord")
    Response<List<CmsAssignedImsiRecord>> getAssignedImsiRecord(@RequestBody CmsAssignedImsiRecord form) {
        return Response.ok(channelService.getAssignedImsiRecord(form));
    }

    @PostMapping("/getChargingIdsByImsis")
    public Response getChargingIdsByIcids(@RequestBody Set<String> imsis) {
        return Response.ok(channelCardService.getChargingIdsByImsis(imsis));
    }

    @PostMapping("/getCorpNamesByImsiAmountId")
    public Response<Map<String, List<String>>> getCorpNamesByImsiAmountId(@RequestBody Set<String> ids) {
        return Response.ok(channelService.getCorpNamesByImsiAmountId(ids));
    }

    @PostMapping("/getChannelCardPoolMcc")
    @ApiOperation("渠道商支持国家卡池关联组查询")
    public Response<Map<String, String>> getChannelCardPoolMcc(@RequestParam String corpId) {
        return Response.ok(channelCardService.getChannelCardPoolMcc(corpId));
    }

    @PostMapping("/getParentChannel")
    @ApiOperation("渠道商支持国家卡池关联组查询")
    public Response<Channel> getParentChannel(@RequestParam String corpId) {
        return Response.ok(channelCardService.getParentChannel(corpId));
    }

    @GetMapping("/contractExpire")
    public Response contractExpire() {
        channelService.contractExpire();
        return Response.ok();
    }

    @GetMapping("/getChannelCooperationModeForLogin")
    public Response<List<String>> getChannelCooperationModeForLogin(@RequestParam("corpId") String corpId) {
        return Response.ok(channelService.getChannelCooperationModeForLogin(corpId));
    }

    @GetMapping("/getInfo4Order")
    public Response<BlankcardOrderConfirmVO> getInfo4Order(@RequestParam String corpId, @RequestParam String cooperationMode) {
        return Response.ok(channelService.getInfo4Order(corpId, cooperationMode));
    }

    @GetMapping("/getInfo4Order2")
    public Response<BlankcardOrderConfirmVO> getInfo4Order2(@RequestParam String corpId) {
        return Response.ok(channelService.getInfo4Order2(corpId, "3"));
    }

    @PostMapping("/channelAtzFlowExport")
    @ApiOperation("渠道ATZ/资源合作流量明细导出")
    public Response channelAtzFlowExport(@RequestParam String corpId,
                                         @RequestParam String beginDate,
                                         @RequestParam String endDate,
                                         @RequestParam String type,
                                         @RequestParam String billType,
                                         @RequestParam String userId,
                                         @RequestParam Long id) {
        return Response.ok(channelService.channelAtzFlowExport(corpId, beginDate, endDate, type, billType, userId, id));
    }

    @PostMapping("/channelAtzSummaryExport")
    @ApiOperation("渠道ATZ/资源合作流量汇总导出")
    public Response<ExportVO> channelAtzSummaryExport(@RequestParam String corpId,
                                                      @RequestParam String beginDate,
                                                      @RequestParam String endDate,
                                                      @RequestParam String type,
                                                      @RequestParam String billType,
                                                      @RequestParam String userId,
                                                      @RequestParam Long id) {
        return Response.ok(channelService.channelAtzSummaryExport(corpId, beginDate, endDate, type, billType, userId, id));
    }


    @PostMapping("/channelAtzSummary")
    @ApiOperation("渠道ATZ合作流量汇总")
    public Response<List<StatCorpFlowdetail>> channelAtzSummary(@RequestParam String corpId,
                                                                @RequestParam String beginDate,
                                                                @RequestParam String endDate) {
        return Response.ok(channelService.channelAtzSummary(corpId, beginDate, endDate));
    }

    @PostMapping("/getAccountPeriodId")
    @ApiOperation("获取渠道的账期id")
    public Response<List<ChannelAccountPeriodDTO>> getAccountPeriodId(@RequestBody List<String> corpIdList) {
        return Response.ok(channelService.getAccountPeriodId(corpIdList));
    }

    @PostMapping("/channelResourceSummary")
    @ApiOperation("渠道资源合作流量汇总")
    public Response<List<StatCorpFlowdetail>> channelResourceSummary(@RequestParam String corpId,
                                                                     @RequestParam String beginDate,
                                                                     @RequestParam String endDate) {
        return Response.ok(channelService.channelResourceSummary(corpId, beginDate, endDate));
    }


    @PostMapping("/getChannelDetails")
    @ApiOperation("获取渠道商是否支持自建套餐")
    public Response<Map<String, String>> getChannelDetails(@RequestBody Set<String> corpIds) {
        return Response.ok(channelService.getChannelDetails(corpIds));
    }

    @PostMapping("/getChannelSellData")
    @ApiOperation("获取渠道商是否支持自建套餐")
    public Response<List<ChannelSellDataDTO>> getChannelSellData(@RequestBody ChannelSellVO channelSellVO) {
        return Response.ok(channelService.getChannelSellDataForPage(channelSellVO));
    }

    @GetMapping("/getChannelSellData/todo")
    public Response<TodoChannelSellDataDTO> getTodoChannelSellData(@RequestParam String userId, @RequestParam String batchId,
                                                                   @RequestParam String todoNodeId, @RequestParam String procUniqueId) {
        return Response.ok(channelService.getTodoChannelSellData(userId, todoNodeId, procUniqueId, batchId));
    }

    @GetMapping("/getChannelSellData/finish")
    public Response finishTodoChannelSellData(@RequestParam String todoNodeId, @RequestParam String procUniqueId) {
        channelService.finishTodoChannelSellData(todoNodeId, procUniqueId);
        return Response.ok();
    }

    @GetMapping("/getCorpIdByName")
    @ApiOperation("获取渠道商是否支持自建套餐")
    public Response<List<String>> getCorpIdByName(@RequestParam String corpName) {
        return Response.ok(channelService.getCorpIdByName(corpName));
    }

    @GetMapping("/getChannelByEmail")
    @ApiOperation("获取销售绑定的渠道商")
    public Response<List<Channel>> getChannelByEmail(@RequestParam String userName) {
        return Response.ok(channelService.getChannelByEmail(userName));
    }

    //导出渠道商imsi费详情
    @PostMapping("/channelExport")
    @ApiOperation(value = "渠道商财务系统导出")
    public Response<List<ExportVO>> channelExport(@RequestBody ChannelExportDTO dto) {
        return Response.ok(channelService.channelExport(dto));
    }

    @PostMapping("/deposit/record")
    @ApiOperation(value = "押金充值分页查询")
    @NormalLog
    public Response<List<DepositRecordVO>> depositRecord(@RequestBody GetDepositVo getDepositVo, @RequestHeader(name = "user-info", required = false) String user) {
        IPage<DepositRecordVO> results = channelService.depositRecord(getDepositVo, JSON.parseObject(user, User.class));
        return Response.ok(results);
    }

    @PostMapping("/deposit/recordExport")
    @NormalLog
    public void depositRecord(@RequestBody GetDepositVo getDepositVo, @RequestHeader(name = "user-info", required = false) String user, HttpServletResponse response) {
        channelService.depositRecordExport(getDepositVo, JSON.parseObject(user, User.class), response);
    }


    @GetMapping("/getNotNeedVerifyCodeCropId")
    public Response<Boolean> getNotNeedVerifyCodeCropId(@RequestParam String corpId) {
        return Response.ok(channelService.getNotNeedVerifyCodeCropId(corpId));
    }


    @GetMapping("/getAccountManagement")
    public Response<List<ChannelDistributorDto>> getAccountManagement(@RequestParam String corpId, @RequestParam String cooperationMode) {
        return Response.ok(channelService.getAccountManagement(corpId, cooperationMode));
    }

    @PostMapping("/mktChannelPage")
    @ApiOperation(value = "分页查询渠道商信息")
    public Response<List<ChannelCorpPageVO>> getPage(@RequestBody @Valid MktChannelPageDTO mktChannelPageDTO) {
        PageResult<ChannelCorpPageVO> page = channelService.getPage(mktChannelPageDTO);
        return Response.ok(page.getRecords(), page.getTotalCount());
    }

    @PostMapping("/mktChannelList")
    @ApiOperation(value = "获取渠道商重复信息")
    public Response<List<ChannelCorpPageVO>> getRepeatList(@RequestBody MktChannelPageDTO mktChannelPageDTO) {
        return Response.ok(channelService.getRepeatList(mktChannelPageDTO));
    }


    @GetMapping("/getConsignmentDetails")
    public Response<List<ConsignmentDetailsDto>> getConsignmentDetails(@RequestParam String corpId) {
        return Response.ok(channelService.getConsignmentDetails(corpId));
    }

    @GetMapping("/getChannelA2zOperator")
    public Response<List<ChannelA2zOperatorDTO>> getChannelA2zOperator(@RequestParam() String corpId) {
        return Response.ok(channelService.getChannelA2zOperator(corpId));
    }

    @GetMapping("/updateChannelA2zOperatorByMcc")
    public Response updateChannelA2zOperatorByMcc(@RequestParam("mcc") String mcc,
                                                  @RequestParam("operatorIds") List<Long> operatorIds) {
        return channelService.updateChannelA2zOperatorByMcc(mcc, operatorIds);
    }

    @GetMapping("/deleteChannelA2zOperatorByMcc")
    public Response deleteChannelA2zOperatorByMcc(@RequestParam("mcc") String mcc) {
        channelService.deleteChannelA2zOperatorByMcc(mcc);
        return Response.ok();
    }
}
