package com.ebupt.cmi.clientmanagement.controller;


import com.ebupt.cmi.clientmanagement.domain.dto.ChannelPromiseDTO;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.service.ChannelPushService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/channelPush")
@AllArgsConstructor
public class ChannelPushController {
    private ChannelPushService channelPushService;

    @PostMapping("/channelPromiseSales")
    public void channelPromiseSales() {
        channelPushService.channelPromiseSales();
    }

    @GetMapping("/getChannelPromiseDTO")
    public Response<ChannelPromiseDTO> getChannelPromiseDTO(@RequestParam String corpId, @RequestParam String mode) {
        return Response.ok(channelPushService.getChannelPromiseDTO(corpId, mode));
    }

    @GetMapping("/finish")
    public Response finish(@RequestParam String procUniqueId, @RequestParam String todoNodeId) {
        channelPushService.finish(procUniqueId, todoNodeId);
        return Response.ok();
    }
}
