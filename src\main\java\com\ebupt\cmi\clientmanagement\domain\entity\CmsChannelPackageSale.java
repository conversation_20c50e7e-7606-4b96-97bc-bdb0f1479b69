package com.ebupt.cmi.clientmanagement.domain.entity;

import java.util.Date;
import java.io.Serializable;

/**
 * (CmsChannelPackageSale)实体类
 *
 * <AUTHOR>
 * @since 2024-06-19 18:18:47
 */
public class CmsChannelPackageSale implements Serializable {
    private static final long serialVersionUID = -41199003841363704L;

    private Long id;
/**
     * 看板id
     */
    private Long kanbanId;
/**
     * 售卖量
     */
    private Long statCount;
/**
     * 日期
     */
    private String statTime;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 修改时间
     */
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getKanbanId() {
        return kanbanId;
    }

    public void setKanbanId(Long kanbanId) {
        this.kanbanId = kanbanId;
    }

    public Long getStatCount() {
        return statCount;
    }

    public void setStatCount(Long statCount) {
        this.statCount = statCount;
    }

    public String getStatTime() {
        return statTime;
    }

    public void setStatTime(String statTime) {
        this.statTime = statTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}

