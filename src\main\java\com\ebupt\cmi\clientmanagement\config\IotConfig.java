package com.ebupt.cmi.clientmanagement.config;

import com.ebupt.cmi.clientmanagement.domain.vo.MailRemind;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/25 10:34
 */

@Component
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties("iot")
public class IotConfig extends SftpConfig{
    private Sftp sftp;

    private Mailremind mailRemind;

    private Export export;

    private Long batchDeleteSize;

    private String deleteDate;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Sftp extends SftpConfig.Sftp {
        private String remotePath;
        private String localstoragePath;
        private String remotePathExtra;
    }

    @Data
    public static class Export{
        private String resourceExportPath;
    }

    @Data
    public static class Mailremind {
        private Runoutofbalance runoutofbalance;

        private Prohibitivebuy prohibitivebuy;

        private Stopuse stopuse;

        @Data
        public static class Runoutofbalance {
            private String title;
            private String content;
        }

        @Data
        public static class Prohibitivebuy {
            private String title;
            private String content;
        }

        @Data
        public static class Stopuse {
            private String title;
            private String content;
        }
    }
}