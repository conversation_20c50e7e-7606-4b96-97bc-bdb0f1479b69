package com.ebupt.cmi.clientmanagement.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2022/01/17 10:00
 */

@Data
@Builder
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class IccidCardDTO {
    /**
     * 流量池id
     */
    private String flowPoolId;

    /**
     * 流量池名称
     */
    private String flowPoolName;
    /**
     * iccid
     */
    private String iccid;
    /**
     * 已使用流量，单位G
     */
    private String usedFlow;
    /**
     * 状态，默认值2（DB默认）1：已分配2：待分配
     */
    private String currentRateType = "1";
    /**
     * 单周期类型上限，单位GB
     */
    private String dailyTotal;
    /**
     * 总量，单位MB
     */
    private String total;
    /**
     * 控制逻辑（1：达量停用 2：达量限速 3：达量继续使用）
     */
    private String rateType;
    /**
     * 入池时间
     * 格式是YYYYMMDDHHMMSS
     */
    private String createTime;

    private Integer availableTime;

    private String flowPoolStatus;

}
