package com.ebupt.cmi.clientmanagement.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * (CmsChannelMarketingRebate)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04 14:21:52
 */
@Data
@Builder
public class CmsChannelMarketingRebate implements Serializable {
    private static final long serialVersionUID = 588358381298420234L;

    private Long id;
/**
     * 厂商id
     */
    private String corpId;
/**
     * 1:A2Z返利 2:代销返利
     */
    private String type;
/**
     * 创建时间默认值：CURRENT_TIMESTAMP
     */
    private Date createTime;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 营销返利金额（单位：分）
     */
    private BigDecimal remainAmount;
/**
     * 营销返利剩余金额（单位分）
     */
    private BigDecimal rebateAmount;
/**
     * 营销活动id
     */
    private Long activityId;
/**
     * 额度过期时间
     */
    private Date expiryTime;

    private Long version;

    @AllArgsConstructor
    @Getter
    public enum TypeEnum {
        /**
         * A~Z返利
         */
        A2Z("1"),
        /**
         * 代销返利
         */
        CONSIGNMENT("2");

        private final String type;
    }

}

