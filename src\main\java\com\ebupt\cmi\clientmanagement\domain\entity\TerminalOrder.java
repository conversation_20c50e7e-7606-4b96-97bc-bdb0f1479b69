package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * TerminalOrder
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/5/14 11:30
 */
@TableName("cms_terminal_order")
@Data
@ToString
@Builder
public class TerminalOrder {

    @TableId
    private String orderTerminalId;

    private String packageId;

    private String mcc;

    private String mnc;

    private String thirdOrderId;

    private Integer quantity;

}
