package com.ebupt.cmi.clientmanagement.domain.entity.hvshare;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PackageConsumeErrorLog.java
 * @Description HV流量共享消费者错误日志记录表 cms_package_consume_error_log
 * @createTime 2022年02月28日 16:47:00
 */

@TableName("cms_package_consume_error_log")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PackageConsumeErrorLog {
    Long id;

    String imsi;

    String iccid;

    String msisdn;

    String himsi;
    /**
     * 上网方式1：H，2：V
     */
    String internetType;
    /**
     * 套餐唯一ID
     */
    String packageUniqueId;
    /**
     * 队列名称
     */
    String queueName;

    /**
     * 错误简述
     */
    String errorDescribe;

    /**
     * 生成时间
     */
    LocalDateTime createTime;

}
