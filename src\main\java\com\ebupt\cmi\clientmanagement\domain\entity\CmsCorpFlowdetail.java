package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * cms_corp_flowdetail
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class CmsCorpFlowdetail implements Serializable {
    @TableId
    private Long id;

    /**
     * ebscode
     */
    private String ebsCode;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * Iccid
     */
    private String iccid;

    /**
     * 国家码
     */
    private String mcc;

    /**
     * 国家或地区
     */
    private String countryOrRegion;

    /**
     * 使用量（MB）
     */
    private BigDecimal usedTraffic;

    /**
     * 币种
     */
    private String currency;

    /**
     * 单价（GB）
     */
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 创建时间默认值：CURRENT_TIMESTAMP
     */
    private Date createTime;

    /**
     * 修改时间默认值：CURRENT_TIMESTAMP
     */
    private Date updateTime;

    private String dateBelongTo;

    private String operator;

    private String plmnlist;

    private String himsi;

    private String fileType;

    private BigDecimal rebateAmount;

    private Integer rebateRate;


    private static final long serialVersionUID = 1L;
}