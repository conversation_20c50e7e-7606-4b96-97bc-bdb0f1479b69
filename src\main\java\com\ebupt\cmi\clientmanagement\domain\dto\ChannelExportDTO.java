package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChannelExportDTO {
    @NotBlank
    private String corpId;

    @NotBlank
    private String beginMonth;

    @NotBlank
    private String endMonth;

    private String cooperationMode;

    private List<String> cooperationModes;

    private String corpName;

    private String currencyCode;

    private String billType;

    private Long id;

    private String statTime;

    private String dateStart;

    private String dateEnd;

    private String type;

    private String userId;


}
