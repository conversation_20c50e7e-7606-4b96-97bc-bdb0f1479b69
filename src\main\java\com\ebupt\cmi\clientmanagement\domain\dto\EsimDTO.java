package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2023/2/16 14:40
 */

@Data
public class EsimDTO {
    private Header header;

    private ProfileInfo profileInfo;

    @Data
    public static class Header {
        private FunctionExecutionStatus functionExecutionStatus;

        @Data
        public static class FunctionExecutionStatus {
            private String status;
            private StatusCodeData statusCodeData;

            @Data
            public static class StatusCodeData {
                private String subjectCode;
                private String reasonCode;
                private String message;
            }
        }
    }

    @Data
    public static class ProfileInfo {
        private String smdpAddress;
        private String matchingId;
        private String state;
        private String eid;
        private String firstDownloadDate;
        private String device;
        private String downloadCounter;
        private String updateDate;
        private String downloadUrl;

        public void setDownloadUrl() {
            downloadUrl = "LPA:1${smdpAddress}${matchingId}"
                    .replace("{smdpAddress}", smdpAddress)
                    .replace("{matchingId}", matchingId);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum Status {
        SUCCESS("Executed-Success"),

        FAIL("Failed");

        private String status;
    }
}
