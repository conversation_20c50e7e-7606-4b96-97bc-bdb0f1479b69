package com.ebupt.cmi.clientmanagement.domain.dto;

import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class SubsProductInfo {
    /**
     * 是否已经绑卡
     * 1.已绑定
     * 2.未绑定
     */
    private String bindState;
    /**
     * 已绑卡的CMLink号码/IICCID
     */
    private String cmLinkNumbers;
    /**
     * 省侧订单ID
     */
    private String provinceOrder;
    /**
     * CMI侧订单ID
     */
    private String externalId;
    /**
     * 套餐名称
     */
    private String productName;
    /**
     * 副卡套餐归属国家或方向
     */
    private String productAreaCode;
    /**
     * 套餐状态
     */
    private String productStutus;
    /**
     *
     * 套餐生效时间
     */
    private String absActiveTime;
    /**
     * 套餐失效时间
     */
    private String absInactiveTime;
    /**
     * 预留字段1
     */
    private String reservendFiled1;
    /**
     * 预留字段2
     */
    private String reservendFiled2;
    /**
     * 预留字段3
     */
    private String reservendFiled3;

    private String packageId;

    /**
     * 1：正在生效
     * 2：待生效
     * 3：已失效
     * *1: Taking effect
     *
     * *2: To be effective
     *
     * *3: Invalid
     */
    @Getter
    @AllArgsConstructor
    public enum StateEnum {

        TAKING_EFFECT("1","正在生效"),

        TO_BE_EFFECTIVE("2","待生效"),

        INVALID("3","已失效");

        private String code;

        private String msg;
    }
}
