package com.ebupt.cmi.clientmanagement.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ebupt.cmi.clientmanagement.domain.response.ResponseResult;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.Card;
import com.ebupt.cmi.clientmanagement.service.impl.SmsLuService;
import com.ebupt.cmi.clientmanagement.utils.BizConstants;
import com.ebupt.cmi.clientmanagement.utils.RedisUtil;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/28 14:45
 */

@RabbitListener(queues = "sms.welcomeMsgQueue")
@Component
@RequiredArgsConstructor
@Slf4j
public class WelcomeMsgConsumer {
    private final RedisUtil<?> redisUtil;

    private final SmsLuService smsLuService;

    private final PmsFeignClient pmsFeignClient;

//    @Value("${redis.keys.prefix.first-lu-sms:'first:lu:sms:'}")
//    private String firstSendSmsKeyPrefix;

    @RabbitHandler
    public void process(String cardInfo, Channel channel, Message message) throws IOException {
        Card card = JSONObject.parseObject(cardInfo, Card.class);
        String imsi = card.getImsi();
        try {
            if (StrUtil.isBlank(card.getSendSms())) {
                log.debug("[imsi={}]进行欢迎短信下发", imsi);
                String resultCode = smsLuService.synchronousSendNoticeSms(card.getMsisdn(), card.getTemplateId(), BizConstants.WELCOME_SCENE_ID,
                        card.getSendLang(), "", "", null);
                if (ResponseResult.SUCCESS.getCode().equals(resultCode)) {
                    pmsFeignClient.updateSmsSendStatus(imsi);
                }
            } else {
                log.debug("[imsi={}]已经下发过欢迎短信，不再下发", imsi);
            }
        } catch (Exception e) {
            log.error("imsi:{} 下发欢迎短信失败", card.getImsi(), e);
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
