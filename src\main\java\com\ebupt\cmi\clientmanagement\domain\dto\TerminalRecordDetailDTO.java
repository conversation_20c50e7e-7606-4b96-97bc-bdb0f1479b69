package com.ebupt.cmi.clientmanagement.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 终端厂商V卡使用详情DTO
 * @date 2021/5/8 16:58
 */
@Data
@ToString
@ApiModel
public class TerminalRecordDetailDTO {

    @ApiModelProperty(value = "使用日期")
    private String useDate;

    @ApiModelProperty(value = "VIMSI")
    private String vimsi;

    @ApiModelProperty(value = "计费模式")
    private String settleType;

    @ApiModelProperty(value = "套餐名称/流量方向")
    private String itemName;

    @ApiModelProperty(value = "数量")
    private String count;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "币种")
    private String currency;

}
