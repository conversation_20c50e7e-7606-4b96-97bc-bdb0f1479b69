package com.ebupt.cmi.clientmanagement.domain.dto.cooperation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EopAccessDetailDTO.java
 * @Description 能力接入DTO
 * @createTime 2021年05月07日 20:15:00
 */
@Data
public class EopAccessDetailDTO {

    @ApiModelProperty(value = "就是appKey")
    @NotEmpty(message = "appKey不能为空")
    private String appKey;

    @ApiModelProperty(value = "就是Url")
    @NotEmpty(message = "notifyUrl不能为空")
    private String notifyUrl;

    @ApiModelProperty(value = "就是Username")
    private String userName;

}
