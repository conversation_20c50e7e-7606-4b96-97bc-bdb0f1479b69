package com.ebupt.cmi.clientmanagement.config;

import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonLock;
import com.ebupt.cmi.clientmanagement.domain.entity.redis.RedissonProperties;
import org.redisson.Redisson;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RedissonAutoConfiguration.java
 * @Description 将配置文件注入bean
 * @createTime 2020年12月22日 18:02:00
 */

@Configuration
@ConditionalOnClass(Redisson.class)
@EnableConfigurationProperties(RedissonProperties.class)
public class RedissonAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean
    public RedissonLock redissonLock(RedissonProperties redissonProperties) {
        RedissonLock redissonLock = new RedissonLock(redissonProperties);
        return redissonLock;
    }
}
