package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 流量池覆盖国家表实体
 * @date 2021/4/20 16:05
 */
@EqualsAndHashCode(callSuper = true)
@TableName("cms_flowpool_country_relation")
@Data
@ToString
public class FlowPoolCountryRelation extends BaseEntity {

    private Long id;
    private String flowPoolId;

    private String mcc;

}
