package com.ebupt.cmi.clientmanagement.consumer.hvshare.service;


import com.ebupt.cmi.clientmanagement.feign.pms.domainV2.CardVO;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SmgService.java
 * @Description 短信下发Service类
 * @createTime 2021年07月12日 10:33:00
 */

public interface SmgService {

    /**
     * 发送通知短信
     *
     * @param phone      手机号
     * @param sceneId    场景id
     * @param templateId 模板id
     * @param packageUniqueId 套餐唯一id
     * @return
     */
    String sendNoticeSms(String phone, Long templateId, Integer sceneId, String sendLang, String packageUniqueId, String mcc);

    /**
     * 下发激活短信（用户确认激活触发的短信场景）
     * @param card 主卡信息
     * @param packageUniqueId 套餐唯一id
     * @param packageName 套餐名称
     * @param activeTime 套餐激活时间
     * @param expireTime 套餐到期时间
     */
    void sendActivatedSms(Card<PERSON> card, String packageUniqueId, String packageName,
                          Date activeTime, Date expireTime);
}
