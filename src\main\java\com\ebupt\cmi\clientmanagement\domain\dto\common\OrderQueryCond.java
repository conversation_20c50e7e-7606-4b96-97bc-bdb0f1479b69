package com.ebupt.cmi.clientmanagement.domain.dto.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/20 10:20
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderQueryCond {

    /**
     * 订单ID
     */
    private String orderID;

    /**
     * 订单状态
     * 1、“待发货”
     * 2、“已发货”
     * 3、“使用中”
     * 4、“部分退订”
     * 5、“已退订”
     */
    private Integer status;
}
