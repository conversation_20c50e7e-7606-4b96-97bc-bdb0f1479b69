package com.ebupt.cmi.clientmanagement.consumer.hvshare.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SingleDayDelayVO.java
 * @Description 单日延时恢复消费者message类
 * @createTime 2022年03月07日 11:15:00
 */

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SingleDayDelayVO extends BaseMessageVO {

    /**
     * 高速/低速（正常）签约模板ID
     */
    String signUpccId;
}
