package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * @Desc 套擦批量配置任务实体
 * <AUTHOR> lingsong
 * @Date 2021/6/15 14:47
 */

@TableName("cms_channel_batch_task")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ChannelBatchTask {

    @TableId
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 渠道ID
     */
    private String corpId;

    /**
     * 渠道名称
     */
    private String corpName;

    /**
     * 任务状态：1：处理中，2：完成，4：回滚中，5：已回滚
     */
    private String taskStatus;

    /**
     * 是否新加坡卡 1：是，0：否
     */
    private String isSingapore;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 原始文件路径
     */
    private String sourceFilePath;

    /**
     * 失败详情文件路径
     */
    private String failFilePath;

    /**
     * 成功详情文件路径
     */
    private String successFilePath;

    /**
     * 回滚详情文件路径
     */
    private String rollbackFilePath;


    /**
     * 账户ID
     */
    private Long operatorId;

    /**
     * 账户名
     */
    @TableField("username")
    private String username;

    /**
     * 任务结束时间
     */
    private LocalDateTime completeTime;

    private Integer sourceFileCount;
    private Integer failFileCount;
    private Integer successFileCount;

    private String remark;

}
