package com.ebupt.cmi.clientmanagement.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> sir
 * @Description TODO
 * @date 2021/6/18 16:07
 */
@Data
@ToString
@ApiModel
public class InactivePackageDTO {

    private String iccid;

    private String imsi;

    private String packageId;

    private String packageName;

    @ApiModelProperty(value = "购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String orderDate;

    @ApiModelProperty(value = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String effectiveDay;

    @ApiModelProperty(value = "币种类型")
    private String currencyCode;

    private BigDecimal amount;
}
