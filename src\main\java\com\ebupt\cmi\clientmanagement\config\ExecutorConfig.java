package com.ebupt.cmi.clientmanagement.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

@Slf4j
@Configuration
@EnableAsync
public class ExecutorConfig {

    @Autowired
    private ThreadPoolConfigProperties configProperties;

    @Value("${record-detail.max-thread-num}")
    private Integer maxThreadNum;

    @Bean
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(configProperties.getCoreSize());
        //配置最大线程数
        executor.setMaxPoolSize(configProperties.getMaxSize());
        //配置队列大小
        executor.setQueueCapacity(configProperties.getQueueCapacity());
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(configProperties.getNamePrefix());
        executor.setRejectedExecutionHandler(configProperties.getRejectPolicy().getValue());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor cdrFlowAndReachedProcessingTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(configProperties.getCoreSize());
        //配置最大线程数
        executor.setMaxPoolSize(configProperties.getMaxSize());
        //配置队列大小
        executor.setQueueCapacity(configProperties.getQueueCapacity());
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("cdr-flowAndReached-async-pool-");
        executor.setRejectedExecutionHandler(configProperties.getRejectPolicy().getValue());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor dirAppShiftingProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(configProperties.getCoreSize());
        //配置最大线程数
        executor.setMaxPoolSize(configProperties.getMaxSize());
        //配置队列大小
        executor.setQueueCapacity(configProperties.getQueueCapacity());
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("cdr-dirAppShifting-async-");
        executor.setRejectedExecutionHandler(configProperties.getRejectPolicy().getValue());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean("luExecutor")
    public Executor luExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(configProperties.getCoreSize());
        //配置最大线程数
        executor.setMaxPoolSize(configProperties.getMaxSize());
        //配置队列大小
        executor.setQueueCapacity(configProperties.getQueueCapacity());
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("lu-async-pool-");
        //配置拒绝策略为主线程自己接管
        executor.setRejectedExecutionHandler(configProperties.getRejectPolicy().getValue());

        executor.initialize();

        return executor;
    }

    @Bean
    public Executor upccExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(10);
        //配置最大线程数
        executor.setMaxPoolSize(20);
        //配置队列大小
        executor.setQueueCapacity(50);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("upcc-async-pool-");
        //配置拒绝策略为主线程自己接管
        executor.setRejectedExecutionHandler(configProperties.getRejectPolicy().getValue());

        executor.initialize();

        return executor;
    }

    @Bean
    public Executor flowRechargeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(1);
        //配置最大线程数
        executor.setMaxPoolSize(2);
        //配置队列大小
        executor.setQueueCapacity(10);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("flowRecharge-async-pool-");
        //配置拒绝策略为主线程自己接管
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        executor.initialize();

        return executor;
    }

    @Bean
    public Executor stockTransferExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(2);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("stockTransfer-async-pool-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        return executor;
    }

    @Bean
    public ThreadPoolExecutor upccQueryFlowExecutor() {
        return new ThreadPoolExecutor(
                4,
                4,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque(100),
                new ThreadPoolExecutor.AbortPolicy()
        );
    }

    @Bean
    public ThreadPoolExecutor blankCardIccidCheckExecutor() {
        return new ThreadPoolExecutor(
                4,
                4,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean
    public ThreadPoolExecutor blankCardIccidStroeOutExecutor() {
        return new ThreadPoolExecutor(
                4,
                4,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    // 主卡筛选没用了，我拿来给激活更新卡网元交互状态用

    /**
     * 优先核心线程，核心线程满了就放阻塞队列，阻塞队列满了就新建非核心线程。
     * 核心 > 队列 > 非核心线程
     *
     * @return
     */
    @Bean("hCardScanExecutor")
    public Executor hCardScanExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(5);
        //配置最大线程数
        executor.setMaxPoolSize(30);
        //配置队列大小
        executor.setQueueCapacity(20);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("updateCardStatus-");
        executor.setRejectedExecutionHandler(RejectPolicy.CALLER_RUNS.getValue());
        //执行初始化
        executor.initialize();
        return executor;
    }

    @Bean
    public ExecutorService recordExecutorService() {
        return new ThreadPoolExecutor(
                maxThreadNum,
                maxThreadNum + 1,
                10,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }
}