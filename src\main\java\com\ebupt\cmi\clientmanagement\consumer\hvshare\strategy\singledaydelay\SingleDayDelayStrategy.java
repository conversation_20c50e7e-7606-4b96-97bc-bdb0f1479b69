package com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.singledaydelay;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.BaseContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.context.delay.DelayContext;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.repository.HvShareRepository;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.strategy.AbstractOutsideNetStrategy;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.BaseMessageVO;
import com.ebupt.cmi.clientmanagement.consumer.hvshare.vo.SingleDayDelayVO;
import com.ebupt.cmi.clientmanagement.consumer.uitils.LuWarpper;
import com.ebupt.cmi.clientmanagement.consumer.vo.MockLuVO;
import com.ebupt.cmi.clientmanagement.domain.dto.HimsiStatusAndLocationDTO;
import com.ebupt.cmi.clientmanagement.domain.entity.*;
import com.ebupt.cmi.clientmanagement.domain.enums.RoleEnum;
import com.ebupt.cmi.clientmanagement.domain.response.Response;
import com.ebupt.cmi.clientmanagement.domain.vo.HimsiStatusAndLocationVO;
import com.ebupt.cmi.clientmanagement.feign.control.ControlFeignClient;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.LoadSubscribe;
import com.ebupt.cmi.clientmanagement.feign.control.domain.upcc.UnSubscribeServiceVO;
import com.ebupt.cmi.clientmanagement.feign.pms.PmsFeignClient;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.CardPool;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.HcardInfo;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.UpdateOpenStatusReq;
import com.ebupt.cmi.clientmanagement.feign.pms.domain.VcardInfo;
import com.ebupt.cmi.clientmanagement.mapper.ChannelCardMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsCardUpccRecordMapper;
import com.ebupt.cmi.clientmanagement.mapper.CmsPackageCardUpccRelationMapper;
import com.ebupt.cmi.clientmanagement.mapper.PackageDirectionRelationMapper;
import com.ebupt.cmi.clientmanagement.service.PackageEndService;
import com.ebupt.cmi.clientmanagement.service.ccrcommon.CCRCommonService;
import com.ebupt.cmi.clientmanagement.service.impl.FlowPoolServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SingleDayDelayStrategy.java
 * @Description 单日恢复延时队列消费
 * @createTime 2022年03月07日 10:54:00
 */
@Component("singleDayDelayStrategy")
@Slf4j
public class SingleDayDelayStrategy extends AbstractOutsideNetStrategy {

    @Autowired
    PackageEndService packageEndService;

    @Autowired
    HvShareRepository hvShareRepository;

    @Autowired
    LuWarpper luWarpper;

    @Autowired
    PmsFeignClient pmsFeignClient;

    @Autowired
    ControlFeignClient controlFeignClient;

    @Autowired
    CCRCommonService ccrCommonService;

    @Autowired
    FlowPoolServiceImpl flowPoolServiceImpl;

    @Autowired
    PackageDirectionRelationMapper packageDirectionRelationMapper;

    @Autowired
    CmsPackageCardUpccRelationMapper packageCardUpccRelationMapper;

    @Autowired
    CmsCardUpccRecordMapper cmsCardUpccRecordMapper;

    @Autowired
    protected ChannelCardMapper channelCardMapper;

    @Override
    protected <T extends BaseContext> boolean tryOutsideNet(T context) {

        try {

            log.info("===========================进入模拟LU流程==================================");

            DelayContext delayContext = (DelayContext) context;

            String packageUniqueIDFromTable = delayContext.getHcardInfo().getUpccSignPackageUniqueId();

            String packageUniqueIDFromMessage = delayContext.getMessageVO().getPackageUniqueId();

            //如果二者不相等，不用进行外部的交互
            if (!vertifyPackageUniqueID(packageUniqueIDFromMessage, packageUniqueIDFromTable)) {
                log.info("==========================该卡使用的不是传入的套餐，不做处理=============================");
                return true;
            }

            ChannelPackageCard channelPackageCard = delayContext.getChannelPackageCard();
            if (channelPackageCard != null && "2".equals(channelPackageCard.getPackageUseStatus())) {
                log.info("套餐使用状态为暂停，外部网元流程结束。");
                return true;
            }

            log.info("========================这是第{}次进行外部网元交互重试============================",
                    context.getRetryTimes());

            SingleDayDelayVO messageVO = (SingleDayDelayVO) delayContext.getMessageVO();

            //卡类型: 1:H  2:V，
            if ("1".equals(messageVO.getCardType())) {

                //判断是否是新卡
                Boolean isNewCard = ccrCommonService.DetermineNewAndOldCards(messageVO.getHimsi());
                if (isNewCard) {
                    log.info("==================检测到是H新卡，判断是否触发rar====================");
                    ChannelCard channelCard = channelCardMapper.selectOne(Wrappers.lambdaQuery(ChannelCard.class)
                            .eq(ChannelCard::getIccid, messageVO.getIccid()));

                    if (ccrCommonService.judgePgwSessionExists(channelCard.getImsi())) {
                        log.info("==================触发H卡RAR====================");
                        controlFeignClient.sendRAR(channelCard.getImsi());
                    } else {
                        log.info("==================H卡会话不存在不下发H卡RAR====================");
                    }

                } else {
                    log.info("==================检测到是H旧卡，模拟LU上报流程====================");

                    HimsiStatusAndLocationVO andLocationVO =
                            HimsiStatusAndLocationVO.builder()
                                    .imsi(messageVO.getHimsi())
                                    .role(RoleEnum.PHONENUMBER.getRole())
                                    .build();

                    HimsiStatusAndLocationDTO himsiStatusAndLocationDTO =
                            packageEndService.getUserLocation(andLocationVO);

                    delayContext.setHimsiStatusAndLocationDTO(himsiStatusAndLocationDTO);

                    MockLuVO mockLuVO = getMockLuVO(delayContext);

                    luWarpper.mockLu(mockLuVO);
                }

            } else {

                log.info("==================检测到是V卡，不需要模拟LU上报流程，直接进行Upcc签约====================");

                VcardInfo vcardInfo = Response.getAndCheckRemoteData(pmsFeignClient
                        .getVcardAccountInfo(messageVO.getImsi()));

                CardPool cardPool = Response.getAndCheckRemoteData(pmsFeignClient
                        .getCardPoolByImsi(messageVO.getImsi()));

//                if (ccrCommonService.judgePgwSessionExists(vcardInfo.getImsi())) {
//                    log.info("==================触发V卡RAR====================");
//                    controlFeignClient.sendRAR(vcardInfo.getImsi());
//                }

                //is_sign_upcc 是否去UPCC动态签约;1 true|0 false,注意该字段可能为空
                if (StringUtils.hasLength(cardPool.getIsSignUpcc()) &&
                        "1".equals(cardPool.getIsSignUpcc())) {


                    // 如果二者相等，不用进行外部的交互
                    log.info("message sign id:{}, table sign id:{} (如果两个sign id一致将不进行签约)",
                            messageVO.getSignUpccId(), vcardInfo.getUpccSignBizId());
                    if (!StringUtils.hasText(messageVO.getSignUpccId()) || !messageVO.getSignUpccId().equals(vcardInfo.getUpccSignBizId())) {
                        log.info("==========================该卡现签约Id不一致，需做处理=============================");

                        LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                                .usrIdentifier(vcardInfo.getMsisdn())
                                .srvName(messageVO.getSignUpccId())
                                .build();
                        log.info("签约：{}", JSON.toJSONString(loadSubscribe));
                        Response.getAndCheckRemoteData(controlFeignClient
                                .subscribeService(loadSubscribe));

                        Response.getAndCheckRemoteData(pmsFeignClient.
                                updateCardUpccSignBizId("2", vcardInfo.getImsi(), messageVO.getSignUpccId()));
                    }


                    List<CmsPackageCardUpccRelation> directionalAppPacks = delayContext.getDirectionalAppPacks();

                    //cms_channel_package_card.surf_status = 2-套餐状态限速
                    if (Optional.ofNullable(channelPackageCard)
                            .map(ChannelPackageCard::getSurfStatus)
                            .map(m -> "2".equals(m) && !CollectionUtils.isEmpty(directionalAppPacks))
                            .orElse(false)) {

                        for (CmsPackageCardUpccRelation appPack : directionalAppPacks) {

                            String signId = appPack.getUpccSignId();
                            log.info("AppInfo: appId[{}], signId[{}]", appPack.getAppId(), signId);

                            CmsCardUpccRecord cmsCardUpccRecord = cmsCardUpccRecordMapper.selectOne(new LambdaQueryWrapper<CmsCardUpccRecord>()
                                    .eq(CmsCardUpccRecord::getAppId, appPack.getAppId())
                                    .eq(CmsCardUpccRecord::getImsi, vcardInfo.getImsi()));

                            cmsCardUpccRecord = Optional.ofNullable(cmsCardUpccRecord)
                                    .orElse(CmsCardUpccRecord.builder()
                                            .appId(appPack.getAppId())
                                            .imsi(vcardInfo.getImsi())
                                            .updateTime(new Date())
                                            .createTime(new Date())
                                            .build());
                            log.info("CmsCardUpccRecord: appId[{}], signId[{}], table signId[{}]", appPack.getAppId(), signId, cmsCardUpccRecord.getUpccSignBizId());

                            if (!signId.equals(cmsCardUpccRecord.getUpccSignBizId())) {
                                //不一致 需签约

                                LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                                        .usrIdentifier(vcardInfo.getMsisdn())
                                        .srvName(signId)
                                        .build();
                                log.info("签约：{}", loadSubscribe);
                                Response.getAndCheckRemoteData(controlFeignClient
                                        .subscribeService(loadSubscribe));

                                cmsCardUpccRecord.setUpccSignBizId(signId);

                                if (!ObjectUtils.isEmpty(cmsCardUpccRecord.getId())) {
                                    log.info("cms_card_upcc_record有值， 更新数据");
                                    cmsCardUpccRecordMapper.updateById(cmsCardUpccRecord);
                                } else {
                                    log.info("cms_card_upcc_record无值，插入");
                                    cmsCardUpccRecordMapper.insert(cmsCardUpccRecord);
                                }

                            } else {
                                log.info("signId一致,无需操作");
                            }

                        }

                    }

                }
            }


            log.info("=======================非常幸运，通过了，单日恢复外部网元交互流程===========================");

            return true;

        } catch (Exception ex) {

            log.error("调用外部网元时发生致命错误，位置：单日恢复外部网元交互");

            log.error("", ex);

            return false;

        }

    }

    void loadSubscribe(String msisdn, String oldSignId, String newSignId) {
        log.info("Go to upcc to sign: msisdn[{}],old sign id[{}],new sign id[{}]. Two template IDs cannot be the same",
                msisdn, oldSignId, newSignId);
        if (!StringUtils.hasLength(oldSignId) || !StringUtils.hasLength(newSignId) || oldSignId.equals(newSignId)) {
            log.error("Parameter error, no action taken");
            return;
        }

        UnSubscribeServiceVO unSubscribe = UnSubscribeServiceVO.builder()
                .usrIdentifier(msisdn)
                .srvName(oldSignId)
                .build();
        Response.getAndCheckRemoteData(controlFeignClient.unSubscribeUpccService(unSubscribe));


        LoadSubscribe loadSubscribe = LoadSubscribe.builder()
                .usrIdentifier(msisdn)
                .srvName(newSignId)
                .build();
        Response.getAndCheckRemoteData(controlFeignClient
                .subscribeService(loadSubscribe));


    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000)
    @Transactional(rollbackFor = Exception.class)
    public <T extends BaseContext> void handle(T context) {

        log.info("======================进入单日恢复流程,imsi：{}=========================", context.getMessageVO().getImsi());

        DelayContext delayContext = (DelayContext) context;

        setHcardIntoContext(context);

        String packageUniqueIDFromTable = delayContext.getHcardInfo().getUpccSignPackageUniqueId();

        String packageUniqueIDFromMessage = delayContext.getMessageVO().getPackageUniqueId();


        ChannelPackageCard channelPackageCard = hvShareRepository.getChannelPackageCardByPackageUniqueId(packageUniqueIDFromMessage);

        delayContext.setChannelPackageCard(channelPackageCard);

        //上网状态
        // 1：正常
        // 2：限速
        hvShareRepository.updatePackageSurfLogStatus(packageUniqueIDFromMessage, PackageSurfStatusLog.SurfStatusEnum.NORMAL.getStatus());

        hvShareRepository.refreshChannelPackageCardSurfStatus(packageUniqueIDFromMessage, ChannelPackageCard.SurfStatusEnum.NORMAL.getValue(), true);

        hvShareRepository.refreshChannelPackageNoticeLevelToNull(packageUniqueIDFromMessage);


        if ("2".equals(channelPackageCard.getPackageUseStatus())) {
            log.info("套餐使用状态为暂停，流程结束。");
            return;
        }

        //如果二者相等，才进行业务处理
        if (vertifyPackageUniqueID(packageUniqueIDFromMessage, packageUniqueIDFromTable)) {

            connectWithPms(delayContext);

            setDirectionalAppPack(delayContext, channelPackageCard.getPackageId(), packageUniqueIDFromMessage);

        } else {
            log.info("==========================该卡使用的不是传入的套餐，不做处理=============================");
        }

    }

    void setDirectionalAppPack(DelayContext delayContext, String packageId, String packageUniqueId) {
        List<PackageDirectionRelation> packageDirectionRelations = packageDirectionRelationMapper.selectList(new LambdaQueryWrapper<PackageDirectionRelation>()
                .eq(PackageDirectionRelation::getPackageId, packageId)
                .eq(PackageDirectionRelation::getPackageUniqueId, packageUniqueId)
                .eq(PackageDirectionRelation::getDirectType, "2")
                .eq(PackageDirectionRelation::getIsUsePackage, "1")
                .eq(PackageDirectionRelation::getHasUsed, "3"));
        Set<String> appGroupIds = packageDirectionRelations.stream().map(PackageDirectionRelation::getAppGroupId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(appGroupIds)) {
            List<CmsPackageCardUpccRelation> cmsPackageCardUpccRelations = packageCardUpccRelationMapper.getLowUpccByGroupIds(appGroupIds,packageUniqueId);
            log.info("应用信息：{}", JSON.toJSONString(cmsPackageCardUpccRelations));
            delayContext.setDirectionalAppPacks(cmsPackageCardUpccRelations);
        }
    }

    MockLuVO getMockLuVO(DelayContext context) {

        SingleDayDelayVO messageVO = (SingleDayDelayVO) context.getMessageVO();

        HcardInfo hcardInfo = context.getHcardInfo();

        HimsiStatusAndLocationDTO himsiStatusAndLocationDTO = context.getHimsiStatusAndLocationDTO();

        return MockLuVO.builder()
                .imsi(messageVO.getImsi())
                .activeType("1")
                .cardForm(hcardInfo.getCardForm())
                .himsi(messageVO.getHimsi())
                .iccid(hcardInfo.getIccid())
                .msisdn(hcardInfo.getMsisdn())
                .packageUniqId(messageVO.getPackageUniqueId())
                .mcc(himsiStatusAndLocationDTO.getMobileCountryCode())
                .cardType(messageVO.getCardType())
                .build();

    }

    /**
     * 加一层，好看点
     *
     * @param context 上下文
     */
    public void connectWithPms(DelayContext context) {

        BaseMessageVO messageVO = context
                .getMessageVO();

        //upcc开户状态
        //1：成功
        //2:失败

        //upcc签约状态
        //1:：成功
        //2:失败
        UpdateOpenStatusReq updateOpenStatusReq =
                UpdateOpenStatusReq
                        .builder()
                        .imsi(messageVO.getImsi())
                        .upccSignStatus("2")
                        .upccSignBizId("")
                        .build();


        //卡类型: 1:H  2:V，
        // 若为h卡，调用pms服务修改H卡upcc开户状态、签约状态为“失败”
        if ("1".equals(messageVO.getCardType())) {

            //为了让LU激活流程去走重新签约流程
            updateOpenStatusReq.setUpccSignId("");

            Response.getAndCheckRemoteData(pmsFeignClient.updateCardOpenStatus(updateOpenStatusReq));

        }

    }
}
