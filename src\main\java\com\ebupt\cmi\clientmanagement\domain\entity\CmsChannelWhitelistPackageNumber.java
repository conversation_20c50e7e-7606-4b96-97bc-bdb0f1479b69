package com.ebupt.cmi.clientmanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 渠道商白名单号段-套餐表
 * @date 2023-03-13
 */
@Data
@TableName("cms_channel_whitelist_package_number")
@ApiModel(value = "渠道商白名单号段-套餐表")
public class CmsChannelWhitelistPackageNumber implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    private Long id;

    @ApiModelProperty(value = "渠道商id")
    private String corpId;

    @ApiModelProperty(value = "套餐id")
    private String packageId;

    @ApiModelProperty(value = "起始号段")
    private String beginIccid;

    @ApiModelProperty(value = "结束号段")
    private String endIccid;

    @ApiModelProperty(value = "任务创建时间")
    private Date createTime;

    @ApiModelProperty(value = "数据更新时间")
    private Date updateTime;
} 