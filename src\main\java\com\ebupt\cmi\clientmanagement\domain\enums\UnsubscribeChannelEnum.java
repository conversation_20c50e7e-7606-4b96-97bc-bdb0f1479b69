package com.ebupt.cmi.clientmanagement.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc 订单退订渠道枚举
 * @<PERSON> <PERSON><PERSON> l<PERSON>ong
 * @Date 2021/4/16 15:57
 */
@Getter
@AllArgsConstructor
public enum UnsubscribeChannelEnum {

    ROLLBACK("205", "回滚"),

    CUSTOMER_WEBSITE("206", "客服网站"),

    RECYCLING_RESOURCES("207", "终端回收资源"),

    API("208", "外部Api");

    private String k;

    private String val;
}
