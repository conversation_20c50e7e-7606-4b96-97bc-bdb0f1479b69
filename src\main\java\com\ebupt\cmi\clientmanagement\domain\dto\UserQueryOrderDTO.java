package com.ebupt.cmi.clientmanagement.domain.dto;

import com.ebupt.cmi.clientmanagement.domain.dto.common.Logistic;
import com.ebupt.cmi.clientmanagement.domain.dto.common.OrderItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/26 16:24
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserQueryOrderDTO {

    private String orderID;

    private String createUserID;

    /**
     * 订单类型
     * 1：卡
     * 2：套餐
     * 3：卡+套餐
     * 7：加油包
     */
    private String orderType;

    /**
     * 订单状态枚举值:
     * 1、待发货
     * 2、已发货
     * 3、部分退订
     * 4、全额退订
     * 作为入参时，无效
     * 作为响应时，查到则必填
     */
    private Integer status;

    private List<OrderItem> orderItems;

    private Long totalAmount;

    private Integer currency;

    /**
     * 订单渠道ID
     * 作为响应时，查到则必填
     * （订购渠道的标识，在全球卡项目中，给每个渠道采用appKey来标记）
     * 个人用户无值，渠道用户返回appKey
     */
    private String channelID;

    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private Date createTime;

    private String comments;

    private Address address;

    private Logistic logistic;
}
