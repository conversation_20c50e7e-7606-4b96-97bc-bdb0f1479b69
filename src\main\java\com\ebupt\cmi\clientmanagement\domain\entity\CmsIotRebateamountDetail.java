package com.ebupt.cmi.clientmanagement.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * cms_iot_rebateamount_detail
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsIotRebateamountDetail implements Serializable {
    private Long id;

    /**
     * 物联网话单回滚详情表id
     */
    private Long relationId;

    /**
     * 营销活动id
     */
    private Long rebateId;

    /**
     * 花费营销活动金额
     */
    private BigDecimal rebateAmount;

    private static final long serialVersionUID = 1L;
}